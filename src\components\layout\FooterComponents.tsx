import { FC } from "react";
import { motion } from "framer-motion";

interface FooterLinkProps {
  href: string;
  children: React.ReactNode;
}

export const FooterLink: FC<FooterLinkProps> = ({ href, children }) => {
  return (
    <motion.a
      href={href}
      className="text-white hover:text-blue-400 transition-colors duration-300 text-lg"
      whileHover={{ x: 5 }}
      transition={{ type: "spring", stiffness: 400, damping: 15 }}
    >
      {children}
    </motion.a>
  );
};

interface SocialButtonProps {
  href: string;
  icon: React.ReactNode;
  label: string;
  color: string;
}

export const SocialButton: FC<SocialButtonProps> = ({ href, icon, label, color }) => {
  return (
    <motion.a
      href={href}
      target="_blank"
      rel="noopener noreferrer"
      className={`w-10 h-10 rounded-lg ${color} flex items-center justify-center shadow-lg`}
      whileHover={{ scale: 1.1, y: -2 }}
      whileTap={{ scale: 0.95 }}
      transition={{ duration: 0.2 }}
      aria-label={label}
    >
      {icon}
    </motion.a>
  );
};
