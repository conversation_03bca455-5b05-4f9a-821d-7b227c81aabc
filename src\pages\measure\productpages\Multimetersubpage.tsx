import React, { useState } from 'react';
// import PdfViewer from "@/components/ui/pdf-viewer";

const TRMSMultimetersPage = () => {
  const [activeTab, setActiveTab] = useState('overview');

  const tabs = [
    { id: 'overview', label: 'Overview' },
    { id: 'trms-digital', label: 'TRMS Digital Multimeters' },
    { id: 'digital-astiv', label: 'Digital Multimeters - ASTIV Range' },
    { id: 'voltage-tester', label: 'Voltage Tester' },
  ];

  // Product data based on the screenshots
  const products = [
    {
      id: 'mtx-203',
      title: 'TRMS AC DIGITAL MULTIMETERS',
      color: 'blue',
      model: 'Models - MTX 203',
      imageSrc: '/images/multimeter-blue.png',
      features: [
        'TRMS AC current and voltage measurements and all the ranges programmed automatically for simpler use',
        'NCV (no-contact voltage) detection for work in total safety',
        'Convenient: backlit screen and built-in torch',
        'Ergonomic: fit in one hand',
        'Practical: thanks to its shockproof sheath with storage slots for leads which is also magnetized for easy mounting on electrical cabinets'
      ],
      description: `The patented Multifix mounting system is ideal for use with these multimeters, allowing you to hook it onto a cabinet door or your belt or suspend it... The two-position stand ensures easy reading however the multimeter is positioned. Readings are made even clearer by the blue backlighting of the display (4,000 or 6,000 counts depending on the model). The built-in torch means the multimeter can be used even in the dark.

      The rotary switch offers one function per position. On the front panel, 3 keys are all you need to access all the various functions. The 600 V CAT III, IP54 double-well input terminals are easily accessible.
      
      Electrical maintenance operations are optimized thanks to the VLowZ low-impedance voltage measurement function. It is also very easy to carry out initial troubleshooting on PCBs by measuring the resistance, capacitance, diode, etc.
      
      In addition to the traditional measurements, the MTX200 models can measure temperature via a K thermocouple contact sensor delivered as standard. This means users can carry out:
      • electrical maintenance
      • initial troubleshooting on PCBs
      • verification of radiator control, etc.`
    },
    {
      id: 'ca-5273',
      title: 'TRMS AC DC DIGITAL MULTIMETERS',
      color: 'yellow',
      model: 'Models - C.A 5273, C.A 5275, C.A 5277',
      imageSrc: '/images/multimeter-yellow.png',
      features: [
        'CAT IV 600V / CAT III 1,000V',
        'TRMS measurements',
        'Bi-mode double 6,000-count backlit display and 61+2-segment bargraph (full scale / central zero)',
        'Auto AC/DC selection ,Automatic or manual range selection',
        'VLowZ low-impedance voltage measurement with low-pass filter',
        '1,000 V, 10 A',
        'Resistance / audible continuity ,Temperature,Capacitance',
        'Max / Min storage',
        '3-year warranty'
      ],
      description: `The C.A 5273 is a comprehensive multimeter for electrical maintenance of installations and small AC and DC machines, with a double 6,000-count backlit display and a 61+2-segment bargraph with remanent effect.

      Its 600V CAT IV safety is backed by IP54 ingress protection.
      
      Designed and manufactured in France by CHAUVIN ARNOUX, this multimeter comes with a 3-year warranty.`
    },
    {
      id: 'atest-m-nano1',
      title: 'TRMS AC DC DIGITAL MULTIMETERS',
      color: 'orange-case',
      model: 'Models - ATEST M nano1',
      imageSrc: '/images/multimeter-nano.png',
      features: [
        '600V AC/V/DC',
        'RMS measurement',
        '4000 count large scale digital display',
        'Auto ranging and manual Selection',
        'Relative Mode on Capacitance',
        'Auto Power off',
        'Data Hold',
        'Shock proof from 4 feet drops',
        'Compact design on small size',
        'Integral Safety test leads',
        'Deluxe Carrying wallet included',
        'Safety standard CAT II 600V'
      ],
      specifications: [
        { parameter: 'DC Voltage', range: '400.0mV ~ 600V', accuracy: '0.5' },
        { parameter: 'AC Voltage', range: '400.0mV ~ 600V', accuracy: '1' },
        { parameter: 'Resistance', range: '400.0Ω ~ 40.00MΩ', accuracy: '0.5' },
        { parameter: 'Capacitance', range: '50nF ~ 100µF', accuracy: '2.5' },
        { parameter: 'Frequency Counter', range: '5Hz ~ 5MHz', accuracy: '0.1' },
        { parameter: 'Duty Cycle', range: '0.1% ~ 99.9%', accuracy: '0.5' },
        { parameter: 'Diode Test', range: 'Open circuit Voltage: 1.5V', accuracy: '' }
      ]
    },
    {
      id: 'atest-m-2340',
      title: 'TRMS AC DC DIGITAL MULTIMETERS',
      color: 'orange',
      model: 'Models - ATEST M 2340',
      imageSrc: '/images/multimeter-orange-2340.png',
      features: [
        '1000V AC/V/DCV',
        'True RMS reading on AC and AC+DC mode',
        '100,000/10,000 count extra large digital display, 45 segments analog bar graph',
        'Graphic Dual display',
        'High Frequency Rejection (HFR)',
        'Min/Max/Avg',
        'Relative % function',
        'Auto Hold/Peak Hold',
        'Store/Recall memories',
        'dBm/dB measurement',
        '20,000 records data logging capacity',
        'USB interface with Software included',
        'Safety standard CAT IV 600V'
      ],
      specifications: [
        { parameter: 'DC Voltage', range: '40mV to 1000V', accuracy: '0.015' },
        { parameter: 'ACV & VAC+DCV', range: '40mV to 1000V', accuracy: '0.7' },
        { parameter: 'DC Current', range: '400μA to 10A', accuracy: '0.2' },
        { parameter: 'ACA & AC+DCA', range: '400μA to 10A', accuracy: '0.8' },
        { parameter: 'Resistance', range: '1000Ω to 40MΩ', accuracy: '0.025' },
        { parameter: 'Capacitance', range: '4nF to 40mF', accuracy: '0.8' }
      ]
    },
    {
      id: 'atest-m-2342',
      title: 'TRMS AC DC DIGITAL MULTIMETERS',
      color: 'orange',
      model: 'Models - ATEST M 2342',
      imageSrc: '/images/multimeter-orange-2342.png',
      features: [
        '1000V AC/V/DCV',
        'True RMS reading on AC and AC+DC mode',
        '40,000/4000 count extra large digital display, 43 segments analog bar graph',
        'Auto Backlit Dual display',
        'Navigator key drive',
        'High Frequency Rejection (HFR)',
        'Relative % function',
        'Store/Recall memories',
        'dBm/dB measurement',
        '20,000 records data logging capacity',
        'USB interface with Software included',
        'Safety standard CAT IV 600V'
      ],
      specifications: [
        { parameter: 'DC Voltage', range: '40mV to 1000V', accuracy: '0.03' },
        { parameter: 'ACV & VAC+DCV', range: '40mV to 1000V', accuracy: '0.7' },
        { parameter: 'DC Current', range: '400μA to 10A', accuracy: '0.2' },
        { parameter: 'ACA & AC+DCA', range: '400μA to 10A', accuracy: '0.8' },
        { parameter: 'Resistance', range: '400Ω to 40MΩ', accuracy: '0.2' },
        { parameter: 'Capacitance', range: '4nF to 40mF', accuracy: '0.9' },
        { parameter: 'Frequency Counter', range: '40Hz to 40MHz', accuracy: '0.002' },
        { parameter: 'Temperature', range: '-40°C ~ 400°C', accuracy: '1' }
      ]
    },
    {
      id: 'atest-mh-2502',
      title: 'TRMS AC DC DIGITAL MULTIMETERS',
      color: 'red',
      model: 'Models - ATEST MH 2502',
      imageSrc: '/images/multimeter-red.png',
      features: [
        '1000V AC/V/DCV',
        'True RMS measurements on AC/V&CA',
        '(AC+DC)V and (AC+DC)mV measurements',
        '(AC+DCA) and (AC+DC)mA measurements',
        '6000 count digital and 62 segments analog display',
        'LoZ for prevent false reading from ghost Voltage',
        'Non-Contact Voltage detection',
        '11A/1000V & 400mA/1000V High Energy Fuses',
        'Shock proof from 4 feet drops',
        'Safety standard CAT IV 600V'
      ],
      specifications: [
        { parameter: 'DC Voltage', range: '60mV, 1000V', accuracy: '0.08' },
        { parameter: 'AC Voltage', range: '60mV, 1000V', accuracy: '0.8' },
        { parameter: 'Auto V-LoZ', range: '600V, 1000V', accuracy: '0.8' },
        { parameter: 'DC Current', range: '60mA, 10A', accuracy: '0.8' },
        { parameter: 'AC Current', range: '60mA, 10A', accuracy: '1.2' },
        { parameter: 'Resistance', range: '600Ω~40MΩ', accuracy: '0.8' },
        { parameter: 'Capacitance', range: '1μF~10mF', accuracy: '1.2' },
        { parameter: 'Frequency Counter', range: '100Hz ~ 100KHz', accuracy: '0.1' },
        { parameter: 'Temperature', range: '-40°C ~ 400°C', accuracy: '1' }
      ]
    },
    {
      id: 'atest-m-3550',
      title: 'TRMS AC DC DIGITAL MULTIMETERS',
      color: 'gray',
      model: 'Models - ATEST M 3550',
      imageSrc: '/images/multimeter-gray.png',
      features: [
        '600V AC/V/DCV',
        'True RMS on ACV',
        '4000 count digital large display',
        'Auto ranging',
        'Data Hold for relative peak readings',
        'Relative function',
        'Non-Contact Voltage detection',
        'Continuity with buzzer and display',
        'Battery capacity indication in segments',
        'Pocket size smaller Size design',
        'Optional Holster with probe holder, IR card and magnetic strap',
        'Safety standard CAT II 600V'
      ],
      specifications: [
        { parameter: 'DC Voltage', range: '4V, 40V, 400V', accuracy: '0.5' },
        { parameter: 'AC Voltage', range: '4V, 40V, 400V', accuracy: '1' },
        { parameter: 'Diode', range: '2.000V', accuracy: 'NA' },
        { parameter: 'Resistance', range: '400Ω~40MΩ', accuracy: '0.5' },
        { parameter: 'Capacitance', range: '50μF ~100μF', accuracy: '2' },
        { parameter: 'Frequency Counter', range: '100Hz~400Hz', accuracy: '0.1' },
        { parameter: 'Diode Test', range: '1.500V', accuracy: '1' },
        { parameter: 'Continuity Buzzer', range: '<50Ω w/Tone Sound', accuracy: '' }
      ]
    },
    {
      id: 'atest-m-3552',
      title: 'TRMS AC DC DIGITAL MULTIMETERS',
      color: 'gray',
      model: 'Models - ATEST M 3552',
      imageSrc: '/images/multimeter-gray.png',
      features: [
        '600V AC/V/DCV',
        'True RMS on ACV and DCA',
        '4000 count digital large display',
        'Auto ranging',
        'Data Hold for relative peak readings',
        'Relative function',
        'Non-Contact Voltage detection',
        'Continuity with buzzer and display',
        'DC µA function',
        'High Energy Fuse',
        'Pocket size smaller Size design',
        'Safety standard CAT II 600V'
      ],
      specifications: [
        { parameter: 'DC Voltage', range: '4V, 40V, 400V', accuracy: '0.5' },
        { parameter: 'AC Voltage', range: '4V, 40V, 400V', accuracy: '1' },
        { parameter: 'DC µA', range: '400μA', accuracy: '0.5' },
        { parameter: 'ACA, DCA', range: '4A, 10A', accuracy: '1.5' },
        { parameter: 'DC µA', range: '400μA, 4000μA', accuracy: '0.5' },
        { parameter: 'Resistance', range: '400Ω~40MΩ', accuracy: '0.5' },
        { parameter: 'Capacitance', range: '50μF ~100μF', accuracy: '2' },
        { parameter: 'Frequency Counter', range: '10Hz~1MHz', accuracy: '0.1' },
        { parameter: 'Diode Test', range: '1.500V', accuracy: '1' },
        { parameter: 'Continuity Buzzer', range: '<50Ω W/Tone Sound', accuracy: '' }
      ]
    },
    {
      id: 'atest-m-3554',
      title: 'TRMS AC DC DIGITAL MULTIMETERS',
      color: 'gray',
      model: 'Models - ATEST M 3554',
      imageSrc: '/images/multimeter-gray.png',
      features: [
        '600V AC/V/DCV',
        'True RMS on ACV and DCA',
        '4000 count digital large display',
        'Auto ranging',
        'Data Hold for relative peak readings',
        'Relative function',
        'Non-Contact Voltage detection',
        'Continuity with buzzer and display',
        'DC µA function',
        'High Energy Fuse',
        'Pocket size smaller Size design',
        'Safety standard CAT II 600V'
      ],
      specifications: [
        { parameter: 'DC Voltage', range: '4V, 40V, 400V', accuracy: '0.5' },
        { parameter: 'AC Voltage', range: '4V, 40V, 400V', accuracy: '1' },
        { parameter: 'DC Current', range: '400μA', accuracy: '0.5' },
        { parameter: 'ACA, DCA', range: '4A, 10A', accuracy: '1.5' },
        { parameter: 'DC µA', range: '400μA, 4000μA', accuracy: '0.5' },
        { parameter: 'Resistance', range: '400Ω~40MΩ', accuracy: '0.5' },
        { parameter: 'Capacitance', range: '50μF ~100μF', accuracy: '2' },
        { parameter: 'Frequency Counter', range: '10Hz~1MHz', accuracy: '0.1' },
        { parameter: 'Temperature', range: '-40°C ~ 400°C', accuracy: '1' },
        { parameter: 'Diode Test', range: '1.500V', accuracy: '1' },
        { parameter: 'Continuity Buzzer', range: '<50Ω W/Tone Sound', accuracy: '' }
      ]
    },
    {
      id: 'atest-m-2200',
      title: 'TRMS AC DC DIGITAL MULTIMETERS',
      color: 'red',
      model: 'Models - ATEST M 2200',
      imageSrc: '/images/multimeter-red-2200.png',
      features: [
        '1000V AC/V/DCV',
        'True RMS measurements on ACV',
        '6000 count digital and 60 segments analog display',
        'LoZ to prevent false reading from ghost voltage',
        'Data Hold for relative peak readings',
        'Relative function',
        'Non-Contact Voltage detection',
        'Double with built-in magnetic holder',
        'Continuity with buzzer and display',
        'Battery capacity indication in segments',
        'Safety standard CAT IV 600V'
      ],
      specifications: [
        { parameter: 'DC Voltage', range: '600mV~1000V', accuracy: '0.5' },
        { parameter: 'AC Voltage', range: '600mV~1000V', accuracy: '1' },
        { parameter: 'Auto V-LoZ', range: '600mV~1000V', accuracy: '2' },
        { parameter: 'Resistance', range: '600Ω~60MΩ', accuracy: '0.9' },
        { parameter: 'Capacitance', range: '1.000μF~10.00μF', accuracy: '1.9' },
        { parameter: 'Frequency Counter', range: '10Hz ~ 100Hz', accuracy: '0.1' },
        { parameter: 'Diode Test', range: '1.500V', accuracy: '' }
      ]
    },
    {
      id: 'atest-m-2202',
      title: 'TRMS AC DC DIGITAL MULTIMETERS',
      color: 'red',
      model: 'Models - ATEST M 2202',
      imageSrc: '/images/multimeter-red-2200.png',
      features: [
        '1000V AC/V/DCV',
        'True RMS measurements on ACV',
        '6000 count digital and 60 segments analog display',
        'µA measurement on both AC and DC',
        'LoZ to prevent false reading from ghost voltage',
        'Data Hold for relative peak readings',
        'Relative function',
        'Non-Contact Voltage detection',
        'Double with built-in magnetic holder',
        'Continuity with buzzer and display',
        'Battery capacity indication in segments',
        'Safety standard CAT IV 600V'
      ],
      specifications: [
        { parameter: 'DC Voltage', range: '600mV~1000V', accuracy: '0.5' },
        { parameter: 'AC Voltage', range: '600mV~1000V', accuracy: '1' },
        { parameter: 'Auto V-LoZ', range: '600mV~1000V', accuracy: '2' },
        { parameter: 'DC µA', range: '600μA', accuracy: '1' },
        { parameter: 'AC µA', range: '600μA', accuracy: '1.5' },
        { parameter: 'Resistance', range: '600Ω~60MΩ', accuracy: '0.5' },
        { parameter: 'Capacitance', range: '1μF ~100μF', accuracy: '1.9' },
        { parameter: 'Frequency Counter', range: '10Hz~10MHz', accuracy: '0.1' },
        { parameter: 'Temperature', range: '-40°C ~400°C', accuracy: '1' },
        { parameter: 'Diode Test', range: '1.500V', accuracy: '1' },
        { parameter: 'Continuity Buzzer', range: '<50Ω W/To Tone Sound', accuracy: '' }
      ]
    }
  ];

  return (
    <div className="bg-white min-h-screen">
      {/* Header Navigation */}
      <div className="bg-gray-100 p-4 border-b">
        <div className="container mx-auto flex items-center">
          {tabs.map(tab => (
            <button
              key={tab.id}
              className={`px-4 py-2 mr-2 ${activeTab === tab.id ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
              onClick={() => setActiveTab(tab.id)}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      <div className="container mx-auto p-4">
        {products.map((product, index) => (
          <div key={product.id} className="mb-16 border-b pb-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="col-span-1">
                <img src={product.imageSrc} alt={product.model} className="max-w-full h-auto" />
              </div>

              <div className="col-span-2">
                <h2 className="text-4xl font-bold mb-6">
                  <span className="text-gray-700">TRMS AC DC </span>
                  <span className="text-yellow-500">DIGITAL MULTIMETERS</span>
                </h2>

                <div className="mb-6">
                  <p className="text-lg font-medium mb-2">{product.model}</p>
                  <ul className="space-y-2">
                    {product.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start">
                        <span className="text-yellow-500 mr-2">✓</span>
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="flex space-x-4 mt-8">
                  <button className="bg-yellow-500 text-white px-6 py-2 rounded hover:bg-yellow-600">
                    ENQUIRE
                  </button>
                  <button className="border border-yellow-500 text-yellow-500 px-6 py-2 rounded hover:bg-yellow-50">
                    BROCHURE
                  </button>
                </div>
              </div>
            </div>

            {product.description && (
              <div className="mt-8">
                <p className="text-gray-700 whitespace-pre-line">{product.description}</p>
              </div>
            )}

            {product.specifications && (
              <div className="mt-8">
                <h3 className="text-xl font-bold mb-4">Specifications</h3>
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-yellow-500 text-white">
                      <th className="p-2 text-left">PARAMETER</th>
                      <th className="p-2 text-left">RANGE</th>
                      <th className="p-2 text-left">ACCURACY %</th>
                    </tr>
                  </thead>
                  <tbody>
                    {product.specifications.map((spec, idx) => (
                      <tr key={idx} className={idx % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                        <td className="p-2 border">{spec.parameter}</td>
                        <td className="p-2 border">{spec.range}</td>
                        <td className="p-2 border">{spec.accuracy}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Footer */}
      <div className="bg-gray-800 text-white p-6">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">About Us</h3>
              <p>Leading provider of professional electrical measurement solutions</p>
            </div>
            <div>
              <h3 className="text-xl font-bold mb-4">Contact</h3>
              <p>Email: <EMAIL></p>
              <p>Phone: +****************</p>
            </div>
            <div>
              <h3 className="text-xl font-bold mb-4">Support</h3>
              <p>Documentation</p>
              <p>Product Registration</p>
              <p>Warranty Information</p>
            </div>
            <div>
              <h3 className="text-xl font-bold mb-4">Social</h3>
              <div className="flex space-x-4">
                <a href="#" className="hover:text-yellow-500">Facebook</a>
                <a href="#" className="hover:text-yellow-500">Twitter</a>
                <a href="#" className="hover:text-yellow-500">LinkedIn</a>
              </div>
            </div>
          </div>
          <div className="mt-8 text-center">
            <p>© 2025 TRMS Measuring Instruments. All rights reserved.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TRMSMultimetersPage;