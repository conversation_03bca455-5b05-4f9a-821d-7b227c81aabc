import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import Layout from "@/components/layout/Layout";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";

interface ProductSpec {
  label: string;
  description: string;
}

interface Product {
  id: string;
  name: string;
  title: string;
  subtitle?: string;
  model: string;
  image: string;
  description?: string;
  specs: ProductSpec[];
  hasRange?: boolean;
  rangeData?: {
    parameters: string[];
    ranges: string[];
    accuracies: string[];
  };
}

const GasMeasurementProductPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('co2-meter');
  
  const products: Product[] = [
    {
      id: 'co2-meter',
      name: 'MULTIFUNCTION CO2 METER',
      title: 'MULTIFUNCTION CO2',
      subtitle: 'METER,TEMPERATURE, HUMIDITY METER',
      model: 'C.A 1510',
      image: '/images/co2-meter.png',
      description: 'In indoor environments, dense occupation and insufficient air renewal lead to high CO2 concentrations. The level of CO2 is therefore an excellent indicator of air quality, alongside pollution analysis performed by inspection organizations and laboratories, as well as showing the effectiveness of the air renewal systems for environmental engineering professionals.',
      specs: [
        { label: 'CO2', description: '0 to 5,000 ppm' },
        { label: 'Accuracy', description: '±(50 ppm + 3% of the value measured)' },
        { label: 'Temperature', description: '-10 °C to +60 °C/Accuracy: ±0.5 °C' },
        { label: 'Humidity', description: '5 to 95 %RH/Accuracy: ±2%RH' },
        { label: 'Recording', description: '1 million values stored on the product in the form of several measurement campaigns' },
        { label: 'USB interface', description: 'product recognized as a USB key) or Bluetooth for wireless use' },
        { label: 'Software', description: 'supplied as standard and 1510 application available with Android' },
        { label: 'Locking', description: 'of the instrument possible during recording (display & keyboard)' },
        { label: 'Mounting', description: 'Numerous mounting possibilities (integrated magnet, wall support with padlock insert, desktop stand or suspended)' }
      ]
    },
    {
      id: 'co-meter',
      name: 'CO METER',
      title: 'CO METER',
      model: 'ATTEST EG 800',
      image: '/images/co-meter.png',
      specs: [
        { label: 'LCD', description: 'Large LCD dual displays for CO and TEMP measurement' },
        { label: 'Backlight', description: 'for easy screen viewing under dim lighting conditions' },
        { label: 'Alarm', description: 'Adjustable alarm limits' },
        { label: 'CO level', description: 'beeper can be silenced if desired' },
        { label: 'Zero point', description: 'Automatic Zero point adjustment' },
        { label: 'Functions', description: 'Max / Data Hold / Auto Power off' },
        { label: 'Battery', description: 'Easy to replace battery' },
        { label: 'Sensor', description: 'Replaceable CO sensor extends tool life' }
      ],
      hasRange: true,
      rangeData: {
        parameters: ['CO', 'Temperature °C', 'Temperature °F', 'Sensor'],
        ranges: ['0 to 1000 ppm', '-20 to 70°C', '-4 to 158°F', 'Stabilized electrochemical CO specific sensor'],
        accuracies: ['5', '0.8', '1.5', '']
      }
    },
    {
      id: 'gas-detector',
      name: 'GAS LEAKAGE DETECTOR',
      title: 'GAS LEAKAGE DETECTOR',
      model: 'ATTEST EG 820',
      image: '/images/gas-detector.png',
      specs: [
        { label: 'Mixture', description: '5% Hydrogen(H2) + 95% Nitrogen(N)' },
        { label: 'Sensitivity', description: 'Less than 5 ppm - 2g/year' },
        { label: 'Sensor life', description: 'approx. 1 year' },
        { label: 'Warm up time', description: '45 seconds' },
        { label: 'DC brushless fan', description: '' },
        { label: 'Sensitivity selector', description: 'High-medium-Low leak sensitivity selector' },
        { label: 'Probe', description: '15.5 (40 CM) flexible stainless probe' },
        { label: 'Reset', description: 'Ambient concentration reset' },
        { label: 'Compensation', description: 'Automatic zero and background compensation' }
      ]
    }
  ];
  
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };
  
  const activeProduct = products.find(product => product.id === activeTab);
  
  return (
    <Layout>
      <div className="pt-24 md:pt-32">
        {/* Product Tabs */}
        <div className="bg-white border-b">
          <div className="container mx-auto px-4">
            <div className="flex overflow-x-auto py-4 space-x-6">
              {products.map(product => (
                <button
                  key={product.id}
                  className={`whitespace-nowrap px-4 py-2 font-medium rounded-md transition-colors ${
                    activeTab === product.id 
                      ? 'bg-yellow-500 text-white' 
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => handleTabChange(product.id)}
                >
                  {product.name}
                </button>
              ))}
            </div>
          </div>
        </div>
        
        {/* Main Content */}
        {activeProduct && (
          <main className="flex-grow container mx-auto px-4 py-8">
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              {/* Product Hero Section */}
              <div className="relative">
                <div className={`absolute inset-0 ${activeProduct.id === 'co2-meter' ? 'bg-gray-100' : activeProduct.id === 'co-meter' ? 'bg-yellow-50' : 'bg-gray-100'}`}></div>
                <div className="relative flex flex-col md:flex-row p-6 md:p-12 items-center">
                  <div className="w-full md:w-1/2 flex justify-center mb-8 md:mb-0">
                    <div className="bg-white p-4 rounded-lg shadow-md transform hover:scale-105 transition-transform duration-300">
                      <img 
                        src={activeProduct.image} 
                        alt={activeProduct.name} 
                        className="max-h-80 object-contain"
                      />
                    </div>
                  </div>
                  <div className="w-full md:w-1/2 md:pl-12">
                    <h1 className="text-4xl font-bold text-gray-700 mb-2">
                      {activeProduct.title}
                    </h1>
                    {activeProduct.subtitle && (
                      <h2 className="text-4xl font-bold mb-6">
                        <span className="text-gray-700">{activeProduct.subtitle.split(',')[0]},</span>
                        <span className="text-yellow-500">{activeProduct.subtitle.split(',')[1]}</span>
                      </h2>
                    )}
                    <p className="text-lg font-medium mb-4">Model: {activeProduct.model}</p>
                    {activeProduct.description && (
                      <p className="text-gray-600 mb-6">
                        {activeProduct.description}
                      </p>
                    )}
                    <div className="flex space-x-4">
                      <Button className="px-6 py-2 bg-yellow-500 text-white hover:bg-yellow-600">
                        ENQUIRE
                      </Button>
                      <Button variant="outline" className="px-6 py-2 border-2 border-yellow-500 text-yellow-500 hover:bg-yellow-50">
                        BROCHURE
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Product Specifications */}
              <div className="p-6 md:p-12 border-t border-gray-200">
                <h3 className="text-2xl font-bold text-gray-700 mb-8">Specifications</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {activeProduct.specs.map((spec, index) => (
                    <div key={index} className="flex items-start space-x-4">
                      <div className="flex-shrink-0 w-6 h-6 rounded-full bg-yellow-500 flex items-center justify-center mt-1">
                        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-bold text-gray-700">{spec.label}</h4>
                        <p className="text-gray-600">{spec.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
                
                {/* Range Table for CO Meter */}
                {activeProduct.hasRange && activeProduct.rangeData && (
                  <div className="mt-12">
                    <h3 className="text-2xl font-bold text-gray-700 mb-6">Measurement Ranges</h3>
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr>
                            <th className="px-6 py-3 bg-yellow-500 text-white font-bold text-left">PARAMETER</th>
                            <th className="px-6 py-3 bg-yellow-500 text-white font-bold text-left">RANGE</th>
                            <th className="px-6 py-3 bg-yellow-500 text-white font-bold text-left">ACCURACY %</th>
                          </tr>
                        </thead>
                        <tbody>
                          {activeProduct.rangeData.parameters.map((param, index) => (
                            <tr key={index} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                              <td className="px-6 py-4 border border-gray-200">{param}</td>
                              <td className="px-6 py-4 border border-gray-200">{activeProduct.rangeData?.ranges[index]}</td>
                              <td className="px-6 py-4 border border-gray-200">{activeProduct.rangeData?.accuracies[index]}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Features Section */}
              <div className="p-6 md:p-12 bg-gray-50 border-t border-gray-200">
                <h3 className="text-2xl font-bold text-gray-700 mb-8">Key Features</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                      </svg>
                    </div>
                    <h4 className="text-lg font-bold text-gray-700 mb-2">High Accuracy</h4>
                    <p className="text-gray-600">Precise measurements for professional environmental monitoring.</p>
                  </div>
                  
                  <div className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                      </svg>
                    </div>
                    <h4 className="text-lg font-bold text-gray-700 mb-2">User-Friendly Design</h4>
                    <p className="text-gray-600">Intuitive interfaces with clear displays for easy operation.</p>
                  </div>
                  
                  <div className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                      </svg>
                    </div>
                    <h4 className="text-lg font-bold text-gray-700 mb-2">Data Management</h4>
                    <p className="text-gray-600">Advanced storage and connectivity options for comprehensive data analysis.</p>
                  </div>
                </div>
              </div>
              
              {/* Call to Action */}
              <div className="p-6 md:p-12 bg-gray-800 text-white text-center">
                <h3 className="text-2xl font-bold mb-4">Need Professional Gas Measurement Solutions?</h3>
                <p className="text-gray-300 mb-6 max-w-2xl mx-auto">Contact our team of experts to find the right equipment for your specific environmental monitoring needs.</p>
                <Button asChild className="px-8 py-3 bg-yellow-500 text-white hover:bg-yellow-600">
                  <Link to="/contact">REQUEST A QUOTE</Link>
                </Button>
              </div>
            </div>
          </main>
        )}
      </div>
    </Layout>
  );
};

export default GasMeasurementProductPage;