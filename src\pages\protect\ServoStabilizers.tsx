import React, { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";

import PageLayout from "@/components/layout/PageLayout";
import { Button } from "@/components/ui/button";
import {
  Zap,
  Shield,
  Plus,
  PlusCircle,
  Globe,
  Clock,
  ShieldCheck,
  Headphones,
  HelpCircle,
  BarChart3,

  ArrowRight,
  CheckCircle2,
  FileText,
  Mail
} from "lucide-react";



// Watermark Component
const Watermark = () => {
  return (
    <div className="fixed inset-0 pointer-events-none z-0 flex items-center justify-center overflow-hidden">
      <div className="absolute w-full h-full">
        <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <pattern id="watermark" patternUnits="userSpaceOnUse" width="600" height="600">
              <text
                x="300"
                y="300"
                fontSize="60"
                fontWeight="bold"
                fill="rgba(59, 130, 246, 0.03)"
                textAnchor="middle"
                dominantBaseline="middle"
                transform="rotate(-45, 300, 300)"
              >
                SERVO STABILIZER
              </text>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#watermark)" />
        </svg>
      </div>
    </div>
  );
};

// CountUp Animation Component
const CountUp = ({
  to,
  from = 0,
  duration = 2,
  delay = 0,
  className = "",
}) => {
  const ref = useRef(null);
  const [isInView, setIsInView] = useState(false);
  const [count, setCount] = useState(from);
  const currentRef = useRef(null);
  useEffect(() => {
    const element = ref.current;
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (element) {
      observer.observe(element);
    }

    return () => {
      if (element) {
        observer.unobserve(element);
      }
    };
  }, []);

  useEffect(() => {
    let frame;
    let startTime;

    if (isInView) {
      const animate = (timestamp) => {
        if (!startTime) startTime = timestamp;
        const progress = Math.min((timestamp - startTime) / (duration * 1000), 1);

        setCount(Math.floor(from + (to - from) * progress));

        if (progress < 1) {
          frame = requestAnimationFrame(animate);
        }
      };

      setTimeout(() => {
        frame = requestAnimationFrame(animate);
      }, delay * 1000);
    }

    return () => {
      if (frame) {
        cancelAnimationFrame(frame);
      }
    };
  }, [isInView, from, to, duration, delay]);

  return <span ref={ref} className={className}>{count}</span>;
};

// Feature data
const features = [
  {
    title: "True RMS Correction",
    icon: <Zap className="h-6 w-6" />,
    description: "Advanced measurement and correction technology",
    items: [
      {
        icon: <Zap className="h-5 w-5" />,
        title: "Microprocessor",
        description: "Based control system for measurement and correction",
      },
      {
        icon: <BarChart3 className="h-5 w-5" />,
        title: "Digital Voltage, Current",
        description: "& Frequency display",
      },
    ],
  },
  {
    title: "Comprehensive Protection",
    icon: <Shield className="h-6 w-6" />,
    description: "Multiple layers of protection for your equipment",
    items: [
      {
        icon: <Shield className="h-5 w-5" />,
        title: "Standard Protection",
        description:
          "Optional LOW / HIGH / Low voltage & frequency, Overload, Single Phasing, High / ± Voltage, Short circuit & built-in protection against Transverse mode Spikes",
      },
      {
        icon: <Zap className="h-5 w-5" />,
        title: "Electronic Output Overload",
        description:
          "Trip uses a unique current sensing circuit - SCPT/OC to detect-and-trip based Overload protection",
      },
    ],
  },
  {
    title: "Optional Protection",
    icon: <PlusCircle className="h-6 w-6" />,
    description: "Additional protection features for specialized needs",
    items: [
      {
        icon: <Plus className="h-5 w-5" />,
        title: "Optional Protection",
        description:
          "Features: Offline 3-in-1 High Voltage surge Phase Neutral Spike Filter, ELCB / RCCB, Type 2 Surge Suppressor",
      },
    ],
  },
  {
    title: "After Sales Support",
    icon: <Headphones className="h-6 w-6" />,
    description: "Comprehensive service network and guarantees",
    items: [
      {
        icon: <HelpCircle className="h-5 w-5" />,
        title: "No Questions",
        description: "Asked guarantee",
      },
      {
        icon: <Globe className="h-5 w-5" />,
        title: "Wide",
        description: "Service network",
      },
      {
        icon: <Clock className="h-5 w-5" />,
        title: "Service Response Within",
        description:
          "8 hours in Service towns and within 24 hours in the same State",
      },
      {
        icon: <ShieldCheck className="h-5 w-5" />,
        title: "Comprehensive AMC",
        description:
          "Regardless of the age of the Stab'lizer",
      },
    ],
  },
];

const applications = [
  { text: "Industrial Machinery", icon: "🏭" },
  { text: "Medical Equipment", icon: "🏥" },
  { text: "IT & Data Centers", icon: "🖥️" },
  { text: "Commercial Buildings", icon: "🏢" },
  { text: "Residential Complexes", icon: "🏘️" }
];

const productTypes = [
  {
    title: "1 Phase Stabilizers",
    description: "Perfect for homes and small businesses. Protects sensitive electronics from voltage fluctuations.",
    features: [
      "Digital control for precise regulation",
      "Fast response time (<40ms)",
      "High efficiency design",
      "Compact and noise-free operation",
      "Comprehensive surge protection",
      "Wide input voltage window"
    ],    accentColor: "from-blue-400 to-cyan-600",
    textColor: "text-blue-600",
    bgColor: "bg-gradient-to-br from-blue-50 to-cyan-50",
    imageUrl: "/Servo_stabilizers/SS_12_-_1-removebg-preview.png",
    altText: "1 Phase Home Servo Stabilizer"
  },
  {
    title: "3 Phase Stabilizers",
    description: "Ideal for industrial applications and heavy machinery. Ensures consistent power across all phases.",
    features: [
      "Microprocessor controlled digital design",
      "High-speed correction",
      "Three independent control circuits",
      "LCD display for all parameters",
      "Wide input voltage range",
      "High overload capacity"
    ],
    accentColor: "from-blue-500 to-indigo-600",
    textColor: "text-blue-700",
    bgColor: "bg-gradient-to-br from-blue-50 to-indigo-50",
    imageUrl: "/Servo_stabilizers/SS_28_-1-removebg-preview.png",
    altText: "3 Phase Industrial Servo Stabilizer"
  },
  {
    title: "Custom Designed Stabilizers",
    description: "Tailor-made solutions for specific requirements. Contact our experts for a customized solution.",
    features: [
      "Designed to your exact specifications",
      "Special voltage configurations",
      "Advanced monitoring and remote operation",
      "Integrated with building management systems",
      "High-precision regulation (±1%)",
      "Extended environmental operating ranges"
    ],
    accentColor: "from-cyan-500 to-blue-600",
    textColor: "text-cyan-700",
    bgColor: "bg-gradient-to-br from-cyan-50 to-blue-50",
    imageUrl: "/Servo_stabilizers/Oil_4_-removebg-preview.png",
    altText: "Custom Designed Servo Stabilizer"
  }
];

const specifications = [
  {
    category: "Input",
    items: [
      { label: "Voltage Range", value: "170-280V AC" },
      { label: "Frequency", value: "47-53 Hz" },
      { label: "Phase Options", value: "1φ and 3φ" }
    ]
  },
  {
    category: "Output",
    items: [
      { label: "Regulation", value: "±1%" },
      { label: "Correction Rate", value: "<20ms" },
      { label: "Efficiency", value: ">98%" }
    ]
  },
  {
    category: "Protection",
    items: [
      { label: "Overload Trip", value: "110% to 150%" },
      { label: "Surge Protection", value: "Class III" },
      { label: "EMI/RFI Filter", value: "Optional" }
    ]
  }
];

const statsData = [
  {
    number: 400000,
    suffix: "+",
    text: "Installations",
    icon: <CheckCircle2 className="h-8 w-8" />,
    color1: "#3b82f6",
    color2: "#2563eb",
    description: "Worldwide since 1985"
  },
  {
    number: 30,
    suffix: "+",
    text: "Years Experience",
    icon: <Clock className="h-8 w-8" />,
    color1: "#0ea5e9",
    color2: "#0284c7",
    description: "Industry leadership"
  },
  {
    number: 99,
    suffix: "%",
    text: "Reliability",
    icon: <Shield className="h-8 w-8" />,
    color1: "#2563eb",
    color2: "#1e40af",
    description: "Proven performance"
  },
  {
    number: 24,
    suffix: "",
    text: "Hour Support",
    icon: <Headphones className="h-8 w-8" />,
    color1: "#3b82f6",
    color2: "#2563eb",
    description: "Expert service network"
  }
];

// StatsCard Component
const StatsCard = ({ number, suffix = "", text, icon, color1, color2, description, index = 0 }) => (
  <motion.div
    initial={{ opacity: 0, y: 30 }}
    whileInView={{ opacity: 1, y: 0 }}
    viewport={{ once: true }}
    transition={{ duration: 0.6, delay: index * 0.1 }}
    className="relative group"
  >
    <div className="absolute -inset-3 bg-gradient-to-r from-blue-500/5 to-indigo-500/5 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

    <div className="bg-white dark:bg-slate-800 rounded-2xl p-8 shadow-xl relative overflow-hidden backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 h-full flex flex-col justify-between transform group-hover:translate-y-[-8px] transition-transform duration-500"
      style={{
        boxShadow: "0 20px 40px rgba(59, 130, 246, 0.08), 0 10px 20px rgba(59, 130, 246, 0.06)"
      }}
    >
      {/* Colored gradient blob in corner */}
      <div className="absolute -top-20 -right-20 w-40 h-40 rounded-full"
        style={{
          background: `radial-gradient(circle, ${color1}20, transparent 70%)`,
          filter: "blur(25px)"
        }}>
      </div>

      <div className="absolute -bottom-20 -left-20 w-40 h-40 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700"
        style={{
          background: `radial-gradient(circle, ${color2}20, transparent 70%)`,
          filter: "blur(25px)"
        }}>
      </div>

      <div className="text-4xl mb-6"
        style={{
          color: color1
        }}>
        {icon}
      </div>

      <div>
        <div className="text-5xl font-bold mb-2 bg-clip-text text-transparent relative z-10"
          style={{
            backgroundImage: `linear-gradient(135deg, ${color1}, ${color2})`
          }}>
          <CountUp
            to={number}
            from={0}
            duration={2.5}
            delay={0.5 + index * 0.2}
            className="inline-block"
          />
          <span>{suffix}</span>
        </div>
        <div className="text-xl font-semibold text-slate-700 dark:text-slate-200 mb-3">{text}</div>
        <div className="text-sm text-slate-500 dark:text-slate-400">{description}</div>
      </div>
    </div>
  </motion.div>
);



const ServoStabilizers = () => {
  // Simple refs for section navigation
  const heroRef = useRef(null);
  const featureRef = useRef(null);

  // Function to open brochure PDF
  const openBrochure = () => {
    // URL to your PDF file
    const pdfUrl = "/stabilizers.pdf";

    // Open PDF directly in a new tab
    window.open(pdfUrl, '_blank');
  };

  return (
    <PageLayout
      title="Servo Stabilizers"
      subtitle="INDIA'S NO.1 POWER QUALITY EQUIPMENT MANUFACTURER"
      category="protect"
    >
      {/* Watermark */}
      <Watermark />



      {/* Enhanced Hero Section with Blue Background Design - following clampmeters pattern */}
      <section
        id="overview"
        ref={heroRef}
        className="py-6 md:py-12 mb-0 relative overflow-hidden font-['Open_Sans']"
      >
        {/* Hero Background Elements - Mobile optimized with blue theme */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-0 right-0 w-1/2 md:w-3/4 h-full bg-blue-50 rounded-bl-[50px] md:rounded-bl-[100px] transform -skew-x-6 md:-skew-x-12"></div>
          <div className="absolute bottom-20 left-0 w-32 h-32 md:w-64 md:h-64 bg-blue-400 rounded-full opacity-10"></div>
          <div className="absolute top-20 right-20 w-48 h-48 md:w-72 md:h-72 bg-blue-300/20 rounded-full blur-3xl animate-pulse-glow"></div>
          <div className="absolute bottom-10 left-10 w-48 h-48 md:w-72 md:h-72 bg-blue-400/20 rounded-full blur-3xl animate-pulse-glow-reverse"></div>
        </div>

        {/* Decorative elements with blue theme */}
        <div className="absolute -z-10 top-40 right-40 w-96 h-96 rounded-full border border-blue-300/30 animate-rotate-slow"></div>
        <div className="absolute -z-10 bottom-40 left-40 w-80 h-80 rounded-full border border-blue-300/40 animate-rotate-slow" style={{ animationDirection: 'reverse', animationDuration: '25s' }}></div>

        {/* Enhanced colorful background */}
        <div className="absolute inset-0 -z-20 overflow-hidden">
          {/* Subtle pattern overlay */}
          <div className="absolute inset-0 opacity-5 bg-[radial-gradient(circle_at_1px_1px,rgba(59,130,246,0.1)_1px,transparent_0)]" style={{ backgroundSize: "24px 24px" }}></div>
        </div>

        {/* CSS Animations */}
        <style>
          {`
            @keyframes pulse-glow {
              0% { opacity: 0.3; filter: blur(8px); }
              50% { opacity: 0.8; filter: blur(12px); }
              100% { opacity: 0.3; filter: blur(8px); }
            }

            @keyframes pulse-glow-reverse {
              0% { opacity: 0.6; filter: blur(10px); }
              50% { opacity: 0.2; filter: blur(15px); }
              100% { opacity: 0.6; filter: blur(10px); }
            }

            @keyframes rotate-slow {
              from { transform: rotate(0deg); }
              to { transform: rotate(360deg); }
            }

            .animate-pulse-glow {
              animation: pulse-glow 8s ease-in-out infinite;
            }

            .animate-pulse-glow-reverse {
              animation: pulse-glow-reverse 9s ease-in-out infinite;
            }

            .animate-rotate-slow {
              animation: rotate-slow 30s linear infinite;
            }
          `}
        </style>

        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-10 lg:gap-20 items-center">
            {/* Content Section - Now on the left with blue theme */}
            <motion.div
              initial={{ opacity: 0, x: -40 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative z-10 order-2 lg:order-1 space-y-4 text-center lg:text-left"
            >
              {/* KRYKARD Precision Instruments Badge */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="inline-block bg-blue-400 py-1 px-3 rounded-full mb-2"
              >
                <span className="text-sm md:text-base font-semibold text-white font-['Open_Sans']">KRYKARD Precision Instruments</span>
              </motion.div>

              {/* Enhanced product information with larger text - more responsive now */}
              <div className="mb-6 sm:mb-8">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight font-['Open_Sans'] mb-2 tracking-tight overflow-hidden">
                    SERVO <span className="text-blue-400">STABILIZERS</span>
                  </h1>
                </motion.div>

                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: "150px" }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                  className="h-1.5 sm:h-2 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full mb-4 sm:mb-6 mx-auto lg:mx-0"
                />

                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.8, delay: 0.7 }}
                  className="text-base sm:text-xl md:text-2xl font-medium text-gray-700 dark:text-gray-300 animate-text-fade-in font-['Open_Sans']"
                >
                  INDIA'S NO.1 POWER QUALITY EQUIPMENT MANUFACTURER
                </motion.p>
              </div>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.8 }}
                className="text-sm sm:text-base md:text-lg lg:text-xl text-gray-900 dark:text-gray-200 mb-6 sm:mb-10 leading-relaxed font-medium font-['Open_Sans']"
              >
                Setting the industry standard in design and quality with over 400,000 installations since 1985. Ensure
                <span className="text-blue-600 dark:text-blue-400 font-semibold"> consistent power supply </span>
                with KRYKARD's premium stabilizers.
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1 }}
                className="flex flex-wrap gap-3 sm:gap-4"
              >
                {/* Enhanced Enquiry Button with blue theme */}
                <Button
                  className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-4 sm:px-6 md:px-8 py-2.5 sm:py-3 rounded-xl shadow-xl hover:shadow-blue-500/30 transition-all duration-300 flex items-center gap-2 text-sm sm:text-base md:text-lg font-semibold"
                  onClick={() => window.location.href = "/contact/sales"}
                >
                  <Mail className="h-4 w-4 sm:h-5 sm:w-5" />
                  <span>GET A QUOTE</span>
                </Button>

                {/* Enhanced Brochure Button with blue theme */}
                <Button
                  variant="outline"
                  className="border-2 border-blue-500 text-blue-700 hover:bg-blue-50 hover:text-blue-800 hover:border-blue-600 px-4 sm:px-6 md:px-8 py-2.5 sm:py-3 rounded-xl transition-all duration-300 flex items-center gap-2 text-sm sm:text-base md:text-lg shadow-lg font-semibold"
                  onClick={openBrochure}
                >
                  <FileText className="h-4 w-4 sm:h-5 sm:w-5" />
                  <span>VIEW BROCHURE</span>
                </Button>
              </motion.div>
            </motion.div>

            {/* Image Section - Now on the right with blue background theme */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative order-1 lg:order-2 mt-6 sm:mt-0"
            >
              {/* Blue gradient background similar to clampmeters */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-200 to-blue-50 rounded-full opacity-20 blur-xl transform scale-90"></div>

              <motion.div
                animate={{
                  y: [0, -10, 0],
                }}
                transition={{
                  repeat: Infinity,
                  duration: 3,
                  ease: "easeInOut"
                }}
                className="relative z-10 flex justify-center"
              >
                <div className="relative">
                  <img
                    src="/banner-inside-2-1.png"
                    alt="KRYKARD Servo Stabilizer"
                    className="max-h-[300px] sm:max-h-[400px] md:max-h-[500px] lg:max-h-[600px] w-auto object-contain drop-shadow-2xl"
                  />
                </div>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section - With Interactive Cards */}
      <section className="py-20 relative overflow-hidden">
        {/* Enhanced colorful gradient background with animated overlay */}
        <div className="absolute inset-0 -z-10 overflow-hidden">
          {/* Main gradient background */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-100 via-indigo-50 to-purple-100 dark:from-blue-900/30 dark:via-indigo-900/30 dark:to-purple-900/30"></div>

          {/* Animated gradient overlay */}
          <div
            className="absolute inset-0 opacity-40 bg-[linear-gradient(60deg,transparent_0%,rgba(59,130,246,0.15)_20%,transparent_30%)]"
            style={{
              backgroundSize: "200% 200%",
              animation: "gradient-shift 12s ease infinite"
            }}
          ></div>

          {/* Colorful floating orbs */}
          <motion.div
            className="absolute top-20 left-[10%] w-64 h-64 rounded-full bg-blue-300/20 blur-3xl"
            animate={{
              y: [0, -30, 0],
              opacity: [0.2, 0.3, 0.2],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
          <motion.div
            className="absolute bottom-40 right-[15%] w-80 h-80 rounded-full bg-indigo-300/20 blur-3xl"
            animate={{
              y: [0, 40, 0],
              opacity: [0.2, 0.25, 0.2],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
          <motion.div
            className="absolute top-1/2 left-1/3 w-40 h-40 rounded-full bg-purple-300/15 blur-3xl"
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.15, 0.25, 0.15],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
        </div>

        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-400 dark:to-indigo-400 inline-block">
              Our Impact By Numbers
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-indigo-400 mx-auto mt-4 rounded-full"></div>
          </motion.div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-10">
            {statsData.map((stat, index) => (
              <StatsCard
                key={index}
                number={stat.number}
                suffix={stat.suffix}
                text={stat.text}
                icon={stat.icon}
                color1={stat.color1}
                color2={stat.color2}
                description={stat.description}
                index={index}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Enhanced Premium Features Section with 3D-like cards and interactive elements */}
      <section id="features" ref={featureRef} className="py-32 relative overflow-hidden">
        {/* Enhanced colorful background with depth */}
        <div className="absolute inset-0 -z-10 overflow-hidden">
          {/* Main gradient background with more vibrant colors */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-100/80 via-indigo-50 to-purple-100/80 dark:from-blue-900/40 dark:via-indigo-900/40 dark:to-purple-900/40"></div>

          {/* Animated floating elements */}
          <motion.div
            className="absolute top-20 right-[15%] w-72 h-72 rounded-full bg-blue-300/20 blur-3xl"
            animate={{
              y: [0, -40, 0],
              scale: [1, 1.1, 1],
              opacity: [0.2, 0.3, 0.2],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
          <motion.div
            className="absolute bottom-40 left-[10%] w-80 h-80 rounded-full bg-indigo-300/20 blur-3xl"
            animate={{
              y: [0, 50, 0],
              scale: [1.1, 1, 1.1],
              opacity: [0.2, 0.25, 0.2],
            }}
            transition={{
              duration: 15,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />

          {/* Enhanced pattern overlay */}
          <div className="absolute inset-0 opacity-5"
            style={{
              backgroundImage: "radial-gradient(circle at 20px 20px, rgba(79, 70, 229, 0.3) 2px, transparent 0)",
              backgroundSize: "24px 24px"
            }}
          ></div>
        </div>

        <div className="container mx-auto px-4 relative">


          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7 }}
            className="text-center mb-20"
          >
            <motion.h2
              className="text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-indigo-600 to-blue-600 dark:from-blue-400 dark:via-indigo-400 dark:to-blue-400 inline-block mb-6"
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              PREMIUM FEATURES
            </motion.h2>

            <div className="w-32 h-1.5 bg-gradient-to-r from-blue-500 to-indigo-500 mx-auto mt-4 rounded-full"></div>

            <motion.p
              className="text-center text-gray-800 dark:text-gray-200 max-w-3xl mx-auto mt-8 text-xl font-medium leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              Advanced technology that sets KRYKARD Servo Stabilizers apart from conventional designs,
              delivering <span className="text-blue-600 font-semibold">unmatched performance</span> and
              <span className="text-indigo-600 font-semibold"> reliability</span> for your critical equipment.
            </motion.p>
          </motion.div>

          {/* Enhanced Feature Cards with 3D effect and interactive elements */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.7, delay: index * 0.15 }}
                whileHover={{
                  y: -10,
                  transition: { type: "spring", stiffness: 300, damping: 20 }
                }}
                className="group relative"
              >
                {/* Card shadow/glow effect */}
                <div className="absolute -inset-1 bg-gradient-to-r from-blue-500/20 to-indigo-500/20 rounded-2xl blur-lg opacity-0 group-hover:opacity-100 transition-all duration-500"></div>

                {/* Main card */}
                <div className="bg-white dark:bg-slate-800 rounded-xl shadow-xl p-8 border border-blue-100/80 dark:border-blue-900/30 relative z-10 h-full transform transition-all duration-500 group-hover:shadow-2xl">
                  {/* Header with icon and title */}
                  <div className="flex items-center mb-6">
                    <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-4 rounded-xl mr-5 shadow-lg transform transition-transform duration-500 group-hover:scale-110 group-hover:rotate-3">
                      {feature.icon}
                    </div>
                    <h3 className="text-2xl font-bold text-gray-800 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">{feature.title}</h3>
                  </div>

                  {/* Description with enhanced typography */}
                  <p className="text-gray-700 dark:text-gray-300 mb-6 text-lg font-medium">{feature.description}</p>

                  {/* Feature items with enhanced styling and animations - Improved mobile display */}
                  <div className="space-y-4">
                    {feature.items.map((item, i) => (
                      <motion.div
                        key={i}
                        className="flex items-start p-3 sm:p-4 rounded-xl bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 border border-blue-100 dark:border-blue-800/30 transform transition-all duration-300 hover:translate-y-[-5px] hover:shadow-md"
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.5, delay: 0.3 + (i * 0.1) }}
                      >
                        <div className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-lg p-2 mr-3 sm:mr-4 shadow-md flex-shrink-0 transform transition-transform duration-300 group-hover:scale-110">
                          {item.icon}
                        </div>
                        <div className="flex-grow min-w-0">
                          <h4 className="font-bold text-gray-800 dark:text-white text-base sm:text-lg mb-1 break-words">{item.title}</h4>
                          <p className="text-gray-600 dark:text-gray-300 text-sm sm:text-base break-words">{item.description}</p>
                        </div>
                      </motion.div>
                    ))}
                  </div>

                  {/* Decorative corner accent */}
                  <div className="absolute -bottom-2 -right-2 w-24 h-24 border-r-2 border-b-2 border-blue-400/30 rounded-br-xl opacity-40"></div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Bottom decorative element */}
          <motion.div
            className="mt-20 text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <div className="inline-flex items-center justify-center">
              <div className="h-px w-12 bg-blue-300 dark:bg-blue-700"></div>
              <div className="mx-4 text-blue-500 dark:text-blue-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <div className="h-px w-12 bg-blue-300 dark:bg-blue-700"></div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Modern Product Types Section - Centered Design */}
      <section id="products" className="py-32 relative overflow-hidden bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-950">
        {/* Enhanced colorful background with depth */}        <div className="absolute inset-0 -z-10 overflow-hidden">
          {/* Main gradient background with more vibrant colors */}
          <div className="absolute inset-0 bg-gradient-to-br from-indigo-100/80 via-blue-50 to-sky-100/80 dark:from-indigo-900/40 dark:via-blue-900/40 dark:to-sky-900/40"></div>

          {/* Background pattern for visual texture */}
          <div className="absolute inset-0 opacity-10 dark:opacity-5">
            <div className="absolute inset-0" style={{
              backgroundImage: "radial-gradient(circle at 25px 25px, rgba(59, 130, 246, 0.15) 2px, transparent 0)",
              backgroundSize: "40px 40px"
            }}></div>
          </div>

          {/* Animated floating elements */}
          <motion.div
            className="absolute top-40 right-[20%] w-96 h-96 rounded-full bg-blue-300/15 blur-3xl"
            animate={{
              y: [0, -50, 0],
              scale: [1, 1.1, 1],
              opacity: [0.15, 0.25, 0.15],
            }}
            transition={{
              duration: 15,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
          <motion.div
            className="absolute bottom-20 left-[15%] w-80 h-80 rounded-full bg-indigo-300/15 blur-3xl"
            animate={{
              y: [0, 40, 0],
              scale: [1.1, 1, 1.1],
              opacity: [0.15, 0.2, 0.15],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />

          {/* Enhanced pattern overlay */}
          <div className="absolute inset-0 opacity-5"
            style={{
              backgroundImage: "radial-gradient(circle at 25px 25px, rgba(79, 70, 229, 0.2) 2px, transparent 0)",
              backgroundSize: "30px 30px"
            }}
          ></div>
        </div>

        <div className="container mx-auto px-4 relative">
          <motion.div
            className="text-center mb-20"
            initial={{ opacity: 0, y: -20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
          >
            <motion.h2
              className="text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-indigo-600 to-blue-600 dark:from-blue-400 dark:via-indigo-400 dark:to-blue-400 inline-block mb-6"
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              Find Your Perfect Stabilizer
            </motion.h2>

            <div className="w-32 h-1.5 bg-gradient-to-r from-blue-500 to-indigo-500 mx-auto mt-4 rounded-full"></div>

            <motion.p
              className="text-center text-gray-800 dark:text-gray-200 max-w-3xl mx-auto mt-8 text-xl font-medium leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              Explore our range of high-performance servo stabilizers tailored for different applications and power requirements
            </motion.p>
          </motion.div>
            {/* Product Cards - New Clean UI Design */}
          <div className="max-w-6xl mx-auto space-y-16">            {/* First Product - 1 Phase Stabilizers */}
            <div id="1phase" className="pt-20 -mt-20"></div> {/* Anchor target with padding to prevent overlap */}
            {productTypes.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.7 }}
                className="relative"
              ><div className="relative bg-white/95 dark:bg-slate-800/95 backdrop-blur-sm rounded-3xl shadow-xl overflow-hidden border border-blue-100/50 dark:border-blue-900/30 transform transition-all duration-500 hover:shadow-2xl">                  {/* Completely redesigned corner accent to prevent overlap */}
                  <div className="absolute top-0 left-0 w-0 h-0 border-t-[48px] border-l-[48px] border-t-blue-400 border-l-blue-400 rounded-br-sm z-1"></div>

                  {/* Icon for the Product - Positioned better for mobile */}
                  <div className="absolute top-3 left-3 z-20">
                    <div className={`flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 flex items-center justify-center shadow-lg`}>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    </div>
                  </div>                  <div className="flex flex-col lg:flex-row">
                    {/* Content Section (Left) - Improved mobile padding and spacing */}
                    <div className="p-6 sm:p-8 pt-16 lg:pt-8 lg:pl-12 lg:w-7/12 flex flex-col">
                      {/* Title - Fixed positioning with clear separation from corner and better mobile spacing */}
                      <h3 className={`text-2xl sm:text-3xl font-bold text-blue-600 mb-4 relative z-30 mt-3 pl-0 sm:pl-1`}>{productTypes[0].title}</h3>

                      {/* Description */}
                      <p className="text-gray-700 dark:text-gray-300 text-base leading-relaxed mb-6">{productTypes[0].description}</p>

                      {/* Key Features */}
                      <div className="mb-6">
                        <h4 className={`inline-block text-lg font-semibold mb-5 text-blue-600 border-b-2 border-gray-200 pb-2`}>
                          Key Features
                        </h4>
                        <div className="space-y-3">
                          {productTypes[0].features.map((feature, fidx) => (
                            <div
                              key={fidx}
                              className="flex items-center"
                            >
                              <div className="flex-shrink-0 h-5 w-5 rounded-full bg-blue-100 dark:bg-blue-900/40 flex items-center justify-center mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                              </div>
                              <span className="text-gray-800 dark:text-gray-200 font-medium">{feature}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Technical highlights - Improved mobile responsiveness */}
                      <div className="mt-auto grid grid-cols-2 sm:grid-cols-3 gap-2 py-4 border-t border-gray-200 dark:border-gray-700">
                        <div className="p-2">
                          <div className="text-xs sm:text-sm font-bold text-blue-600 mb-1">Efficiency</div>
                          <div className="text-gray-600 dark:text-gray-400 text-base sm:text-lg font-semibold">98%+</div>
                        </div>
                        <div className="p-2">
                          <div className="text-xs sm:text-sm font-bold text-blue-600 mb-1">Response</div>
                          <div className="text-gray-600 dark:text-gray-400 text-base sm:text-lg font-semibold">&lt;20ms</div>
                        </div>
                        <div className="p-2 col-span-2 sm:col-span-1">
                          <div className="text-xs sm:text-sm font-bold text-blue-600 mb-1">Regulation</div>
                          <div className="text-gray-600 dark:text-gray-400 text-base sm:text-lg font-semibold">±1%</div>
                        </div>
                      </div>

                      {/* View Details Button */}
                      <div className="mt-4">
                        <Button
                          className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white shadow-md font-semibold py-2 rounded-lg transition-all duration-300"
                          onClick={() => window.location.href = "/protect/productpages/SinglePhaseStabilizer"}
                        >
                          View Details
                        </Button>
                      </div>
                    </div>                    {/* Image Section (Right) - Responsive positioning to prevent overlapping */}
                    <div className="lg:w-5/12 relative">                      {/* Image container for desktop */}
                      <div className="hidden lg:block relative h-full">                        <div className="absolute inset-0 flex items-center justify-center overflow-hidden">
                          <motion.img
                            whileHover={{ scale: 1.05 }}
                            animate={{
                              y: [0, -15, 0],
                              transition: {
                                duration: 4,
                                repeat: Infinity,
                                repeatType: "reverse",
                                ease: "easeInOut"
                              }
                            }}
                            src={productTypes[0].imageUrl}
                            alt={productTypes[0].altText}
                            className="w-auto h-[95%] max-w-[110%] object-contain z-10"
                          />
                        </div>
                        {/* Background glow effect */}
                        <div className="absolute bottom-1/2 right-0 h-2/3 w-2/3 bg-gradient-radial from-blue-100/30 to-transparent dark:from-blue-900/20 dark:to-transparent rounded-full blur-xl"></div>
                      </div>                        {/* Image for mobile/tablet - Improved responsive layout */}
                      <div className="lg:hidden py-4 px-4 flex justify-center items-center">
                        <motion.img
                          whileHover={{ scale: 1.05 }}
                          animate={{
                            y: [0, -10, 0],
                            transition: {
                              duration: 4,
                              repeat: Infinity,
                              repeatType: "reverse",
                              ease: "easeInOut"
                            }
                          }}
                          src={productTypes[0].imageUrl}
                          alt={productTypes[0].altText}
                          className="w-full h-auto max-h-80 object-contain mx-auto"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}            {/* Second Product - 3 Phase Stabilizers */}
            <div id="3phase" className="pt-20 -mt-20"></div> {/* Anchor target with padding to prevent overlap */}
            {productTypes.length > 1 && (
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.7 }}
                className="relative"
              ><div className="relative bg-white/95 dark:bg-slate-800/95 backdrop-blur-sm rounded-3xl shadow-xl overflow-hidden border border-blue-100/50 dark:border-blue-900/30 transform transition-all duration-500 hover:shadow-2xl">                  {/* Completely redesigned corner accent to prevent overlap */}
                  <div className="absolute top-0 left-0 w-0 h-0 border-t-[48px] border-l-[48px] border-t-blue-600 border-l-blue-600 rounded-br-sm z-1"></div>

                  {/* Icon for the Product - Positioned better for mobile */}
                  <div className="absolute top-3 left-3 z-20">
                    <div className={`flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center shadow-lg`}>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    </div>
                  </div>

                  <div className="flex flex-col lg:flex-row">
                    {/* Content Section (Left) */}
                    <div className="p-8 pt-16 lg:pt-8 lg:pl-12 lg:w-7/12 flex flex-col">
                      {/* Title - Fixed positioning with clear separation from corner */}
                      <h3 className={`text-3xl font-bold text-blue-600 mb-4 relative z-30 mt-3`}>{productTypes[1].title}</h3>

                      {/* Description */}
                      <p className="text-gray-700 dark:text-gray-300 text-base leading-relaxed mb-6">{productTypes[1].description}</p>

                      {/* Key Features */}
                      <div className="mb-6">
                        <h4 className={`inline-block text-lg font-semibold mb-5 text-blue-600 border-b-2 border-gray-200 pb-2`}>
                          Key Features
                        </h4>
                        <div className="space-y-2">
                          {productTypes[1].features.slice(0, 4).map((feature, fidx) => (
                            <div
                              key={fidx}
                              className="flex items-center"
                            >
                              <div className="flex-shrink-0 h-5 w-5 rounded-full bg-blue-100 dark:bg-blue-900/40 flex items-center justify-center mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                              </div>
                              <span className="text-gray-800 dark:text-gray-200 font-medium">{feature}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Technical highlights */}
                      <div className="mt-auto grid grid-cols-3 gap-2 py-4 border-t border-gray-200 dark:border-gray-700">
                        <div>
                          <div className="text-sm font-bold text-blue-600 mb-1">Efficiency</div>
                          <div className="text-gray-600 dark:text-gray-400 text-lg font-semibold">98%+</div>
                        </div>
                        <div>
                          <div className="text-sm font-bold text-blue-600 mb-1">Response</div>
                          <div className="text-gray-600 dark:text-gray-400 text-lg font-semibold">&lt;20ms</div>
                        </div>
                        <div>
                          <div className="text-sm font-bold text-blue-600 mb-1">Regulation</div>
                          <div className="text-gray-600 dark:text-gray-400 text-lg font-semibold">±1%</div>
                        </div>
                      </div>

                      {/* View Details Button */}
                      <div className="mt-4">
                        <Button
                          className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white shadow-md font-semibold py-2 rounded-lg transition-all duration-300"
                          onClick={() => window.location.href = "/protect/productpages/ThreePhaseStabilizer"}
                        >
                          View Details
                        </Button>
                      </div>
                    </div>                    {/* Image Section (Right) - Responsive positioning to prevent overlapping */}
                    <div className="lg:w-5/12 relative">                      {/* Image container for desktop */}
                      <div className="hidden lg:block relative h-full">                        <div className="absolute inset-0 flex items-center justify-center overflow-hidden">
                          <motion.img
                            whileHover={{ scale: 1.05 }}
                            animate={{
                              y: [0, -15, 0],
                              transition: {
                                duration: 4.5,
                                repeat: Infinity,
                                repeatType: "reverse",
                                ease: "easeInOut"
                              }
                            }}
                            src={productTypes[1].imageUrl}
                            alt={productTypes[1].altText}
                            className="w-auto h-[95%] max-w-[110%] object-contain z-10"
                          />
                        </div>
                        {/* Background glow effect */}
                        <div className="absolute bottom-1/2 right-0 h-2/3 w-2/3 bg-gradient-radial from-cyan-100/30 to-transparent dark:from-cyan-900/20 dark:to-transparent rounded-full blur-xl"></div>
                      </div>
                        {/* Image for mobile/tablet */}
                      <div className="lg:hidden py-6 px-4">                        <motion.img
                          whileHover={{ scale: 1.05 }}
                          animate={{
                            y: [0, -10, 0],
                            transition: {
                              duration: 4.5,
                              repeat: Infinity,
                              repeatType: "reverse",
                              ease: "easeInOut"
                            }
                          }}
                          src={productTypes[1].imageUrl}
                          alt={productTypes[1].altText}
                          className="w-full h-auto max-h-[32rem] object-contain mx-auto"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}            {/* Third Product - Custom Designed Stabilizers */}
            <div id="custom" className="pt-20 -mt-20"></div> {/* Anchor target with padding to prevent overlap */}
            {productTypes.length > 2 && (
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.7 }}
                className="relative"
              ><div className="relative bg-white/95 dark:bg-slate-800/95 backdrop-blur-sm rounded-3xl shadow-xl overflow-hidden border border-blue-100/50 dark:border-blue-900/30 transform transition-all duration-500 hover:shadow-2xl">                  {/* Completely redesigned corner accent to prevent overlap */}
                  <div className="absolute top-0 left-0 w-0 h-0 border-t-[48px] border-l-[48px] border-t-cyan-500 border-l-cyan-500 rounded-br-sm z-1"></div>

                  {/* Icon for the Product - Positioned better for mobile */}
                  <div className="absolute top-3 left-3 z-20">
                    <div className={`flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-cyan-500 to-blue-500 flex items-center justify-center shadow-lg`}>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    </div>
                  </div>

                  <div className="flex flex-col lg:flex-row">
                    {/* Content Section (Left) */}
                    <div className="p-8 pt-16 lg:pt-8 lg:pl-12 lg:w-7/12 flex flex-col">
                      {/* Title - Fixed positioning with clear separation from corner */}
                      <h3 className={`text-3xl font-bold text-cyan-700 mb-4 relative z-30 mt-3`}>{productTypes[2].title}</h3>

                      {/* Description */}
                      <p className="text-gray-700 dark:text-gray-300 text-base leading-relaxed mb-6">{productTypes[2].description}</p>

                      {/* Key Features */}
                      <div className="mb-6">
                        <h4 className={`inline-block text-lg font-semibold mb-5 text-cyan-700 border-b-2 border-gray-200 pb-2`}>
                          Key Features
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2">
                          {productTypes[2].features.map((feature, fidx) => (
                            <div
                              key={fidx}
                              className="flex items-center"
                            >
                              <div className="flex-shrink-0 h-5 w-5 rounded-full bg-cyan-100 dark:bg-cyan-900/40 flex items-center justify-center mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-cyan-600" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                              </div>
                              <span className="text-gray-800 dark:text-gray-200 font-medium text-sm">{feature}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Technical highlights */}
                      <div className="mt-auto grid grid-cols-4 gap-2 py-4 border-t border-gray-200 dark:border-gray-700">
                        <div>
                          <div className="text-sm font-bold text-cyan-700 mb-1">Efficiency</div>
                          <div className="text-gray-600 dark:text-gray-400 text-lg font-semibold">98%+</div>
                        </div>
                        <div>
                          <div className="text-sm font-bold text-cyan-700 mb-1">Response</div>
                          <div className="text-gray-600 dark:text-gray-400 text-lg font-semibold">&lt;20ms</div>
                        </div>
                        <div>
                          <div className="text-sm font-bold text-cyan-700 mb-1">Regulation</div>
                          <div className="text-gray-600 dark:text-gray-400 text-lg font-semibold">±1%</div>
                        </div>
                        <div>
                          <div className="text-sm font-bold text-cyan-700 mb-1">Custom</div>
                          <div className="text-gray-600 dark:text-gray-400 text-lg font-semibold">100%</div>
                        </div>
                      </div>

                      {/* Contact Button for Custom Solutions */}
                      <div className="mt-4">
                        <Button
                          className="w-full bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white shadow-md font-semibold py-2 rounded-lg transition-all duration-300"
                          onClick={() => window.location.href = "/contact/sales"}
                        >
                          Request Custom Solution
                        </Button>
                      </div>
                    </div>                    {/* Image Section (Right) - Responsive positioning to prevent overlapping */}
                    <div className="lg:w-5/12 relative">                      {/* Image container for desktop */}
                      <div className="hidden lg:block relative h-full">                        <div className="absolute inset-0 flex items-center justify-center overflow-hidden">                          <motion.img
                            whileHover={{ scale: 1.05 }}
                            animate={{
                              y: [0, -15, 0],
                              transition: {
                                duration: 5,
                                repeat: Infinity,
                                repeatType: "reverse",
                                ease: "easeInOut"
                              }
                            }}
                            src={productTypes[2].imageUrl}
                            alt={productTypes[2].altText}
                            className="w-auto h-[115%] max-w-[140%] object-contain z-10"
                          />
                        </div>
                        {/* Background glow effect */}
                        <div className="absolute bottom-1/2 right-0 h-2/3 w-2/3 bg-gradient-radial from-cyan-100/30 to-transparent dark:from-cyan-900/20 dark:to-transparent rounded-full blur-xl"></div>
                      </div>
                        {/* Image for mobile/tablet */}
                      <div className="lg:hidden py-6 px-4">                        <motion.img
                          whileHover={{ scale: 1.05 }}
                          animate={{
                            y: [0, -10, 0],
                            transition: {
                              duration: 5,
                              repeat: Infinity,
                              repeatType: "reverse",
                              ease: "easeInOut"
                            }
                          }}
                          src={productTypes[2].imageUrl}
                          alt={productTypes[2].altText}
                          className="w-full h-auto max-h-[32rem] object-contain mx-auto"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </div>

          {/* Bottom decorative element */}
          <motion.div
            className="mt-20 text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <div className="inline-flex items-center justify-center">
              <div className="h-px w-16 bg-blue-300 dark:bg-blue-700"></div>
              <div className="mx-4 text-blue-500 dark:text-blue-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                </svg>
              </div>
              <div className="h-px w-16 bg-blue-300 dark:bg-blue-700"></div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Ultra-Modern Technical Specifications Section with 3D Effects */}
      <section id="specifications" className="relative py-32 overflow-hidden">
        {/* Enhanced colorful background with depth and animation */}
        <div className="absolute inset-0 -z-10 overflow-hidden">
          {/* Main gradient background with vibrant colors */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-100/80 via-indigo-50/90 to-purple-100/80 dark:from-blue-900/40 dark:via-indigo-900/40 dark:to-purple-900/40"></div>

          {/* Animated floating elements */}
          <motion.div
            className="absolute top-40 left-[20%] w-96 h-96 rounded-full bg-blue-300/15 blur-3xl"
            animate={{
              y: [0, -50, 0],
              x: [0, 30, 0],
              scale: [1, 1.1, 1],
              opacity: [0.15, 0.25, 0.15],
            }}
            transition={{
              duration: 15,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
          <motion.div
            className="absolute bottom-20 right-[15%] w-80 h-80 rounded-full bg-indigo-300/15 blur-3xl"
            animate={{
              y: [0, 40, 0],
              x: [0, -20, 0],
              scale: [1.1, 1, 1.1],
              opacity: [0.15, 0.2, 0.15],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />

          {/* Circuit board pattern overlay */}
          <div className="absolute inset-0 opacity-5"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234338ca' fill-opacity='0.2'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
              backgroundSize: '60px 60px'
            }}
          ></div>
        </div>

        <div className="container mx-auto px-4 relative">


          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7 }}
            className="text-center mb-20 pt-10"
          >
            <motion.h2
              className="text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-indigo-600 to-blue-600 dark:from-blue-400 dark:via-indigo-400 dark:to-blue-400 inline-block mb-6"
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              Technical Specifications
            </motion.h2>

            <div className="w-32 h-1.5 bg-gradient-to-r from-blue-500 to-indigo-500 mx-auto mt-4 rounded-full"></div>

            <motion.p
              className="text-center text-gray-800 dark:text-gray-200 max-w-3xl mx-auto mt-8 text-xl font-medium leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              Engineered for <span className="text-blue-600 font-semibold">optimal performance</span> across a wide range of applications with
              <span className="text-indigo-600 font-semibold"> industry-leading precision</span>
            </motion.p>
          </motion.div>          {/* Interactive Specification Cards with 3D Effects - Improved mobile layout */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 lg:gap-10 max-w-6xl mx-auto">
            {specifications.map((spec, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{
                  duration: 0.7,
                  delay: index * 0.2,
                  type: "spring",
                  stiffness: 100
                }}
                whileHover={{
                  y: -10,
                  rotateY: 5,
                  rotateX: 5,
                  scale: 1.02,
                  transition: {
                    type: "spring",
                    stiffness: 400,
                    damping: 10
                  }
                }}
                className="group relative"
              >
                {/* Card glow effect on hover */}
                <div className="absolute -inset-1 bg-gradient-to-r from-blue-500/20 via-indigo-500/20 to-purple-500/20 rounded-2xl blur-lg opacity-0 group-hover:opacity-100 transition-all duration-500"></div>

                {/* Main card with glass morphism */}
                <div className="bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm rounded-2xl shadow-xl overflow-hidden border border-blue-100/50 dark:border-blue-900/30 relative z-10 h-full transform transition-all duration-500 group-hover:shadow-2xl">
                  {/* Decorative top bar with gradient */}
                  <div className="h-2 w-full bg-gradient-to-r from-blue-500 to-indigo-600"></div>

                  <div className="p-8">
                    {/* Category heading with icon */}
                    <div className="flex items-center mb-8">
                      <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-3 rounded-xl mr-4 shadow-lg transform transition-transform duration-500 group-hover:scale-110 group-hover:rotate-3">
                        {index === 0 ? (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                          </svg>
                        ) : index === 1 ? (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                          </svg>
                        ) : (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c-.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                        )}
                      </div>
                      <h3 className="text-2xl font-bold text-blue-600 dark:text-blue-400 group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors duration-300">
                        {spec.category}
                      </h3>
                    </div>

                    {/* Animated specs list */}
                    <div className="space-y-5">
                      {spec.items.map((item, itemIndex) => (
                        <motion.div
                          key={itemIndex}
                          initial={{ opacity: 0, x: -20 }}
                          whileInView={{ opacity: 1, x: 0 }}
                          viewport={{ once: true }}
                          transition={{ duration: 0.5, delay: 0.3 + (itemIndex * 0.1) }}
                          className="relative"
                        >
                          <div className="flex justify-between items-center p-4 rounded-lg bg-gradient-to-r from-blue-50/70 to-indigo-50/70 dark:from-blue-900/30 dark:to-indigo-900/30 border border-blue-100/50 dark:border-blue-800/30 transform transition-all duration-300 hover:translate-y-[-3px] hover:shadow-md">
                            <div className="flex items-center">
                              <div className="w-2 h-2 rounded-full bg-blue-500 mr-3"></div>
                              <span className="text-gray-800 dark:text-gray-200 font-semibold">{item.label}</span>
                            </div>
                            <motion.div
                              className="font-bold text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/30 px-3 py-1 rounded-md"
                              whileHover={{
                                scale: 1.05,
                                backgroundColor: "rgba(79, 70, 229, 0.1)"
                              }}
                            >
                              {item.value}
                            </motion.div>
                          </div>
                        </motion.div>
                      ))}
                    </div>

                    {/* Interactive 3D corner accent */}
                    <motion.div
                      className="absolute -bottom-3 -right-3 w-24 h-24 border-r-2 border-b-2 border-blue-400/50 rounded-br-xl"
                      animate={{
                        opacity: [0.3, 0.5, 0.3],
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        repeatType: "reverse"
                      }}
                      style={{
                        transformStyle: "preserve-3d",
                        transform: "perspective(1000px) rotateX(10deg) rotateY(-10deg)"
                      }}
                    />

                    {/* Animated particle effect */}
                    {Array.from({ length: 3 }).map((_, i) => (
                      <motion.div
                        key={i}
                        className="absolute w-2 h-2 rounded-full bg-blue-400/50"
                        style={{
                          top: `${Math.random() * 100}%`,
                          left: `${Math.random() * 100}%`,
                          zIndex: 20
                        }}
                        animate={{
                          y: [0, Math.random() * 20 - 10],
                          x: [0, Math.random() * 20 - 10],
                          opacity: [0.5, 0.2, 0.5],
                          scale: [1, 1.5, 1],
                        }}
                        transition={{
                          duration: 2 + Math.random() * 2,
                          repeat: Infinity,
                          repeatType: "reverse",
                        }}
                      />
                    ))}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>




        </div>
      </section>

      {/* Contact Information Section - Light Blue Box Style */}
      <section className="py-16 relative overflow-hidden">
        <div className="container mx-auto px-4 max-w-6xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="rounded-xl bg-gradient-to-br from-blue-100 via-blue-50 to-indigo-100 shadow-md p-10 relative overflow-hidden"
          >
            {/* Decorative elements for contact section */}
            <div className="absolute -top-10 -right-10 w-40 h-40 bg-blue-300/20 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-10 -left-10 w-40 h-40 bg-indigo-300/20 rounded-full blur-3xl"></div>
            <motion.div
              className="absolute top-1/2 right-1/4 w-20 h-20 bg-sky-300/20 rounded-full blur-xl"
              animate={{
                y: [0, -15, 0],
                opacity: [0.2, 0.3, 0.2],
              }}
              transition={{
                duration: 6,
                repeat: Infinity,
                repeatType: "reverse"
              }}
            ></motion.div>
            <div className="text-center">
              <motion.h2
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
                className="text-3xl font-bold text-blue-700 mb-4"
              >
                Need More Information?
              </motion.h2>

              <motion.p
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="text-blue-600 max-w-3xl mx-auto mb-8"
              >
                Our team of experts is ready to help you with product specifications, custom solutions, pricing, and
                any other details you need about the KRYKARD Servo Stabilizer systems.
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <Button
                  className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white px-8 py-3 rounded-md flex items-center gap-2 mx-auto shadow-lg hover:shadow-blue-300/30 transition-all duration-300"
                  onClick={() => window.location.href = "/contact/sales"}
                >
                  <Mail className="h-5 w-5" />
                  <span className="font-semibold">Contact Our Experts</span>
                </Button>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>
    </PageLayout>
  );
};

export default ServoStabilizers;