import React, { useEffect, useRef } from "react";
import { motion, useInView } from "framer-motion";
import { Shield, Zap, BarChart3, Check, ExternalLink } from "lucide-react";

// Enhanced Services data with more subtle colors
const services = [
  {
    id: "measure",
    title: "Measure",
    icon: <BarChart3 className="h-8 w-8" />,
    color: "from-yellow-400/80 to-amber-500/80",
    hoverColor: "group-hover:from-yellow-500 group-hover:to-amber-600",
    bgColor: "from-yellow-900/30 to-amber-900/30",
    textColor: "text-yellow-50",
    iconBg: "bg-yellow-500",
    description:
      "Advanced analytics and monitoring solutions for complete visibility into your power infrastructure.",
    features: ["Real-time monitoring", "Power quality analysis", "Predictive diagnostics", "Energy usage patterns"],
  },
  {
    id: "protect",
    title: "Protect",
    icon: <Shield className="h-8 w-8" />,
    color: "from-blue-400/80 to-blue-600/80",
    hoverColor: "group-hover:from-blue-500 group-hover:to-blue-700",
    bgColor: "from-blue-900/30 to-blue-950/30",
    textColor: "text-blue-50",
    iconBg: "bg-blue-600",
    description:
      "Comprehensive protection systems to safeguard equipment from power disturbances and anomalies.",
    features: ["Surge protection", "Voltage stabilization", "Backup power systems", "Critical load protection"],
  },
  {
    id: "conserve",
    title: "Conserve",
    icon: <Zap className="h-8 w-8" />,
    color: "from-green-400/80 to-green-600/80",
    hoverColor: "group-hover:from-green-500 group-hover:to-green-700",
    bgColor: "from-green-900/30 to-green-950/30",
    textColor: "text-green-50",
    iconBg: "bg-green-600",
    description:
      "Intelligent energy optimization to reduce consumption, costs, and environmental impact.",
    features: ["AI optimization", "Peak demand management", "Renewable integration", "Energy efficiency analysis"],
  },
];

const ServicesSection = () => {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: false, amount: 0.2 });
  
  // Card refs for hover effects
  const cardRefs = useRef([]);
  
  // Set up refs for each card
  useEffect(() => {
    cardRefs.current = cardRefs.current.slice(0, services.length);
  }, []);
  
  // Card hover interaction effect
  const handleCardHover = (index, isHovering) => {
    if (!cardRefs.current[index]) return;
    
    const card = cardRefs.current[index];
    
    if (isHovering) {
      card.style.transform = 'translateY(-8px)';
      card.style.transition = 'all 0.4s ease';
    } else {
      card.style.transform = 'translateY(0)';
      card.style.transition = 'all 0.4s ease';
    }
  };

  return (
    <section 
      ref={sectionRef}
      className="relative py-24 bg-gradient-to-b from-gray-950 to-black overflow-hidden"
      id="services"
    >
      {/* Subtle background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900/5 via-green-900/5 to-yellow-900/5 z-0" />
      
      {/* Subtle floating orbs with reduced opacity */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none z-0">
        {/* Blue orb */}
        <motion.div 
          className="absolute w-96 h-96 rounded-full opacity-5 bg-blue-500/20 blur-3xl"
          style={{ top: '10%', left: '15%' }}
          animate={{
            scale: [1, 1.2, 1],
            y: [0, 30, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        
        {/* Green orb */}
        <motion.div 
          className="absolute w-64 h-64 rounded-full opacity-5 bg-green-500/20 blur-3xl"
          style={{ bottom: '20%', right: '15%' }}
          animate={{
            scale: [1, 1.3, 1],
            y: [0, -40, 0],
          }}
          transition={{
            duration: 30,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>
      
      <div className="container max-w-7xl mx-auto px-6 relative z-10">
        {/* Section Header with animations */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          {/* Modern Highlight Badge */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="mb-6"
          >
            <span className="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full bg-white/5 text-white/80 border border-white/10">
              <span className="flex h-2 w-2 rounded-full bg-blue-400 mr-2"></span>
              Our Services
            </span>
          </motion.div>
          
          <motion.h2 
            className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-300 via-green-300 to-yellow-300">
              Comprehensive
            </span>{' '}
            <span className="text-white">Energy Solutions</span>
          </motion.h2>
          
          <motion.p 
            className="text-xl text-gray-300/90 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            Our integrated approach combines cutting-edge hardware, intelligent software, and expert
            services to deliver complete energy management.
          </motion.p>
        </motion.div>

        {/* Service Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <motion.div
              key={service.id}
              initial={{ opacity: 0, y: 50 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
              transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
              ref={el => cardRefs.current[index] = el}
              onMouseEnter={() => handleCardHover(index, true)}
              onMouseLeave={() => handleCardHover(index, false)}
              className="group h-full"
            >
              <div className="relative h-full rounded-xl overflow-hidden">
                {/* Subtle card glow effect */}
                <div
                  className={`absolute inset-0 bg-gradient-to-br ${service.color} rounded-xl blur-lg opacity-20 group-hover:opacity-30 transition-all duration-500 -z-10`}
                />
                
                {/* Main card content */}
                <div className={`relative h-full flex flex-col bg-gradient-to-br ${service.bgColor} border border-white/10 rounded-xl p-8 overflow-hidden transition-all duration-500 group-hover:border-white/20 shadow-lg`}>
                  {/* Animated accent line at top */}
                  <div className={`absolute top-0 left-0 right-0 h-1 ${
                    service.id === "measure" 
                      ? "bg-yellow-400/60" 
                      : service.id === "protect" 
                        ? "bg-blue-400/60" 
                        : "bg-green-400/60"
                  } transform scale-x-0 group-hover:scale-x-100 origin-left transition-transform duration-500 ease-out`}></div>
                  
                  {/* Title and Icon */}
                  <div className="flex items-center mb-6 relative">
                    <div
                      className={`p-3 rounded-lg ${service.iconBg} text-white shadow-lg transition-all duration-500 group-hover:scale-110`}
                    >
                      {service.icon}
                    </div>
                    <h3 className="text-2xl font-bold text-white ml-4">
                      {service.title}
                    </h3>
                  </div>
                  
                  {/* Description */}
                  <p className={`${service.textColor} mb-6 flex-grow relative text-lg leading-relaxed`}>
                    {service.description}
                  </p>
                  
                  {/* Feature list */}
                  <ul className="space-y-3 mb-8 relative">
                    {service.features.map((item, i) => (
                      <li 
                        key={i} 
                        className="flex items-start"
                      >
                        <span className={`flex items-center justify-center h-5 w-5 rounded-full ${
                          service.id === "measure" 
                            ? "bg-yellow-500/20 text-yellow-400" 
                            : service.id === "protect" 
                              ? "bg-blue-500/20 text-blue-400" 
                              : "bg-green-500/20 text-green-400"
                        } mr-3 flex-shrink-0`}>
                          <Check className="h-3 w-3" />
                        </span>
                        <span className={`${service.textColor} font-medium`}>{item}</span>
                      </li>
                    ))}
                  </ul>
                  
                  {/* CTA button */}
                  <div className="mt-auto">
                    <a 
                      href={`/${service.id}`}
                      className={`inline-flex items-center gap-2 px-4 py-2 rounded-lg border ${
                        service.id === "measure" 
                          ? "border-yellow-600/40 hover:bg-yellow-600/10 text-yellow-200" 
                          : service.id === "protect" 
                            ? "border-blue-600/40 hover:bg-blue-600/10 text-blue-200" 
                            : "border-green-600/40 hover:bg-green-600/10 text-green-200"
                      } transition-all duration-300`}
                    >
                      <span>Learn More</span>
                      <ExternalLink className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                    </a>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;