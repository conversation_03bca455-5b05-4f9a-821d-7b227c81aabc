import React, { useState, useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  ChevronDown,
  Menu,
  X,
  Phone,
  Mail,
  Gamepad2,
  ArrowUpRight,
  Zap,
  Shield,
  Leaf,
  Users,
  MessageCircle
} from "lucide-react";

interface NavigationProps {
  scrollY: number;
}

// Custom hook for responsive breakpoints
const useResponsiveBreakpoint = () => {
  const [breakpoint, setBreakpoint] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');

  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      if (width < 768) {
        setBreakpoint('mobile');
      } else if (width < 1024) {
        setBreakpoint('tablet');
      } else {
        setBreakpoint('desktop');
      }
    };

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);

  return breakpoint;
};

// Clean and simple mega menu dropdown with attractive styling
const MegaDropdown = ({
  items,
  isOpen,
  color,
  category,
  onItemClick
}: {
  items: { name: string; path: string }[],
  isOpen: boolean,
  color: string,
  category: string,
  onItemClick?: () => void
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [shouldShow, setShouldShow] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const breakpoint = useResponsiveBreakpoint();

  useEffect(() => {
    if (isOpen) {
      setShouldShow(true);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    } else if (!isHovered) {
      timeoutRef.current = setTimeout(() => {
        setShouldShow(false);
      }, 150);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [isOpen, isHovered]);

  if (!shouldShow) return null;

  const getColorConfig = () => {
    switch (color) {
      case 'measure':
        return {
          icon: '!text-yellow-600',
          hover: 'hover:!bg-yellow-50 hover:!text-yellow-700',
          accent: '!bg-yellow-500',
          text: '!text-yellow-600'
        };
      case 'protect':
        return {
          icon: 'text-blue-700',
          hover: 'hover:bg-blue-50 hover:text-blue-800',
          accent: 'bg-blue-500',
          text: 'text-blue-700'
        };
      case 'conserve':
        return {
          icon: 'text-emerald-700',
          hover: 'hover:bg-emerald-50 hover:text-emerald-800',
          accent: 'bg-emerald-500',
          text: 'text-emerald-700'
        };
      case 'about':
        return {
          icon: 'text-purple-700',
          hover: 'hover:bg-purple-50 hover:text-purple-800',
          accent: 'bg-purple-500',
          text: 'text-purple-700'
        };
      case 'contact':
        return {
          icon: 'text-blue-700',
          hover: 'hover:bg-blue-50 hover:text-blue-800',
          accent: 'bg-blue-500',
          text: 'text-blue-700'
        };
      default:
        return {
          icon: 'text-gray-700',
          hover: 'hover:bg-gray-50 hover:text-gray-800',
          accent: 'bg-gray-500',
          text: 'text-gray-700'
        };
    }
  };

  const colorConfig = getColorConfig();

  const getDropdownClasses = () => {
    const isMeasureCategory = category === 'measure';
    const baseClasses = `
      absolute top-full left-1/2 transform -translate-x-1/2
      bg-white/95 backdrop-blur-2xl font-['Open_Sans']
      border border-gray-200/50 shadow-2xl shadow-black/10
      rounded-2xl transition-all duration-200 overflow-hidden z-[120]
      ${isMeasureCategory ? 'max-h-96 overflow-y-auto' : ''}
    `;

    switch (breakpoint) {
      case 'mobile':
        return `${baseClasses} mt-3 w-auto max-w-sm`;
      case 'tablet':
        return `${baseClasses} mt-3 ${isMeasureCategory ? 'w-[400px]' : 'w-[350px]'}`;
      default:
        return `${baseClasses} mt-3 ${isMeasureCategory ? 'w-[450px]' : 'w-[400px]'}`;
    }
  };

  return (
    <motion.div
      className={getDropdownClasses()}
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      transition={{ duration: 0.2, ease: "easeOut" }}
      onMouseEnter={() => {
        setIsHovered(true);
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
      }}
      onMouseLeave={() => {
        setIsHovered(false);
      }}
    >
      {/* Clean menu items with smart layout */}
      <div className="p-4">
        <div className={`${category === 'measure' ? 'grid grid-cols-2 gap-2' : 'space-y-2'}`}>
          {items.map((item, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.15, delay: index * 0.01 }}
            >
              <Link
                to={item.path}
                className={`
                  group flex items-center justify-between px-4 py-3
                  text-gray-900 ${colorConfig.hover}
                  transition-all duration-200
                  rounded-xl
                  font-medium
                  cursor-pointer
                  min-h-[48px]
                  hover:bg-white/80 hover:shadow-md border border-transparent hover:border-white/50
                  ${category === 'measure' ? 'text-sm' : 'text-sm'}
                `}
                onClick={onItemClick}
              >
                <span className="group-hover:translate-x-1 transition-transform duration-200 truncate pr-2 flex-1">
                  {item.name}
                </span>
                <ArrowUpRight className={`opacity-0 group-hover:opacity-100 transition-all duration-200 flex-shrink-0 ${category === 'measure' ? 'w-4 h-4' : 'w-4 h-4'}`} />
              </Link>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Clean Footer CTA */}
      <div className="px-4 py-3 border-t border-gray-100 bg-gray-50">
        <Link
          to={`/${category}`}
          className={`
            w-full flex items-center justify-center px-4 py-2
            rounded-lg text-white font-semibold text-sm
            ${colorConfig.accent} hover:shadow-lg
            transition-all duration-200 transform hover:scale-[1.02]
            group
          `}
          onClick={onItemClick}
        >
          View All {category.charAt(0).toUpperCase() + category.slice(1)}
          <ArrowUpRight className="w-4 h-4 ml-2 group-hover:translate-x-0.5 group-hover:-translate-y-0.5 transition-transform duration-200" />
        </Link>
      </div>
    </motion.div>
  );
};

const Navigation: React.FC<NavigationProps> = ({ scrollY }) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const breakpoint = useResponsiveBreakpoint();

  const isScrolled = scrollY > 20;

  // Close mobile menu when resizing to desktop
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setIsMobileMenuOpen(false);
        setActiveDropdown(null);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Close dropdown when switching to mobile
  useEffect(() => {
    if (breakpoint === 'mobile') {
      setActiveDropdown(null);
    }
  }, [breakpoint]);

  // Lock body scroll when mobile menu is open
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isMobileMenuOpen]);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleMouseEnter = (dropdown: string) => {
    if (breakpoint !== 'mobile') {
      setActiveDropdown(dropdown);
    }
  };

  const handleMouseLeave = () => {
    if (breakpoint !== 'mobile') {
      setActiveDropdown(null);
    }
  };

  // Enhanced menu categories with modern structure
  const menuCategories = {
    measure: {
      name: "Measure",
      items: [
        { name: "Power Quality Analyzers", path: "/measure/power-quality-analyzers" },
        { name: "Thermal Imagers", path: "/measure/thermal-imagers" },
        { name: "Insulation Testers", path: "/measure/insulation-testers" },
        { name: "Oscilloscopes", path: "/measure/oscilloscopes" },
        { name: "Earth Testers", path: "/measure/EarthTesters" },
        { name: "Earth Loop Testers", path: "/measure/earth-loop-testers" },
        { name: "Clamp Meters", path: "/measure/clamp-meters" },
        { name: "Digital Multimeters", path: "/measure/digital-multimeters" },
        { name: "Micro Ohm Meters", path: "/measure/micro-ohmmeters" },
        { name: "Installation Testers", path: "/measure/installation-testers" },
        { name: "Multi Functional Meters", path: "/measure/multi-functional-meters" },
      ]
    },
    protect: {
      name: "Protect",
      items: [
        { name: "Online UPS", path: "/protect/ups" },
        { name: "Servo Stabilizers", path: "/protect/servo-stabilizers" },
        { name: "Static Stabilizers", path: "/protect/static-stabilizers" },
        { name: "Isolation Transformers", path: "/protect/isolation-transformers" },
      ]
    },
    conserve: {
      name: "Conserve",
      items: [
        { name: "On-Premise Systems", path: "/conserve/on-premise-systems" },
        { name: "Cloud Energy Management", path: "/conserve/cloud-energy-management" },
        { name: "Lighting Energy Saver", path: "/conserve/lighting-energy-saver" },
        { name: "Energy Audits", path: "/conserve/energy-audits" },
      ]
    },
    about: {
      name: "About Us",
      items: [
        { name: "Company", path: "/about/company" },
        { name: "Sustainability", path: "/about/sustainability" },
        { name: "Company Events", path: "/about/events" },
      ]
    },
    contact: {
      name: "Contact",
      items: [
        { name: "Contact Sales", path: "/contact/sales" },
        { name: "Technical Services", path: "/contact/service" },
      ]
    }
  };

  return (
    <>
      <header
        className={cn(
          "fixed left-0 right-0 z-[100] transition-all duration-500 font-['Open_Sans']",
          isScrolled
            ? "bg-white/95 shadow-xl shadow-black/5 backdrop-blur-2xl border-b border-gray-100/50"
            : "bg-white/90 backdrop-blur-xl"
        )}
        style={{
          top: '0px'
        }}
      >
        <div className="w-full max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex items-center justify-between py-3 sm:py-4 lg:py-3 relative min-h-[60px] sm:min-h-[70px]">

            {/* Logo - Left side with enhanced styling */}
            <motion.div
              className="flex-shrink-0 z-10"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <Link to="/" className="block">
                <img
                  src="/background_images/logo.png"
                  alt="KRYKARD"
                  className="h-8 sm:h-10 md:h-11 lg:h-12 w-auto object-contain filter drop-shadow-sm"
                  style={{
                    maxWidth: breakpoint === 'mobile' ? "160px" :
                             breakpoint === 'tablet' ? "180px" : "200px"
                  }}
                />
              </Link>
            </motion.div>

            {/* Clean Desktop Navigation - Right side */}
            <div className="hidden lg:flex items-center space-x-2 xl:space-x-3 ml-auto">
              {Object.entries(menuCategories).map(([key, category]) => (
                <div
                  key={key}
                  className="relative"
                  onMouseEnter={() => handleMouseEnter(key)}
                  onMouseLeave={handleMouseLeave}
                >
                  <Link
                    to={`/${key}`}
                    className={cn(
                      "group flex items-center font-semibold text-sm xl:text-base transition-all duration-200 relative px-4 py-2.5 rounded-xl",
                      "hover:shadow-lg hover:shadow-black/5 backdrop-blur-sm",
                      "border border-transparent hover:border-gray-200",
                      "bg-white/60 hover:bg-white/80",
                      key === 'measure' ? '!text-yellow-600 hover:!text-yellow-700' :
                      key === 'protect' ? 'text-blue-700 hover:text-blue-800' :
                      key === 'conserve' ? 'text-emerald-700 hover:text-emerald-800' :
                      key === 'about' ? 'text-purple-700 hover:text-purple-800' :
                      key === 'contact' ? 'text-blue-700 hover:text-blue-800' :
                      'text-gray-700 hover:text-gray-900'
                    )}
                  >
                    <span className="relative z-10">{category.name}</span>
                    <ChevronDown className={cn(
                      "ml-2 h-4 w-4 transition-transform duration-200",
                      activeDropdown === key ? "rotate-180" : ""
                    )} />
                  </Link>

                  <MegaDropdown
                    items={category.items}
                    isOpen={activeDropdown === key}
                    color={key}
                    category={key}
                  />
                </div>
              ))}
            </div>

            {/* Contact Info - Far right blended with UI */}
            <div className="hidden xl:flex flex-col items-start ml-8 space-y-1.5">
              <motion.div
                className="flex items-center group cursor-pointer"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
                <div className="p-2 bg-blue-500 rounded-lg mr-3 shadow-md flex-shrink-0">
                  <Phone className="h-4 w-4 text-white" />
                </div>
                <a
                  href="tel:+919500097966"
                  className="text-blue-700 hover:text-blue-800 transition-colors font-semibold text-sm group-hover:underline whitespace-nowrap"
                >
                  +91 95000 97966
                </a>
              </motion.div>
              <motion.div
                className="flex items-center group cursor-pointer"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
                <div className="p-2 bg-blue-500 rounded-lg mr-3 shadow-md flex-shrink-0">
                  <Mail className="h-4 w-4 text-white" />
                </div>
                <a
                  href="mailto:<EMAIL>"
                  className="text-gray-700 hover:text-gray-800 transition-colors font-medium text-sm group-hover:underline whitespace-nowrap"
                >
                  <EMAIL>
                </a>
              </motion.div>
            </div>

            {/* Enhanced Mobile Menu Button */}
            <motion.button
              className="lg:hidden relative flex items-center justify-center p-3 bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 active:from-blue-700 active:to-indigo-700 rounded-xl shadow-md shadow-blue-500/25 transition-all duration-300 touch-manipulation min-h-[48px] min-w-[48px] z-[110]"
              onClick={toggleMobileMenu}
              aria-label="Toggle menu"
              aria-expanded={isMobileMenuOpen}
              whileTap={{ scale: 0.95 }}
              whileHover={{ scale: 1.05 }}
            >
              <AnimatePresence mode="wait">
                {isMobileMenuOpen ? (
                  <motion.div
                    key="close"
                    initial={{ rotate: -90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: 90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <X className="w-6 h-6 text-white" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="menu"
                    initial={{ rotate: 90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: -90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Menu className="w-6 h-6 text-white" />
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Pulse animation for attention */}
              <div className="absolute inset-0 rounded-xl bg-blue-400 animate-pulse opacity-20"></div>
            </motion.button>
          </nav>
        </div>
      </header>

      {/* Enhanced Mobile Menu */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <>
            {/* Backdrop with blur */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="fixed inset-0 z-[90] bg-black/40 lg:hidden backdrop-blur-md"
              onClick={() => setIsMobileMenuOpen(false)}
            />

            {/* Menu Panel with enhanced design */}
            <motion.div
              initial={{ x: "100%", opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: "100%", opacity: 0 }}
              transition={{
                duration: 0.4,
                ease: [0.23, 1, 0.32, 1],
                staggerChildren: 0.1
              }}
              className={cn(
                "fixed right-0 top-0 bottom-0 z-[95] bg-gradient-to-br from-white via-white to-blue-50/30 shadow-2xl lg:hidden overflow-y-auto",
                "backdrop-blur-2xl border-l border-white/50 font-['Open_Sans']",
                breakpoint === 'mobile'
                  ? "w-full max-w-sm"
                  : "w-[85%] max-w-md"
              )}
              style={{
                maxHeight: '100vh',
                overflowY: 'auto',
                WebkitOverflowScrolling: 'touch'
              }}
            >
              <div className="relative">
                {/* Decorative background pattern */}
                <div className="absolute inset-0 opacity-5">
                  <div className="absolute top-20 right-8 w-32 h-32 bg-blue-500 rounded-full blur-3xl"></div>
                  <div className="absolute bottom-40 left-8 w-24 h-24 bg-purple-500 rounded-full blur-3xl"></div>
                </div>

                <div className={cn(
                  "relative z-10 p-4 sm:p-6",
                  "pt-20 sm:pt-24"
                )}>

                  {/* Enhanced Contact Section */}
                  <motion.div
                    className="mb-6 p-4 sm:p-6 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl shadow-xl text-white"
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.1 }}
                  >
                    <h3 className="font-bold text-lg mb-4 flex items-center">
                      <MessageCircle className="w-5 h-5 mr-2" />
                      Get in Touch
                    </h3>
                    <div className="space-y-3">
                      <motion.a
                        href="tel:+919500097966"
                        className="flex items-center p-3 bg-white/20 backdrop-blur-sm rounded-2xl hover:bg-white/30 transition-all duration-200 touch-manipulation group"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Phone className="h-5 w-5 mr-3 flex-shrink-0" />
                        <span className="font-semibold group-hover:translate-x-1 transition-transform duration-200">
                          +91 95000 97966
                        </span>
                      </motion.a>
                      <motion.a
                        href="mailto:<EMAIL>"
                        className="flex items-center p-3 bg-white/20 backdrop-blur-sm rounded-2xl hover:bg-white/30 transition-all duration-200 touch-manipulation group"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Mail className="h-5 w-5 mr-3 flex-shrink-0" />
                        <span className="font-medium text-sm group-hover:translate-x-1 transition-transform duration-200">
                          <EMAIL>
                        </span>
                      </motion.a>
                    </div>
                  </motion.div>

                  {/* Enhanced Navigation Menu */}
                  <motion.div
                    className="space-y-6"
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.2 }}
                  >
                    {Object.entries(menuCategories).map(([key, category], categoryIndex) => (
                      <motion.div
                        key={key}
                        className="relative"
                        initial={{ x: 50, opacity: 0 }}
                        animate={{ x: 0, opacity: 1 }}
                        transition={{ delay: 0.3 + categoryIndex * 0.1 }}
                      >
                        {/* Category Header */}
                        <Link
                          to={`/${key}`}
                          className={cn(
                            "flex items-center justify-between p-4 rounded-2xl mb-4 touch-manipulation group transition-all duration-300",
                            "backdrop-blur-sm border shadow-lg hover:shadow-xl",
                            key === 'measure' ? 'bg-white border-gray-200/50 !text-yellow-700' :
                            key === 'protect' ? 'bg-white border-gray-200/50 text-blue-800' :
                            key === 'conserve' ? 'bg-white border-gray-200/50 text-emerald-800' :
                            key === 'about' ? 'bg-white border-gray-200/50 text-purple-800' :
                            key === 'contact' ? 'bg-white border-gray-200/50 text-blue-800' :
                            'bg-white border-gray-200/50 text-gray-800'
                          )}
                          onClick={() => setIsMobileMenuOpen(false)}
                        >
                          <div className="flex items-center">
                            <div className={cn(
                              "p-3 rounded-xl mr-4",
                              key === 'measure' ? '!bg-yellow-500 text-white' :
                              key === 'protect' ? 'bg-blue-500 text-white' :
                              key === 'conserve' ? 'bg-emerald-500 text-white' :
                              key === 'about' ? 'bg-purple-500 text-white' :
                              key === 'contact' ? 'bg-blue-500 text-white' :
                              'bg-gray-500 text-white'
                            )}>
                              {key === 'measure' && <Zap className="w-5 h-5" />}
                              {key === 'protect' && <Shield className="w-5 h-5" />}
                              {key === 'conserve' && <Leaf className="w-5 h-5" />}
                              {key === 'about' && <Users className="w-5 h-5" />}
                              {key === 'contact' && <MessageCircle className="w-5 h-5" />}
                            </div>
                            <div>
                              <h3 className="font-bold text-lg">{category.name}</h3>
                              <p className="text-sm opacity-70">{category.items.length} products</p>
                            </div>
                          </div>
                          <ArrowUpRight className="w-5 h-5 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-200" />
                        </Link>

                        {/* Category Items */}
                        <div className="space-y-1 ml-4 border-l-2 border-gray-100 pl-4">
                          {category.items.map((item, index) => (
                            <motion.div
                              key={index}
                              initial={{ x: 20, opacity: 0 }}
                              animate={{ x: 0, opacity: 1 }}
                              transition={{ delay: 0.4 + categoryIndex * 0.1 + index * 0.05 }}
                            >
                              <Link
                                to={item.path}
                                className={cn(
                                  "block p-4 rounded-xl transition-all duration-200 touch-manipulation group relative overflow-hidden min-h-[48px] flex items-center",
                                  "hover:bg-white/80 hover:shadow-md backdrop-blur-sm border border-transparent hover:border-white/50",
                                  key === 'measure' ? '!text-yellow-600 hover:!text-yellow-700' :
                                  key === 'protect' ? 'text-blue-700 hover:text-blue-800' :
                                  key === 'conserve' ? 'text-emerald-700 hover:text-emerald-800' :
                                  key === 'about' ? 'text-purple-700 hover:text-purple-800' :
                                  key === 'contact' ? 'text-blue-700 hover:text-blue-800' :
                                  'text-gray-700 hover:text-gray-800'
                                )}
                                onClick={() => setIsMobileMenuOpen(false)}
                              >
                                <div className="relative z-10 flex items-center justify-between w-full">
                                  <span className="font-medium text-sm group-hover:translate-x-2 transition-transform duration-200 flex-1">
                                    {item.name}
                                  </span>
                                  <div className="opacity-0 group-hover:opacity-100 transition-all duration-200 transform translate-x-2 group-hover:translate-x-0 flex-shrink-0">
                                    <ArrowUpRight className="w-4 h-4" />
                                  </div>
                                </div>
                              </Link>
                            </motion.div>
                          ))}
                        </div>
                      </motion.div>
                    ))}
                  </motion.div>

                  {/* Enhanced Footer CTA */}
                  <motion.div
                    className="mt-8 pt-6 border-t border-gray-200/50"
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.8 }}
                  >
                    <div className="grid grid-cols-2 gap-4">
                      <motion.button
                        className="p-4 bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-2xl font-semibold text-sm shadow-lg hover:shadow-xl transition-all duration-200 group"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => {
                          setIsMobileMenuOpen(false);
                          // Add your quote request logic here
                        }}
                      >
                        <div className="flex items-center justify-center">
                          <span className="group-hover:translate-x-1 transition-transform duration-200">
                            Get Quote
                          </span>
                          <ArrowUpRight className="w-4 h-4 ml-2 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-200" />
                        </div>
                      </motion.button>

                      <motion.button
                        className="p-4 bg-white border-2 border-gray-200 text-gray-700 rounded-2xl font-semibold text-sm hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 group"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => {
                          setIsMobileMenuOpen(false);
                          // Add your support logic here
                        }}
                      >
                        <div className="flex items-center justify-center">
                          <span className="group-hover:translate-x-1 transition-transform duration-200">
                            Support
                          </span>
                          <Phone className="w-4 h-4 ml-2 group-hover:scale-110 transition-transform duration-200" />
                        </div>
                      </motion.button>
                    </div>

                    {/* Social proof or additional info */}
                    <div className="mt-6 text-center">
                      <p className="text-xs text-gray-500 mb-2">Trusted by 1000+ businesses</p>
                      <div className="flex justify-center items-center space-x-2">
                        <div className="flex -space-x-2">
                          {[1, 2, 3, 4].map((i) => (
                            <div key={i} className={cn(
                              "w-6 h-6 rounded-full border-2 border-white",
                              i === 1 ? 'bg-blue-400' :
                              i === 2 ? 'bg-green-400' :
                              i === 3 ? 'bg-purple-400' :
                              'bg-yellow-400'
                            )}></div>
                          ))}
                        </div>
                        <span className="text-xs text-gray-600 font-medium">+996 more</span>
                      </div>
                    </div>
                  </motion.div>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
};

export default Navigation;