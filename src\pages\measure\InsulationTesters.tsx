import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useNavigate, useLocation } from "react-router-dom";
import PageLayout from "@/components/layout/PageLayout";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  ArrowRight,
  Check,
  ChevronRight,
  FileText,
  Zap,
  Shield,
  Award,
  Database,
  BarChart4,
  Clock,
  Gauge,
  Download,
  Phone,
  Mail,
  MapPin,
  ArrowDown,
  ArrowLeft
} from "lucide-react";

// Type Definitions
interface TesterModel {
  id: number;
  title: string;
  modelNumbers: string;
  modelImages: string[];
  features: string[];
  measurements: {
    insulation?: string[];
    voltage?: string[];
    leakageCurrent?: string[];
    capacitance?: string[];
    testVoltageRange?: string[];
  };
  colors?: {
    primary: string;
    secondary: string;
  };
}

// PDF URL for brochure
const PDF_URL = "/T&M April 2025.pdf";

// Modern Background Component
const ModernBackground = () => {
  return (
    <div className="fixed inset-0 pointer-events-none z-[-1] overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Abstract shapes */}
      <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-yellow-100 rounded-bl-full opacity-30"></div>
      <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-yellow-200 rounded-tr-full opacity-20"></div>

      {/* Subtle grid pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
    </div>
  );
};



// Enhanced Hero Section Component with updated buttons
const HeroSection = ({ onRequestDemo, onViewBrochure }: { onRequestDemo: () => void; onViewBrochure: () => void }) => {
  return (
    <div className="relative py-6 md:py-12 overflow-hidden font-['Open_Sans']">
      {/* Hero Background Elements - Mobile optimized */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 right-0 w-1/2 md:w-3/4 h-full bg-yellow-50 rounded-bl-[50px] md:rounded-bl-[100px] transform -skew-x-6 md:-skew-x-12"></div>
        <div className="absolute bottom-20 left-0 w-32 h-32 md:w-64 md:h-64 bg-yellow-400 rounded-full opacity-10"></div>
      </div>

      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 items-center">
          {/* Text Content */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-4 text-center lg:text-left"
          >
            <div className="inline-block bg-yellow-400 py-1 px-3 rounded-full mb-2">
              <span className="text-sm md:text-base font-semibold text-gray-900 font-['Open_Sans']">KRYKARD Precision Instruments</span>
            </div>
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight font-['Open_Sans']">
              INSULATION <span className="text-yellow-400">TESTERS</span>
            </h1>

            <p className="text-base md:text-lg lg:text-xl text-gray-900 leading-relaxed font-medium text-center lg:text-justify max-w-lg mx-auto lg:mx-0 font-['Open_Sans']">
              Professional-grade insulation testers for precise measurements and reliable results in electrical maintenance and troubleshooting.
            </p>

            <div className="pt-4 flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center lg:justify-start max-w-md sm:max-w-none mx-auto lg:mx-0">
              <Button
                className="w-full sm:w-auto px-4 sm:px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-semibold rounded-lg shadow-md transition duration-300 flex items-center justify-center space-x-2 text-sm sm:text-base"
                onClick={onRequestDemo}
              >
                <span>Request Demo</span>
                <ArrowRight className="ml-2 h-4 sm:h-5 w-4 sm:w-5" />
              </Button>
              <Button
                className="w-full sm:w-auto px-4 sm:px-6 py-3 bg-white border-2 border-yellow-400 text-gray-900 font-semibold rounded-lg shadow-sm transition duration-300 hover:bg-gray-50 flex items-center justify-center space-x-2 text-sm sm:text-base"
                onClick={onViewBrochure}
              >
                <span>View Brochure</span>
                <FileText className="ml-2 h-4 sm:h-5 w-4 sm:w-5" />
              </Button>
            </div>
          </motion.div>

          {/* Product Image */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative order-first lg:order-last"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-200 to-yellow-50 rounded-full opacity-20 blur-xl transform scale-90"></div>
            <motion.div
              animate={{
                y: [0, -10, 0],
              }}
              transition={{
                repeat: Infinity,
                duration: 3,
                ease: "easeInOut"
              }}
              className="relative z-10 flex justify-center"
            >
              <img
                src="/insulation testers/C.A-6131.png"
                alt="Krykard Insulation Tester"
                className="max-h-[300px] sm:max-h-[400px] md:max-h-[500px] lg:max-h-[600px] w-auto object-contain drop-shadow-2xl"
              />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

// Quick Stats Section Component
const QuickStatsSection = () => {
  return (
    <div className="bg-white py-6 md:py-8 relative z-10 font-['Open_Sans']">
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
          <motion.div
            className="p-4 md:p-6 rounded-xl border border-yellow-100 bg-white shadow-md flex flex-col items-center text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            whileHover={{ y: -5, boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)" }}
          >
            <div className="bg-yellow-50 p-3 rounded-full mb-3">
              <Shield size={24} className="text-yellow-500" />
            </div>
            <h3 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-1 font-['Open_Sans']">99.8%</h3>
            <p className="text-gray-900 font-semibold text-sm md:text-base font-['Open_Sans']">Accuracy Rating</p>
          </motion.div>

          <motion.div
            className="p-4 md:p-6 rounded-xl border border-yellow-100 bg-white shadow-md flex flex-col items-center text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
            whileHover={{ y: -5, boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)" }}
          >
            <div className="bg-yellow-50 p-3 rounded-full mb-3">
              <Gauge size={24} className="text-yellow-500" />
            </div>
            <h3 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-1 font-['Open_Sans']">15kV</h3>
            <p className="text-gray-900 font-semibold text-sm md:text-base font-['Open_Sans']">Maximum Test Voltage</p>
          </motion.div>

          <motion.div
            className="p-4 md:p-6 rounded-xl border border-yellow-100 bg-white shadow-md flex flex-col items-center text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            whileHover={{ y: -5, boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)" }}
          >
            <div className="bg-yellow-50 p-3 rounded-full mb-3">
              <Database size={24} className="text-yellow-500" />
            </div>
            <h3 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-1 font-['Open_Sans']">80K+</h3>
            <p className="text-gray-900 font-semibold text-sm md:text-base font-['Open_Sans']">Data Storage Points</p>
          </motion.div>

          <motion.div
            className="p-4 md:p-6 rounded-xl border border-yellow-100 bg-white shadow-md flex flex-col items-center text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
            whileHover={{ y: -5, boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)" }}
          >
            <div className="bg-yellow-50 p-3 rounded-full mb-3">
              <Award size={24} className="text-yellow-500" />
            </div>
            <h3 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-1 font-['Open_Sans']">10+</h3>
            <p className="text-gray-900 font-semibold text-sm md:text-base font-['Open_Sans']">International Certifications</p>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

// Feature Highlight Component with improved styling
const FeatureHighlight = ({ icon, title, description }: { icon: React.ReactNode; title: string; description: string }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
      whileHover={{ y: -8, boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)" }}
      className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 p-4 h-full border-b-4 border-yellow-400 font-['Open_Sans']"
    >
      <div className="flex flex-col h-full text-center md:text-left">
        <div className="bg-gradient-to-br from-yellow-400 to-yellow-300 w-12 h-12 rounded-lg flex items-center justify-center mb-3 shadow-md mx-auto md:mx-0">
          {icon}
        </div>
        <h3 className="text-base md:text-lg lg:text-xl font-bold text-gray-900 mb-2 font-['Open_Sans']">{title}</h3>
        <p className="text-gray-900 flex-grow font-medium text-sm md:text-base text-center md:text-justify font-['Open_Sans']">{description}</p>
      </div>
    </motion.div>
  );
};

// Animated Product Card
const ProductCard = ({
  product,
  onViewDetails,
  colors = {
    primary: 'yellow-500',
    secondary: 'yellow-50'
  }
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="group h-full font-['Open_Sans']"
    >
      <div className={`h-full rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 bg-white border border-gray-100`}>
        {/* Product Image with Colored Background */}
        <div className={`relative p-4 md:p-6 flex justify-center items-center bg-yellow-50 h-48 md:h-56 overflow-hidden group-hover:bg-opacity-80 transition-all duration-700`}>
          <motion.img
            src={product.modelImages[0]}
            alt={product.title}
            className="h-36 md:h-48 object-contain z-10 drop-shadow-xl"
            whileHover={{ rotate: [-1, 1, -1], transition: { repeat: Infinity, duration: 2 } }}
          />
          <div className={`absolute top-2 left-2 md:top-4 md:left-4 bg-yellow-400 text-white text-xs md:text-sm font-bold py-1 px-2 md:px-3 rounded-full`}>
            {product.modelNumbers}
          </div>
        </div>

        {/* Product Content */}
        <div className="p-4 space-y-3 font-['Open_Sans']">
          <h3 className="text-base md:text-lg lg:text-xl font-bold text-gray-900 group-hover:text-yellow-600 transition-colors duration-300 text-center md:text-left font-['Open_Sans']">
            {product.title}
          </h3>

          {/* Key Features */}
          <div className="space-y-2">
            {product.features.slice(0, 3).map((feature: string, idx: number) => (
              <div key={idx} className="flex items-start">
                <Check className="h-4 w-4 text-yellow-400 mt-1 mr-2 flex-shrink-0" />
                <span className="text-gray-900 text-sm md:text-base font-semibold text-justify font-['Open_Sans']">{feature}</span>
              </div>
            ))}
          </div>

          {/* View Details Button */}
          <Button
            onClick={() => onViewDetails(product.id)}
            className={`w-full mt-4 py-3 px-4 bg-yellow-400 hover:bg-yellow-500 text-center font-semibold text-gray-900 rounded-lg transition-all duration-300 transform group-hover:-translate-y-1 flex items-center justify-center text-sm md:text-base`}
          >
            <span>View Details</span>
            <ChevronRight className="ml-1 h-4 w-4" />
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

// Product Detail View with enhanced UI
const ProductDetailView = ({ product, onBackToList }: { product: TesterModel; onBackToList: () => void }) => {
  const [activeTab, setActiveTab] = useState("features");

  return (
    <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
      <div className="p-6">
        <button
          onClick={onBackToList}
          className="flex items-center text-yellow-600 hover:text-yellow-700 mb-6"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Insulation Testers
        </button>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-200 to-yellow-50 rounded-full opacity-30 blur-xl transform scale-90"></div>
            <motion.div
              animate={{ y: [0, -10, 0] }}
              transition={{ repeat: Infinity, duration: 3, ease: "easeInOut" }}
              className="relative z-10 bg-yellow-50 p-4 sm:p-6 rounded-lg flex items-center justify-center"
            >
              <img
                src={product.modelImages[0]}
                alt={product.title}
                className="max-h-48 sm:max-h-56 md:max-h-64 w-auto object-contain drop-shadow-xl transform transition-transform duration-500 hover:scale-110"
              />
            </motion.div>
            <div className="mt-4 text-center">
              <span className="bg-yellow-500 px-3 py-1 rounded-full text-sm font-medium text-white">
                {product.modelNumbers}
              </span>
            </div>
          </div>

          <div>
            <h1 className="text-xl sm:text-2xl font-bold mb-2 text-gray-900">{product.title}</h1>
            <div className="w-16 h-1 bg-yellow-500 mb-4"></div>
            <p className="text-gray-800 mb-6 text-sm sm:text-base">Professional-grade insulation tester with advanced features for reliable measurements and efficient testing workflows.</p>

            <div className="flex border-b border-gray-200 mb-6">
              <button
                className={`py-2 px-2 sm:px-4 font-medium text-sm sm:text-base ${
                  activeTab === "features"
                    ? "border-b-2 border-yellow-500 text-yellow-600"
                    : "text-gray-500 hover:text-gray-700"
                }`}
                onClick={() => setActiveTab("features")}
              >
                Features
              </button>
              <button
                className={`py-2 px-2 sm:px-4 font-medium text-sm sm:text-base ${
                  activeTab === "specifications"
                    ? "border-b-2 border-yellow-500 text-yellow-600"
                    : "text-gray-500 hover:text-gray-700"
                }`}
                onClick={() => setActiveTab("specifications")}
              >
                Specifications
              </button>
            </div>
          </div>
        </div>

        <div className="mt-8">
          {activeTab === "features" && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h2 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                <span className="w-2 h-6 bg-yellow-500 rounded-full mr-2"></span>
                Key Features
              </h2>
              <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2">
                {product.features.map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className="bg-yellow-50 p-3 sm:p-4 rounded-lg flex items-start hover:bg-yellow-100 hover:shadow-md transition-all duration-300"
                  >
                    <div className="bg-yellow-500 p-1 rounded-full mr-2 sm:mr-3 mt-0.5 flex-shrink-0">
                      <Check className="h-4 w-4 text-white" />
                    </div>
                    <span className="text-gray-900 text-sm sm:text-base font-medium">{feature}</span>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {activeTab === "specifications" && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h2 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                <span className="w-2 h-6 bg-yellow-500 rounded-full mr-2"></span>
                Specifications
              </h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 md:gap-8">
                {product.measurements.insulation && (
                  <div className="bg-yellow-50 p-3 sm:p-4 rounded-lg">
                    <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-2 sm:mb-3 pb-2 border-b border-yellow-200 flex items-center">
                      <Zap className="mr-2 text-yellow-500 flex-shrink-0" size={16} />
                      <span className="truncate">Insulation</span>
                    </h3>
                    <ul className="space-y-2 sm:space-y-3">
                      {product.measurements.insulation.map((item, index) => (
                        <li key={index} className="text-gray-900 text-sm sm:text-base flex items-start font-medium">
                          <span className="text-yellow-500 mr-2 mt-1 flex-shrink-0">•</span>
                          <span>{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {product.measurements.voltage && (
                  <div className="bg-yellow-50 p-3 sm:p-4 rounded-lg">
                    <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-2 sm:mb-3 pb-2 border-b border-yellow-200 flex items-center">
                      <Gauge className="mr-2 text-yellow-500 flex-shrink-0" size={16} />
                      <span className="truncate">Voltage</span>
                    </h3>
                    <ul className="space-y-2 sm:space-y-3">
                      {product.measurements.voltage.map((item, index) => (
                        <li key={index} className="text-gray-900 text-sm sm:text-base flex items-start font-medium">
                          <span className="text-yellow-500 mr-2 mt-1 flex-shrink-0">•</span>
                          <span>{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {product.measurements.testVoltageRange && (
                  <div className="bg-yellow-50 p-3 sm:p-4 rounded-lg">
                    <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-2 sm:mb-3 pb-2 border-b border-yellow-200 flex items-center">
                      <BarChart4 className="mr-2 text-yellow-500 flex-shrink-0" size={16} />
                      <span className="truncate">Test Voltage Range</span>
                    </h3>
                    <ul className="space-y-2 sm:space-y-3">
                      {product.measurements.testVoltageRange.map((item, index) => (
                        <li key={index} className="text-gray-900 text-sm sm:text-base flex items-start font-medium">
                          <span className="text-yellow-500 mr-2 mt-1 flex-shrink-0">•</span>
                          <span>{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {product.measurements.leakageCurrent && (
                  <div className="bg-yellow-50 p-3 sm:p-4 rounded-lg">
                    <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-2 sm:mb-3 pb-2 border-b border-yellow-200 flex items-center">
                      <Zap className="mr-2 text-yellow-500 flex-shrink-0" size={16} />
                      <span className="truncate">Leakage Current</span>
                    </h3>
                    <ul className="space-y-2 sm:space-y-3">
                      {product.measurements.leakageCurrent.map((item, index) => (
                        <li key={index} className="text-gray-900 text-sm sm:text-base flex items-start font-medium">
                          <span className="text-yellow-500 mr-2 mt-1 flex-shrink-0">•</span>
                          <span>{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {product.measurements.capacitance && (
                  <div className="bg-yellow-50 p-3 sm:p-4 rounded-lg">
                    <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-2 sm:mb-3 pb-2 border-b border-yellow-200 flex items-center">
                      <Database className="mr-2 text-yellow-500 flex-shrink-0" size={16} />
                      <span className="truncate">Capacitance</span>
                    </h3>
                    <ul className="space-y-2 sm:space-y-3">
                      {product.measurements.capacitance.map((item, index) => (
                        <li key={index} className="text-gray-900 text-sm sm:text-base flex items-start font-medium">
                          <span className="text-yellow-500 mr-2 mt-1 flex-shrink-0">•</span>
                          <span>{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
};

// Testimonial Component
const Testimonial = ({ quote, author, role, company, delay = 0 }: { quote: string; author: string; role: string; company: string; delay?: number }) => {
  return (
    <motion.div
      className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100 relative"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay }}
      whileHover={{ y: -10 }}
    >
      <div className="absolute -top-5 left-8 text-yellow-500 text-6xl">"</div>
      <div className="relative z-10 pt-4">
        <p className="text-gray-700 mb-6">{quote}</p>
        <div className="flex items-center">
          <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mr-4">
            <span className="text-yellow-700 font-bold">{author.split(' ').map(name => name[0]).join('')}</span>
          </div>
          <div>
            <h4 className="font-bold text-gray-900">{author}</h4>
            <p className="text-gray-600 text-sm">{role}, {company}</p>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// Contact Section Component
const ContactSection = ({ onContactClick }: { onContactClick: () => void }) => {
  return (
    <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 py-6 px-4 rounded-2xl shadow-lg font-['Open_Sans']">
      <div className="max-w-full mx-auto text-center">
        <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-black mb-3 font-['Open_Sans']">Need More Information?</h2>
        <p className="text-black mb-6 max-w-4xl mx-auto font-semibold text-sm md:text-base lg:text-lg text-center font-['Open_Sans']">
          Our team of experts is ready to help you with product specifications, custom solutions,
          pricing, and any other details you need about KRYKARD Insulation Testers.
        </p>
        <Button
          className="inline-flex items-center px-6 md:px-8 py-3 md:py-4 bg-yellow-400 hover:bg-yellow-500 text-black font-semibold rounded-lg shadow-md transition duration-300 transform hover:-translate-y-1 text-sm md:text-base"
          onClick={onContactClick}
        >
          Contact Our Experts
          <ArrowRight className="ml-2 h-4 md:h-5 w-4 md:w-5" />
        </Button>
      </div>
    </div>
  );
};

// FAQ Component
const FAQItem = ({ question, answer }: { question: string; answer: string }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="bg-white rounded-xl shadow-md overflow-hidden">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex justify-between items-center p-6 w-full text-left"
      >
        <h3 className="text-lg font-medium text-gray-900">{question}</h3>
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.3 }}
        >
          <ArrowDown className="h-5 w-5 text-yellow-500" />
        </motion.div>
      </button>

      {isOpen && (
        <div className="p-6 pt-0 text-gray-800 border-t border-gray-100">
          <p className="font-medium">{answer}</p>
        </div>
      )}
    </div>
  );
};

// Application Card Component
const ApplicationCard = ({ icon, title, description }: { icon: React.ReactNode; title: string; description: string }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      viewport={{ once: true }}
      className="bg-white p-4 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-2 border-b-4 border-yellow-400 text-center md:text-left"
    >
      <div className="bg-yellow-100 w-12 md:w-16 h-12 md:h-16 rounded-lg flex items-center justify-center mb-3 text-yellow-600 mx-auto md:mx-0">
        {icon}
      </div>
      <h3 className="text-base md:text-lg lg:text-xl font-semibold mb-2 text-gray-900 font-['Open_Sans']">{title}</h3>
      <p className="text-gray-900 font-semibold text-sm md:text-base text-center md:text-justify font-['Open_Sans']">{description}</p>
    </motion.div>
  );
};

// Insulation Testers Models Data
const insulationTesters = [
  {
    id: 1,
    title: '1kV Insulation Testers',
    modelNumbers: 'CA 6522/CA 6528',
    modelImages: ['/insulation testers/C.A-6522.png'],
    features: [
      'Display : 4000 counts, double digital backlit LCD screen',
      'Logarithmic bargraph (CA 6522)',
      'Continuity at 200mA',
      'CA 6528 : 0.02Ω - 40Ω CA 6522 : 0.00Ω - 10.00Ω',
      'Visual alarm : Blue/Red backlighting (CA 6528)',
      'Timer (mins) : Upto 39:59',
      'Automatic shutdown, Hold,Manual / Lock / Duration modes',
      'IP 40 (CA 6528) , IP 54 (CA 6522)'
    ],
    measurements: {
      insulation: [
        'Test Voltage : 250 V, 500 V, 1000 V',
        'Range/Accuracy :',
        'CA 6522 : 50 kΩ to 40 GΩ /± (3% +2 counts)',
        'CA 6528 : 50 kΩ to 11GΩ / ± (1.5% R+10 pt)'
      ],
      voltage: [
        'Range : Upto 700 V'
      ]
    },
    colors: {
      primary: 'yellow-500',
      secondary: 'yellow-50'
    }
  },
  {
    id: 2,
    title: '1kV Insulation Testers',
    modelNumbers: 'CA 6524/CA 6526',
    modelImages: ['/insulation testers/C.A-6522.png'],
    features: [
      'Display : 4000 counts, double + bargraph',
      'Continuity at 200mA (0.00Ω - 10.00Ω)/ 20mA (0.0Ω - 100.0Ω)',
      'PI & DAR',
      'Visual Pass/Fail : Red/Green (CA 6526)',
      'Timer (mins) : Upto 39:59',
      'Configurable alarms',
      'Memory :',
      '300 measurements (CA 6524)',
      '1300 measurements (CA 6526)',
      'Communication :',
      'Bluetooth (CA 6526)'
    ],
    measurements: {
      insulation: [
        'Test Voltage : 50 V, 100 V, 250 V, 500 V, 1000 V',
        'Test current : 0.01μA to 2mA',
        'Range : 10 kΩ to 200 GΩ',
        'Accuracy : ± (3% +2 counts)'
      ],
      voltage: [
        'Range : Upto 700 V'
      ],
      capacitance: [
        'Range : 0.1 nF to 10 μF (CA 6526)'
      ]
    },
    colors: {
      primary: 'yellow-600',
      secondary: 'yellow-100'
    }
  },
  {
    id: 3,
    title: 'Insulation Testers Special Models',
    modelNumbers: 'CA 6532/CA 6534/CA 6536',
    modelImages: ['/insulation testers/6532-removebg-preview.png'],
    features: [
      'Display : 4000 counts, double + bargraph',
      'PI & DAR (CA 6532)',
      'Continuity at 200mA (0.00Ω - 10.00Ω)/ 20mA (0.0Ω - 100.0Ω)',
      'Timer (mins) : Upto 39:59',
      'Memory :',
      '1300 records (CA 6532 & 6534)',
      'Communication :',
      'Bluetooth (CA 6532 & 6534)'
    ],
    measurements: {
      testVoltageRange: [
        'Test Voltage/Insulation Range',
        'CA 6532 : 50 V, 100 V/10 kΩ to 20 GΩ for Telecommunications (maximum 100 Km line length)',
        'CA 6534 : 10 V, 25 V, 100 V, 250 V, 500 V/ 2 kΩ to 50 GΩ for electronics industries & electrostatic discharge',
        'CA 6536 :',
        '10 V to 100 V in 1V step/2 kΩ to 20 GΩ for Avionics, Space & Defense',
        'Test current : 0.01μA to 2mA',
        'Accuracy : ± (3% +2 counts)'
      ],
      voltage: [
        'Range : Upto 700 V'
      ],
      capacitance: [
        'Range : 0.1 nF to 10 nF (CA 6532)'
      ]
    },
    colors: {
      primary: 'yellow-600',
      secondary: 'yellow-50'
    }
  },
  {
    id: 4,
    title: '5kV Insulation Testers',
    modelNumbers: 'CA 6505/CA 6545',
    modelImages: ['/insulation testers/C.A-6505.png'],
    features: [
      'Display : Backlit LCD graphic display with bargraph',
      'Programmable test duration',
      'Automatic calculation of the DAR/PI',
      'DD calculation (CA 6545)',
      'Locking test voltage',
      'Programmable Alarms (CA 6545)',
      'Smoothing of Display (CA 6545)',
      'Automatic detection of the presence of AC or DC external voltage on terminals',
      'Auto power save mode to save battery power'
    ],
    measurements: {
      insulation: [
        'Test voltage (Fixed/Adjustable) : 500 V, 1000 V, 2500 V, 5000 V/40 V to 5100 V in 10 V or 100 V increments',
        'Range : 10 kΩ to 10 TΩ',
        'Accuracy : ± 5% +3 pts'
      ],
      voltage: [
        'Range: Upto 5100 V'
      ],
      leakageCurrent: [
        'Upto 3mA'
      ],
      capacitance: [
        'Upto 49.99 F'
      ]
    },
    colors: {
      primary: 'yellow-500',
      secondary: 'yellow-50'
    }
  },
  {
    id: 5,
    title: '5kV Insulation Testers',
    modelNumbers: 'CA 6547/CA 6549',
    modelImages: ['/insulation testers/C.A-6541.png'],
    features: [
      'Large backlit LCD screen, with digital display & bargraph',
      'Automatic calculation of the DAR/PI/DD ratios',
      'Programmable alarms',
      'Displays a error code in an anamoly condition (CA 6549)',
      'Direct plotting resistance over time (R(t)) curves in display (CA 6549)',
      'Calculation of R at Reference Temperature (T°) (CA 6549)',
      'Memory : 128KB storage capacity',
      'Communication : USB (Two-Way)',
      'PC interface'
    ],
    measurements: {
      insulation: [
        'Test Voltage (Fixed/Adjustable) : 500 V, 1000 V, 2500 V, 5000 V/40 V to 5100 V in 10 V or 100 V increments',
        'Range : 10 kΩ to 10 TΩ',
        'Accuracy : ±5% +3 pts'
      ],
      voltage: [
        'Range : Upto 5100 V'
      ],
      leakageCurrent: [
        'Range : Upto 3mA'
      ],
      capacitance: [
        'Range : Upto 49.99 F'
      ]
    },
    colors: {
      primary: 'yellow-600',
      secondary: 'yellow-100'
    }
  },
  {
    id: 6,
    title: '10kV/15kV Insulation Testers',
    modelNumbers: 'CA 6550/CA 6555',
    modelImages: ['/insulation testers/C.A-6470N.png'],
    features: [
      'Large graphical LCD display with backlight & bargraph',
      'Calculation of the DAR/PI/DD ratios',
      'Programmable test duration',
      'Timer (mins) : Upto 99:59',
      'Voltage ramp & step with " burn - in", "early break" & "I - limit" modes',
      'Memory : 80,000 points',
      'Communication :USB',
      'PC interface'
    ],
    measurements: {
      testVoltageRange: [
        'Test Voltage/Insulation range',
        'CA 6550 : 500 V, 1000 V, 2500 V, 5000 V, 10,000 V/10 kΩ to 25 TΩ',
        'CA 6555 : 500 V, 1000 V, 2500 V, 5000 V, 10,000 V, 15,000 V/10 kΩ to 30 TΩ',
        'Accuracy : ±5% +3 pts'
      ],
      voltage: [
        'Range : Upto 2500 V AC/Upto 4000 V DC'
      ],
      leakageCurrent: [
        'Range : Upto 8mA'
      ],
      capacitance: [
        'Range : Upto 19.99 μF'
      ]
    },
    colors: {
      primary: 'yellow-600',
      secondary: 'yellow-50'
    }
  }
];

// Main Insulation Testers Component
const InsulationTesters = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const [activeFilter, setActiveFilter] = useState("all");
  const [selectedTesterId, setSelectedTesterId] = useState(null);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // Get the selected tester based on ID
  const selectedTester = insulationTesters.find(tester => tester.id === selectedTesterId);

  // Filter testers based on active filter
  const filteredTesters = activeFilter === "all"
    ? insulationTesters
    : insulationTesters.filter(tester => {
        if (activeFilter === "5kv") return tester.title.includes("5kV");
        if (activeFilter === "10kv") return tester.title.includes("10kV");
        if (activeFilter === "1kv") return tester.title.includes("1kV");
        if (activeFilter === "special") return tester.title.includes("Special");
        return true;
      });

  // Effect to check URL parameters for initial tab
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const tab = params.get('tab');
    const filter = params.get('filter');
    const productId = params.get('product');

    if (tab) setActiveTab(tab);
    if (filter) setActiveFilter(filter);
    if (productId) {
      const id = parseInt(productId, 10);
      if (!isNaN(id)) {
        setSelectedTesterId(id);
      }
    }
  }, [location]);

  // Handler for Request Demo button
  const handleRequestDemo = () => {
    navigate("/contact/sales");
  };

  // Handler for View Brochure button
  const handleViewBrochure = () => {
    window.open(PDF_URL, '_blank');
  };  // Handle view details for a specific tester
  const handleViewDetails = (testerId: number) => {
    setSelectedTesterId(testerId);
    setActiveTab("details");
    navigate(`?tab=details&product=${testerId}`, { replace: true });
    // Removed scrolling behavior to directly show the product details
  };

  // Handle back to products list
  const handleBackToList = () => {
    setSelectedTesterId(null);
    navigate(`?tab=products${activeFilter !== "all" ? `&filter=${activeFilter}` : ''}`, { replace: true });
  };

  // Navigation tabs data
  const navTabs = [
    { id: "overview", label: "Overview", icon: <Gauge className="h-5 w-5" /> },
    { id: "products", label: "Products", icon: <Zap className="h-5 w-5" /> },
    { id: "details", label: "Product Details", icon: <Database className="h-5 w-5" /> },
    { id: "applications", label: "Applications", icon: <Shield className="h-5 w-5" /> }
  ];

  return (
    <PageLayout
      title="Insulation Testers"
      subtitle=""
      category="measure"
    >
      <style>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
      <div className="font-['Open_Sans']">
      {/* Modern Background */}
      <ModernBackground />

      {/* Premium Modern Navigation Tabs - Responsive Design */}
      <div className="sticky top-0 z-30 bg-white shadow-lg border-b border-gray-100 font-['Open_Sans']">
        <div className="max-w-full mx-auto px-2">
          {/* Desktop Navigation */}
          <div className="hidden md:flex justify-center py-2">
            <div className="bg-gray-50 p-1.5 rounded-full flex shadow-sm">
              {navTabs.map(tab => {
                // Skip the Details tab if no product is selected
                if (tab.id === "details" && !selectedTesterId) return null;

                return (
                  <button
                    key={tab.id}
                    onClick={() => {
                      setActiveTab(tab.id);
                      if (tab.id === "products") {
                        navigate(`?tab=${tab.id}${activeFilter !== "all" ? `&filter=${activeFilter}` : ''}`, { replace: true });
                      } else if (tab.id === "details" && selectedTesterId) {
                        navigate(`?tab=${tab.id}&product=${selectedTesterId}`, { replace: true });
                      } else {
                        navigate(`?tab=${tab.id}`, { replace: true });
                      }
                      setIsMobileMenuOpen(false);
                    }}
                    className={cn(
                      "relative px-6 py-2.5 font-medium rounded-full transition-all duration-300 flex items-center mx-1 overflow-hidden text-base",
                      activeTab === tab.id
                        ? "bg-gradient-to-r from-yellow-400 to-yellow-400 text-black shadow-md transform -translate-y-0.5"
                        : "text-gray-700 hover:bg-yellow-50 hover:text-yellow-500"
                    )}
                  >
                    <div className="flex items-center relative z-10">
                      <span className="mr-2">{tab.icon}</span>
                      <span>{tab.label}</span>
                    </div>
                    {activeTab === tab.id && (
                      <motion.div
                        layoutId="navIndicator"
                        className="absolute inset-0 bg-gradient-to-r from-yellow-400 to-yellow-400 -z-0"
                        initial={false}
                        transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                      />
                    )}
                  </button>
                );
              })}
            </div>
          </div>

          {/* Mobile Navigation */}
          <div className="md:hidden py-2 flex justify-between items-center">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="text-gray-700 hover:text-yellow-500 focus:outline-none"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            <span className="font-semibold text-gray-900 text-lg">
              {navTabs.find(tab => tab.id === activeTab)?.label}
            </span>

            <div className="w-6"></div> {/* Spacer for balanced layout */}
          </div>

          {/* Mobile Menu Dropdown */}
          <div className={`md:hidden overflow-hidden transition-all duration-300 ease-in-out ${isMobileMenuOpen ? 'max-h-60' : 'max-h-0'}`}>
            <div className="bg-white rounded-lg shadow-lg p-2 mb-2">
              {navTabs.map(tab => {
                // Skip the Details tab if no product is selected
                if (tab.id === "details" && !selectedTesterId) return null;

                return (
                  <button
                    key={tab.id}
                    onClick={() => {
                      setActiveTab(tab.id);
                      if (tab.id === "products") {
                        navigate(`?tab=${tab.id}${activeFilter !== "all" ? `&filter=${activeFilter}` : ''}`, { replace: true });
                      } else if (tab.id === "details" && selectedTesterId) {
                        navigate(`?tab=${tab.id}&product=${selectedTesterId}`, { replace: true });
                      } else {
                        navigate(`?tab=${tab.id}`, { replace: true });
                      }
                      setIsMobileMenuOpen(false);
                    }}
                    className={`w-full text-left px-4 py-3 rounded-lg my-1 flex items-center ${
                      activeTab === tab.id
                        ? "bg-yellow-50 text-yellow-600 font-medium"
                        : "text-gray-700 hover:bg-gray-50"
                    }`}
                  >
                    <span className="mr-3">{tab.icon}</span>
                    {tab.label}
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {activeTab === "overview" && (
        <>
          {/* Hero Section */}
          <HeroSection
            onRequestDemo={handleRequestDemo}
            onViewBrochure={handleViewBrochure}
          />

          {/* Quick Stats Section */}
          <QuickStatsSection />

          {/* Key Features Section */}
          <div className="py-6 md:py-8 bg-gradient-to-br from-yellow-50 to-white font-['Open_Sans']">
            <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-6 md:mb-8">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                  viewport={{ once: true }}
                >
                  <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-yellow-600 mb-3 font-['Open_Sans']">Why Choose Our Insulation Testers?</h2>
                  <p className="mt-3 text-sm md:text-base lg:text-lg text-gray-800 max-w-4xl mx-auto font-medium text-center font-['Open_Sans']">
                    Professional insulation testers designed for precision and reliability in the most demanding environments
                  </p>
                </motion.div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
                <FeatureHighlight
                  icon={<Shield size={24} className="text-white" />}
                  title="Superior Accuracy"
                  description="High-precision testing with accuracy up to ±3% for reliable insulation resistance measurements."
                />

                <FeatureHighlight
                  icon={<Zap size={24} className="text-white" />}
                  title="Versatile Voltage Ranges"
                  description="Test voltages from 10V to 15kV to cover all your insulation testing requirements."
                />

                <FeatureHighlight
                  icon={<Award size={24} className="text-white" />}
                  title="Professional-Grade Quality"
                  description="Robust construction and reliability for industrial and field applications."
                />

                <FeatureHighlight
                  icon={<Database size={24} className="text-white" />}
                  title="Advanced Data Storage"
                  description="Store up to 80,000 measurement points with easy data retrieval and analysis."
                />

                <FeatureHighlight
                  icon={<BarChart4 size={24} className="text-white" />}
                  title="Comprehensive Analysis"
                  description="Automatic calculation of DAR/PI/DD ratios for detailed insulation health assessment."
                />

                <FeatureHighlight
                  icon={<Clock size={24} className="text-white" />}
                  title="Time-Saving Features"
                  description="Programmable test durations, timers, and auto-saving for efficient workflow."
                />
              </div>
            </div>
          </div>

          {/* Featured Products Section */}
          <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6 md:py-8 font-['Open_Sans']">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-6 md:mb-8"
            >
              <span className="inline-block bg-yellow-100 text-yellow-800 px-4 py-1 rounded-full text-sm md:text-base font-semibold mb-3 font-['Open_Sans']">
                PROFESSIONAL SERIES
              </span>
              <h2 className="text-xl md:text-2xl lg:text-4xl font-bold mb-4 text-gray-900 font-['Open_Sans']">
                Complete Insulation Tester Range
              </h2>
              <p className="max-w-4xl mx-auto text-gray-800 text-sm md:text-base lg:text-lg font-medium text-center font-['Open_Sans']">
                Explore our comprehensive lineup of professional insulation testers for various voltage ranges and applications
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
              {insulationTesters.map(tester => (
                <ProductCard
                  key={tester.id}
                  product={tester}
                  onViewDetails={handleViewDetails}
                  colors={tester.colors}
                />
              ))}
            </div>
          </div>

          {/* Testimonials Section
          <div className="bg-gray-50 py-20">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <motion.div
                className="text-center mb-16"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">What Our Customers Say</h2>
                <div className="h-1 w-24 bg-yellow-500 mx-auto rounded-full mb-6"></div>
                <p className="text-gray-800 max-w-2xl mx-auto text-lg font-medium">
                  Trusted by professionals worldwide for reliable and accurate insulation testing
                </p>
              </motion.div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <Testimonial
                  quote="The CA 6555 has completely transformed our maintenance operations. The 15kV range allows us to test even the most demanding industrial equipment with confidence."
                  author="John Doe"
                  role="Lead Engineer"
                  company="ABC Industries"
                />

                <Testimonial
                  quote="The memory storage and USB interface make report generation a breeze. We've cut our documentation time in half since switching to KRYKARD testers."
                  author="Jane Smith"
                  role="Quality Manager"
                  company="XYZ Electric"
                  delay={0.1}
                />

                <Testimonial
                  quote="The reliability of these devices is outstanding. After three years of daily use in harsh environments, our CA 6545 still performs as accurately as day one."
                  author="Robert Johnson"
                  role="Field Technician"
                  company="Power Solutions Ltd"
                  delay={0.2}
                />
              </div>
            </div>
          </div> */}

          {/* Contact Section */}
          <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <ContactSection onContactClick={handleRequestDemo} />
          </div>
        </>
      )}

{activeTab === "products" && (
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6 md:py-8 font-['Open_Sans']">
          <div className="text-center mb-6 md:mb-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h1 className="text-xl md:text-2xl lg:text-3xl font-bold text-gray-900 mb-3 font-['Open_Sans']">Our Insulation Tester Range</h1>
              <p className="text-sm md:text-base lg:text-lg text-gray-800 max-w-4xl mx-auto font-medium text-center font-['Open_Sans']">
                Explore our complete lineup of professional insulation testers for various voltage ranges and applications
              </p>
            </motion.div>
          </div>

          {/* Filter Buttons */}
          <div className="flex flex-wrap justify-center items-center mb-6 md:mb-8 gap-2 sm:gap-3 md:gap-4">
            <button
              onClick={() => {
                setActiveFilter("all");
                navigate(`?tab=products&filter=all`, { replace: true });
              }}
              className={`px-3 py-2 rounded-full text-xs sm:text-sm font-medium ${
                activeFilter === "all"
                  ? "bg-yellow-400 text-white"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
            >
              All Products
            </button>
            <button
              onClick={() => {
                setActiveFilter("1kv");
                navigate(`?tab=products&filter=1kv`, { replace: true });
              }}
              className={`px-3 py-2 rounded-full text-xs sm:text-sm font-medium ${
                activeFilter === "1kv"
                  ? "bg-yellow-400 text-white"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
            >
              1kV Models
            </button>
            <button
              onClick={() => {
                setActiveFilter("special");
                navigate(`?tab=products&filter=special`, { replace: true });
              }}
              className={`px-3 py-2 rounded-full text-xs sm:text-sm font-medium ${
                activeFilter === "special"
                  ? "bg-yellow-400 text-white"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
            >
              Special Models
            </button>
            <button
              onClick={() => {
                setActiveFilter("5kv");
                navigate(`?tab=products&filter=5kv`, { replace: true });
              }}
              className={`px-3 py-2 rounded-full text-xs sm:text-sm font-medium ${
                activeFilter === "5kv"
                  ? "bg-yellow-400 text-white"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
            >
              5kV Models
            </button>
            <button
              onClick={() => {
                setActiveFilter("10kv");
                navigate(`?tab=products&filter=10kv`, { replace: true });
              }}
              className={`px-3 py-2 rounded-full text-xs sm:text-sm font-medium ${
                activeFilter === "10kv"
                  ? "bg-yellow-400 text-white"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
            >
              10kV/15kV Models
            </button>
          </div>

          {/* Product Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
            {filteredTesters.map(tester => (
              <ProductCard
                key={tester.id}
                product={tester}
                onViewDetails={handleViewDetails}
                colors={tester.colors}
              />
            ))}

            {filteredTesters.length === 0 && (
              <div className="text-center p-6 md:p-10 bg-gray-50 rounded-xl border border-gray-200 col-span-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 md:h-12 w-8 md:w-12 text-gray-400 mx-auto mb-3 md:mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h3 className="text-lg md:text-xl font-bold text-gray-700 mb-2 font-['Open_Sans']">No products found</h3>
                <p className="text-gray-600 text-sm md:text-base font-['Open_Sans']">Try adjusting your filter criteria</p>
              </div>
            )}
          </div>

          {/* Contact Section */}
          <div className="mt-8 md:mt-12">
            <ContactSection onContactClick={handleRequestDemo} />
          </div>
        </div>
      )}

      {activeTab === "details" && selectedTester && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Product Selector/Tabs */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-gradient-to-r from-yellow-50 to-white rounded-xl shadow-md p-6 mb-8 relative overflow-hidden"
          >
            <div className="absolute top-0 right-0 w-64 h-64 bg-yellow-200 rounded-full opacity-20 transform translate-x-1/2 -translate-y-1/2 blur-3xl"></div>

            <h2 className="text-xl font-bold text-gray-900 mb-4 relative z-10">
              Select <span className="text-yellow-500">Insulation Tester</span> Model
            </h2>

            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2 sm:gap-3 relative z-10">
              {insulationTesters.map((tester) => (
                <motion.button
                  key={tester.id}
                  whileHover={{ y: -3, boxShadow: "0 8px 20px -5px rgba(0, 0, 0, 0.1)" }}
                  whileTap={{ y: 0 }}
                  onClick={() => {
                    setSelectedTesterId(tester.id);
                    navigate(`?tab=details&product=${tester.id}`, { replace: true });
                  }}
                  className={`relative rounded-lg transition-all duration-300 overflow-hidden ${
                    selectedTesterId === tester.id
                      ? "ring-1 ring-yellow-400"
                      : "hover:ring-1 hover:ring-yellow-300"
                  }`}
                >
                  <div className={`h-full py-3 sm:py-4 px-2 sm:px-3 flex flex-col items-center text-center ${
                    selectedTesterId === tester.id
                      ? "bg-yellow-500 text-white"
                      : "bg-white hover:bg-yellow-50"
                  }`}>
                    <div className="mb-1 sm:mb-2">
                      <Gauge className={`h-5 w-5 sm:h-6 sm:w-6 ${selectedTesterId === tester.id ? "text-white" : "text-yellow-500"}`} />
                    </div>

                    <h3 className={`text-xs sm:text-sm font-bold mb-1 ${selectedTesterId === tester.id ? "text-white" : "text-gray-900"}`}>
                      {tester.modelNumbers}
                    </h3>

                    <div className={`text-xs ${selectedTesterId === tester.id ? "text-white opacity-80" : "text-gray-500"}`}>
                      {tester.title.includes("5kV") ? "5kV" :
                       tester.title.includes("10kV") ? "10kV/15kV" :
                       tester.title.includes("1kV") ? "1kV" : "Special"}
                    </div>

                    {selectedTesterId === tester.id && (
                      <div className="mt-1 sm:mt-2 bg-white bg-opacity-20 rounded-full px-2 py-0.5 text-xs font-semibold">
                        Selected
                      </div>
                    )}
                  </div>
                </motion.button>
              ))}
            </div>
          </motion.div>

          {/* Product Detail View */}
          <ProductDetailView
            product={selectedTester}
            onBackToList={handleBackToList}
          />

          {/* Comparison Table */}
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden mt-12">
            <div className="bg-gradient-to-r from-yellow-500 to-yellow-400 p-4">
              <h3 className="text-xl sm:text-2xl font-bold text-center text-white">Model Comparison</h3>
            </div>
            <div className="p-3 sm:p-6 overflow-x-auto">
              <div className="min-w-full pb-2">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead>
                    <tr>
                      <th className="px-3 sm:px-6 py-2 sm:py-3 bg-yellow-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider rounded-tl-lg">Feature</th>
                      {insulationTesters.filter(tester =>
                        (selectedTester.title.includes("5kV") && tester.title.includes("5kV")) ||
                        (selectedTester.title.includes("10kV") && tester.title.includes("10kV")) ||
                        (selectedTester.title.includes("1kV") && tester.title.includes("1kV")) ||
                        (selectedTester.title.includes("Special") && tester.title.includes("Special"))
                      ).map((tester, idx) => (
                        <th key={idx} className={`px-3 sm:px-6 py-2 sm:py-3 bg-yellow-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider ${idx === insulationTesters.length - 1 ? 'rounded-tr-lg' : ''}`}>
                          {tester.modelNumbers}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {[
                      { name: 'Max Test Voltage', getValue: (tester: TesterModel) => tester.title.includes("5kV") ? "5 kV" : tester.title.includes("10kV") ? (tester.modelNumbers.includes("6555") ? "15 kV" : "10 kV") : "1 kV" },
                      { name: 'Display Type', getValue: (tester: TesterModel) => tester.features.find((f: string) => f.includes("Display"))?.replace("Display : ", "") || "LCD" },
                      { name: 'Data Storage', getValue: (tester: TesterModel) => tester.features.find((f: string) => f.includes("Memory"))?.replace("Memory : ", "") || "No" },
                      { name: 'Communication', getValue: (tester: TesterModel) => tester.features.find((f: string) => f.includes("Communication"))?.replace("Communication : ", "") || "No" },
                      { name: 'DAR/PI Calculation', getValue: (tester: TesterModel) => tester.features.some((f: string) => f.includes("DAR/PI") || f.includes("DAR") || f.includes("PI")) ? "Yes" : "No" },
                      { name: 'Alarm Function', getValue: (tester: TesterModel) => tester.features.some((f: string) => f.includes("Alarm")) ? "Yes" : "No" }
                    ].map((feature, idx) => (
                      <motion.tr
                        key={idx}
                        initial={{ opacity: 0, y: 5 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: idx * 0.05 }}
                        viewport={{ once: true }}
                        className={idx % 2 === 0 ? 'bg-white hover:bg-yellow-50 transition-colors duration-200' : 'bg-gray-50 hover:bg-yellow-50 transition-colors duration-200'}
                      >
                        <td className="px-3 sm:px-6 py-2 sm:py-4 text-xs sm:text-sm font-medium text-gray-900">{feature.name}</td>
                        {insulationTesters.filter(tester =>
                          (selectedTester.title.includes("5kV") && tester.title.includes("5kV")) ||
                          (selectedTester.title.includes("10kV") && tester.title.includes("10kV")) ||
                          (selectedTester.title.includes("1kV") && tester.title.includes("1kV")) ||
                          (selectedTester.title.includes("Special") && tester.title.includes("Special"))
                        ).map((tester, i) => (
                          <td key={i} className="px-3 sm:px-6 py-2 sm:py-4 text-xs sm:text-sm text-gray-800 text-center">
                            {feature.getValue(tester)}
                          </td>
                        ))}
                      </motion.tr>
                    ))}
                  </tbody>
                </table>
              </div>
              <p className="text-xs text-gray-500 mt-2 italic text-center">Swipe horizontally to see more details</p>
            </div>
          </div>

          {/* Contact Section */}
          <div className="mt-16">
            <ContactSection onContactClick={handleRequestDemo} />
          </div>
        </div>
      )}

      {activeTab === "applications" && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h1 className="text-3xl font-bold text-gray-900 mb-4">Applications</h1>
              <p className="text-lg text-gray-800 max-w-3xl mx-auto">
                KRYKARD insulation testers are versatile instruments designed for a wide range of professional applications
              </p>
            </motion.div>
          </div>

          {/* Application Areas Section */}
          <div className="py-12 bg-gradient-to-br from-yellow-50 to-white rounded-2xl my-12 shadow-lg">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">Application Areas</h2>
                <p className="text-lg text-gray-800 max-w-3xl mx-auto font-medium">
                  Our insulation testers are used in a variety of industries and applications
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <ApplicationCard
                  icon={<Zap className="h-8 w-8" />}
                  title="Power Generation"
                  description="Testing insulation resistance in transformers, generators, motors and other high voltage equipment."
                />

                <ApplicationCard
                  icon={<Shield className="h-8 w-8" />}
                  title="Industrial Maintenance"
                  description="Preventive maintenance of electrical systems, motors, and controls to identify insulation failures before equipment breakdown."
                />

                <ApplicationCard
                  icon={<Clock className="h-8 w-8" />}
                  title="Telecommunications"
                  description="Testing of cables, lines, and equipment with specialized low voltage models designed for telecom applications."
                />

                <ApplicationCard
                  icon={<Award className="h-8 w-8" />}
                  title="Aerospace & Defense"
                  description="Precise testing of sensitive electronic systems with adjustable low voltage insulation testers for specialized applications."
                />
              </div>
            </div>
          </div>

          {/* Industry Solutions Section */}
          <div className="mt-16 bg-white rounded-2xl shadow-lg p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Industry-Specific Solutions</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-600">
                  <Zap className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Utility Companies</h3>
                  <p className="text-gray-800 font-medium">
                    High voltage models for testing transmission lines, transformers, and switchgear with advanced data storage for compliance records.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-600">
                  <BarChart4 className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Manufacturing</h3>
                  <p className="text-gray-800 font-medium">
                    Versatile models for equipment maintenance, quality assurance, and production line testing with quick test times.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-600">
                  <Database className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Data Centers</h3>
                  <p className="text-gray-800 font-medium">
                    Specialized testing solutions for critical power infrastructure with low interference design for sensitive electronic environments.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-600">
                  <Shield className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Marine Industry</h3>
                  <p className="text-gray-800 font-medium">
                    Rugged, IP-rated models for harsh environments with specialized testing capabilities for maritime electrical systems.
                  </p>
                </div>
              </motion.div>
            </div>

            <div className="flex justify-center mt-8">
              <Button
                className="px-6 py-3 bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-semibold rounded-lg shadow-md transition duration-300 flex items-center space-x-2"
                onClick={handleViewBrochure}
              >
                <span>View Brochure</span>
                <FileText className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </div>

          {/* FAQ Section */}
          <section className="mt-16 mb-16">
            <div className="max-w-5xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
                <p className="text-gray-800 max-w-3xl mx-auto font-medium">
                  Common questions about insulation testing applications
                </p>
              </div>

              <div className="space-y-4">
                <FAQItem
                  question="How often should insulation testing be performed?"
                  answer="As a general rule, critical equipment should undergo insulation testing annually, with more frequent testing for equipment in harsh environments or critical applications. Always follow manufacturer recommendations and industry standards specific to your equipment and industry."
                />

                <FAQItem
                  question="What test voltage should I use for my equipment?"
                  answer="The appropriate test voltage depends on the rated voltage of the equipment being tested. For example, 500V test voltage is typically used for equipment rated up to 600V, 1000V for 600V-1000V equipment, and 5000V for equipment above 4000V. Always consult manufacturer specifications and industry standards."
                />

                <FAQItem
                  question="What are DAR and PI measurements and why are they important?"
                  answer="Dielectric Absorption Ratio (DAR) and Polarization Index (PI) are time-resistance tests that indicate insulation quality beyond a simple spot reading. DAR compares readings at 30 seconds and 60 seconds, while PI compares readings at 1 minute and 10 minutes. These ratios help identify contamination and deterioration that might not be apparent in a single measurement."
                />

                <FAQItem
                  question="Can I test equipment that is connected to a circuit?"
                  answer="No, equipment should always be disconnected and isolated before insulation testing. The high test voltages can damage connected electronic components and create safety hazards. Always follow proper lockout/tagout procedures and ensure the equipment is fully discharged before and after testing."
                />
              </div>
            </div>
          </section>

          {/* Contact Section */}
          <div className="mt-16">
            <ContactSection onContactClick={handleRequestDemo} />
          </div>
        </div>
      )}

      {/* No PDF Viewer Modal - Using direct link instead */}
      </div>
    </PageLayout>
  );
};

export default InsulationTesters;