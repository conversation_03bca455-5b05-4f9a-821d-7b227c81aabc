import React, { useState, useEffect, useRef } from 'react';
import { Zap, TrendingUp, Shield, Award, Users, Monitor, BarChart3, Leaf, Settings, CheckCircle, ArrowRight, Sparkles, Target, Globe, Star, Hexagon, Circle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { motion, useAnimation, useInView, useScroll, useTransform, AnimatePresence, useSpring } from 'framer-motion';
import PageLayout from "@/components/layout/PageLayout";

// Animated Particle Background Component
const AnimatedParticles = ({ count = 50, color = "green" }) => {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {Array.from({ length: count }).map((_, i) => (
        <motion.div
          key={i}
          className={`absolute w-1 h-1 rounded-full bg-${color}-400/30`}
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            y: [0, -100, -200],
            opacity: [0, 0.8, 0],
            scale: [0, 1, 0],
          }}
          transition={{
            duration: 8 + Math.random() * 4,
            repeat: Infinity,
            delay: i * 0.1,
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  );
};

// Floating Geometric Shapes Component
const FloatingShapes = () => {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Hexagon shapes */}
      {Array.from({ length: 8 }).map((_, i) => (
        <motion.div
          key={`hex-${i}`}
          className="absolute"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            rotate: [0, 360],
            scale: [0.8, 1.2, 0.8],
            opacity: [0.1, 0.3, 0.1],
          }}
          transition={{
            duration: 15 + Math.random() * 10,
            repeat: Infinity,
            delay: i * 2,
            ease: "linear",
          }}
        >
          <Hexagon className="w-8 h-8 text-green-300/20" />
        </motion.div>
      ))}

      {/* Star shapes */}
      {Array.from({ length: 6 }).map((_, i) => (
        <motion.div
          key={`star-${i}`}
          className="absolute"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            rotate: [0, -360],
            scale: [0.5, 1, 0.5],
            opacity: [0.2, 0.5, 0.2],
          }}
          transition={{
            duration: 20 + Math.random() * 5,
            repeat: Infinity,
            delay: i * 3,
            ease: "easeInOut",
          }}
        >
          <Star className="w-6 h-6 text-emerald-300/25" />
        </motion.div>
      ))}

      {/* Circle shapes */}
      {Array.from({ length: 10 }).map((_, i) => (
        <motion.div
          key={`circle-${i}`}
          className="absolute"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            scale: [0.3, 1.5, 0.3],
            opacity: [0.1, 0.4, 0.1],
            x: [0, 50, 0],
            y: [0, -30, 0],
          }}
          transition={{
            duration: 12 + Math.random() * 8,
            repeat: Infinity,
            delay: i * 1.5,
            ease: "easeInOut",
          }}
        >
          <Circle className="w-4 h-4 text-teal-300/20" />
        </motion.div>
      ))}
    </div>
  );
};

// Magnetic Button Component
const MagneticButton = ({ children, onClick, className = "" }) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);
  const buttonRef = useRef(null);

  const handleMouseMove = (e) => {
    if (!buttonRef.current) return;
    const rect = buttonRef.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    const deltaX = (e.clientX - centerX) * 0.3;
    const deltaY = (e.clientY - centerY) * 0.3;
    setMousePosition({ x: deltaX, y: deltaY });
  };

  return (
    <motion.button
      ref={buttonRef}
      onClick={onClick}
      className={`relative overflow-hidden ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => {
        setIsHovered(false);
        setMousePosition({ x: 0, y: 0 });
      }}
      onMouseMove={handleMouseMove}
      animate={{
        x: mousePosition.x,
        y: mousePosition.y,
        scale: isHovered ? 1.05 : 1,
      }}
      transition={{
        type: "spring",
        stiffness: 300,
        damping: 20,
      }}
      whileTap={{ scale: 0.95 }}
    >
      {/* Ripple effect */}
      <motion.div
        className="absolute inset-0 bg-white/20 rounded-full"
        initial={{ scale: 0, opacity: 0 }}
        animate={isHovered ? { scale: 1, opacity: 1 } : { scale: 0, opacity: 0 }}
        transition={{ duration: 0.3 }}
      />
      {children}
    </motion.button>
  );
};

const OnPremiseSystemsPage = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedModule, setSelectedModule] = useState<number | null>(null);
  const [countsStarted, setCountsStarted] = useState(false);
  const statsRef = useRef<HTMLDivElement>(null);

  const stats = [
    { label: 'Typical Energy Savings', value: '8-15%', displayValue: '8-15%', icon: TrendingUp, color: 'from-green-500 to-emerald-600' },
    { label: 'Payback Period', value: '1.5 - 2 Years', displayValue: '1.5 - 2 Years', icon: Award, color: 'from-emerald-500 to-teal-600' },
    { label: 'Customers Served', value: '300+', displayValue: '300+', countTo: 300, icon: Users, color: 'from-teal-500 to-green-600' },
    { label: 'Devices Connected', value: '20,000+', displayValue: '20,000+', countTo: 20000, icon: Monitor, color: 'from-green-600 to-emerald-700' }
  ];

  // Count-up animation state
  const [customersCount, setCustomersCount] = useState(0);
  const [devicesCount, setDevicesCount] = useState(0);

  // Intersection Observer for triggering count-up
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && !countsStarted) {
          setCountsStarted(true);

          // Animate customers count
          const customersTarget = 300;
          const customersDuration = 2000;
          const customersStartTime = Date.now();

          const animateCustomers = () => {
            const elapsed = Date.now() - customersStartTime;
            const progress = Math.min(elapsed / customersDuration, 1);
            const current = Math.floor(progress * customersTarget);
            setCustomersCount(current);

            if (progress < 1) {
              requestAnimationFrame(animateCustomers);
            }
          };

          // Animate devices count
          const devicesTarget = 20000;
          const devicesDuration = 2500;
          const devicesStartTime = Date.now();

          const animateDevices = () => {
            const elapsed = Date.now() - devicesStartTime;
            const progress = Math.min(elapsed / devicesDuration, 1);
            const current = Math.floor(progress * devicesTarget);
            setDevicesCount(current);

            if (progress < 1) {
              requestAnimationFrame(animateDevices);
            }
          };

          // Start animations
          requestAnimationFrame(animateCustomers);
          requestAnimationFrame(animateDevices);
        }
      },
      { threshold: 0.3 }
    );

    if (statsRef.current) {
      observer.observe(statsRef.current);
    }

    return () => observer.disconnect();
  }, [countsStarted]);

  const addOnModules = [
    {
      title: 'Power Quality Management',
      description: 'Advanced electrical system monitoring with real-time analytics',
      icon: Zap,
      features: ['Real-time power quality monitoring', 'Harmonic analysis', 'Voltage fluctuation tracking', 'Equipment protection alerts']
    },
    {
      title: 'Demand Management',
      description: 'Intelligent peak demand optimization and load control',
      icon: BarChart3,
      features: ['Peak demand forecasting', 'Load shifting strategies', 'Cost optimization', 'Automated load control']
    },
    {
      title: 'Asset Performance',
      description: 'Maximize utility asset performance and energy conservation',
      icon: Settings,
      features: ['Asset health monitoring', 'Performance optimization', 'Energy conservation tracking', 'Maintenance scheduling']
    },
    {
      title: 'ISO 50001 Compliance',
      description: 'Complete energy management standards compliance solution',
      icon: Shield,
      features: ['Compliance documentation', 'Audit trail management', 'Standard reporting', 'Certification support']
    },
    {
      title: 'Sustainability Reporting',
      description: 'Comprehensive carbon footprint and compliance reporting',
      icon: Leaf,
      features: ['Carbon footprint calculation', 'Emission tracking', 'Sustainability metrics', 'Regulatory compliance']
    },
    {
      title: 'Asset Management',
      description: 'Predictive maintenance and asset lifecycle optimization',
      icon: Monitor,
      features: ['Predictive maintenance', 'Asset lifecycle tracking', 'Maintenance scheduling', 'Performance analytics']
    }
  ];

  const coreFeatures = [
    'Real-time energy monitoring and analytics',
    'AI-powered predictive intelligence',
    'Automated energy optimization',
    'Comprehensive reporting dashboard',
    'Multi-site management capabilities',
    'Mobile and web-based access',
    'Integration with existing systems',
    'Custom alert and notification system'
  ];

  const benefits = [
    { title: 'Cost Reduction', description: '8-15% reduction in energy costs through intelligent optimization', icon: TrendingUp },
    { title: 'Quick ROI', description: 'Payback period of just 1.5-2 years with immediate savings', icon: Award },
    { title: 'Sustainability', description: 'Reduce carbon footprint and meet environmental goals', icon: Leaf },
    { title: 'Compliance', description: 'Meet international energy management standards', icon: Shield }
  ];

  return (
    <div className="font-['Open_Sans'] min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 w-full overflow-x-hidden">
      <PageLayout
        title="On-Premise Systems"
        subtitle="Smart Energy Management System (EnMS)"
        category="conserve"
      >
        {/* Enhanced Modern Hero Section with Advanced Animations */}
        <motion.div
          className="relative overflow-hidden"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1.2 }}
        >
          {/* Animated Gradient Background */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50"
            animate={{
              background: [
                "linear-gradient(135deg, rgb(240 253 244) 0%, rgb(236 253 245) 50%, rgb(240 253 250) 100%)",
                "linear-gradient(135deg, rgb(236 253 245) 0%, rgb(240 253 250) 50%, rgb(240 253 244) 100%)",
                "linear-gradient(135deg, rgb(240 253 250) 0%, rgb(240 253 244) 50%, rgb(236 253 245) 100%)",
              ]
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />

          {/* Floating Geometric Shapes */}
          <FloatingShapes />

          {/* Animated Particles */}
          <AnimatedParticles count={30} color="green" />

          {/* Enhanced Organic Floating Elements with Physics */}
          <div className="absolute inset-0 overflow-hidden">
            <motion.div
              className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-br from-green-200/30 to-emerald-300/20 rounded-full blur-3xl"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.3, 0.6, 0.3],
                x: [0, 50, 0],
                y: [0, -30, 0],
              }}
              transition={{
                duration: 12,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            <motion.div
              className="absolute bottom-10 right-10 w-80 h-80 bg-gradient-to-br from-emerald-200/25 to-teal-300/20 rounded-full blur-2xl"
              animate={{
                scale: [1, 0.8, 1.1, 1],
                opacity: [0.25, 0.5, 0.25],
                x: [0, -40, 0],
                y: [0, 20, 0],
              }}
              transition={{
                duration: 15,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 2
              }}
            />
            <motion.div
              className="absolute top-1/3 right-1/4 w-64 h-64 bg-gradient-to-br from-teal-200/20 to-green-300/15 rounded-full blur-xl"
              animate={{
                scale: [0.8, 1.3, 0.8],
                opacity: [0.2, 0.4, 0.2],
                rotate: [0, 180, 360],
              }}
              transition={{
                duration: 18,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 4
              }}
            />
          </div>

          {/* Dynamic Grid Pattern with Animation */}
          <motion.div
            className="absolute inset-0 opacity-[0.02]"
            animate={{
              opacity: [0.02, 0.05, 0.02]
            }}
            transition={{
              duration: 6,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='%23059669' stroke-width='1'%3E%3Cpath d='M0 0h100v100H0z'/%3E%3Cpath d='M0 50h100M50 0v100'/%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </motion.div>

          <div className="relative px-4 sm:px-6 lg:px-8 py-16 lg:py-24">
            <div className="max-w-7xl mx-auto">
              <div className="grid lg:grid-cols-12 gap-12 lg:gap-16 items-center">
                {/* Left Content - 7 columns with Staggered Animations */}
                <motion.div
                  className="lg:col-span-7 text-center lg:text-left space-y-8"
                  initial={{ opacity: 0, x: -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                >
                  {/* Floating Badge with Enhanced Animation */}
                  <motion.div
                    className="inline-flex items-center px-6 py-3 rounded-full bg-white/80 backdrop-blur-sm border border-green-200/50 shadow-lg"
                    initial={{ opacity: 0, y: -20, scale: 0.8 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    transition={{
                      duration: 0.6,
                      delay: 0.4,
                      type: "spring",
                      stiffness: 200
                    }}
                    whileHover={{
                      scale: 1.05,
                      boxShadow: "0 20px 40px rgba(34, 197, 94, 0.2)"
                    }}
                  >
                    <motion.div
                      className="w-2 h-2 bg-green-500 rounded-full mr-3"
                      animate={{
                        scale: [1, 1.5, 1],
                        opacity: [1, 0.5, 1]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    />
                    <span className="text-sm font-bold text-green-700 tracking-wide font-['Open_Sans']">Next-Gen Energy Solutions</span>
                  </motion.div>

                  {/* Main Heading with Staggered Text Animation */}
                  <div className="space-y-4">
                    <motion.h1
                      className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-black leading-tight font-['Open_Sans']"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.8, delay: 0.6 }}
                    >
                      <motion.span
                        className="text-gray-900 block"
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.8 }}
                      >
                        Smart
                      </motion.span>
                      <motion.span
                        className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent block relative"
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 1.0 }}
                      >
                        On-Premise
                        {/* Enhanced Glow Effect with Animation */}
                        <motion.span
                          className="absolute inset-0 bg-gradient-to-r from-green-600/20 via-emerald-600/20 to-teal-600/20 blur-sm -z-10"
                          animate={{
                            opacity: [0.2, 0.5, 0.2]
                          }}
                          transition={{
                            duration: 3,
                            repeat: Infinity,
                            ease: "easeInOut"
                          }}
                        />
                      </motion.span>
                      <motion.span
                        className="text-gray-800 block"
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 1.2 }}
                      >
                        Energy Management
                      </motion.span>
                    </motion.h1>

                    {/* Enhanced Decorative Elements with Physics Animation */}
                    <motion.div
                      className="flex justify-center lg:justify-start items-center space-x-3"
                      initial={{ opacity: 0, scale: 0 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{
                        duration: 0.8,
                        delay: 1.4,
                        type: "spring",
                        stiffness: 150
                      }}
                    >
                      <motion.div
                        className="w-16 h-1 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full"
                        animate={{
                          scaleX: [1, 1.2, 1],
                          opacity: [0.7, 1, 0.7]
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      />
                      <motion.div
                        className="w-2 h-2 bg-green-500 rounded-full"
                        animate={{
                          scale: [1, 1.5, 1],
                          opacity: [1, 0.5, 1]
                        }}
                        transition={{
                          duration: 1.5,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      />
                      <motion.div
                        className="w-8 h-1 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full"
                        animate={{
                          scaleX: [1, 1.3, 1],
                          opacity: [0.7, 1, 0.7]
                        }}
                        transition={{
                          duration: 2.5,
                          repeat: Infinity,
                          ease: "easeInOut",
                          delay: 0.5
                        }}
                      />
                    </motion.div>
                  </div>

                  {/* Description with Animated Text */}
                  <motion.p
                    className="text-lg lg:text-xl text-gray-800 leading-relaxed font-semibold max-w-2xl mx-auto lg:mx-0 font-['Open_Sans']"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 1.6 }}
                  >
                    Transform your energy infrastructure with our comprehensive EnMS platform designed for
                    <motion.span
                      className="text-green-700 font-black"
                      animate={{
                        color: ["#15803d", "#059669", "#15803d"]
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    > maximum efficiency</motion.span>,
                    <motion.span
                      className="text-emerald-700 font-black"
                      animate={{
                        color: ["#047857", "#10b981", "#047857"]
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: "easeInOut",
                        delay: 1
                      }}
                    > real-time insights</motion.span>, and
                    <motion.span
                      className="text-teal-700 font-black"
                      animate={{
                        color: ["#0f766e", "#14b8a6", "#0f766e"]
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: "easeInOut",
                        delay: 2
                      }}
                    > sustainable operations</motion.span>
                  </motion.p>

                  {/* Enhanced Key Highlights with Staggered Animation */}
                  <motion.div
                    className="flex flex-wrap gap-4 justify-center lg:justify-start"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 1.8 }}
                  >
                    {[
                      { icon: CheckCircle, text: "8-15% Savings", color: "green", delay: 0 },
                      { icon: CheckCircle, text: "Quick ROI", color: "emerald", delay: 0.1 },
                      { icon: CheckCircle, text: "AI-Powered", color: "teal", delay: 0.2 }
                    ].map((item, index) => (
                      <motion.div
                        key={index}
                        className={`flex items-center space-x-2 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-full border border-${item.color}-200 shadow-md`}
                        initial={{ opacity: 0, scale: 0.8, y: 20 }}
                        animate={{ opacity: 1, scale: 1, y: 0 }}
                        transition={{
                          duration: 0.5,
                          delay: 2.0 + item.delay,
                          type: "spring",
                          stiffness: 200
                        }}
                        whileHover={{
                          scale: 1.05,
                          boxShadow: `0 10px 30px rgba(34, 197, 94, 0.2)`,
                          y: -2
                        }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <motion.div
                          animate={{
                            rotate: [0, 360],
                            scale: [1, 1.1, 1]
                          }}
                          transition={{
                            duration: 4,
                            repeat: Infinity,
                            ease: "easeInOut",
                            delay: index * 0.5
                          }}
                        >
                          <item.icon className={`w-4 h-4 text-${item.color}-700`} />
                        </motion.div>
                        <span className="text-sm font-bold text-gray-800 font-['Open_Sans']">{item.text}</span>
                      </motion.div>
                    ))}
                  </motion.div>
                </motion.div>

                {/* Right Content - 5 columns with Advanced Animations */}
                <motion.div
                  className="lg:col-span-5 relative"
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                >
                  <motion.div
                    className="relative group"
                    whileHover={{ scale: 1.02 }}
                    transition={{ type: "spring", stiffness: 200, damping: 20 }}
                  >
                    {/* Floating Orbs around Image */}
                    <div className="absolute -inset-10 pointer-events-none">
                      {Array.from({ length: 6 }).map((_, i) => (
                        <motion.div
                          key={i}
                          className="absolute w-4 h-4 bg-green-400/30 rounded-full blur-sm"
                          style={{
                            left: `${20 + (i * 15)}%`,
                            top: `${10 + (i * 12)}%`,
                          }}
                          animate={{
                            y: [0, -20, 0],
                            opacity: [0.3, 0.8, 0.3],
                            scale: [0.8, 1.2, 0.8],
                          }}
                          transition={{
                            duration: 4 + i,
                            repeat: Infinity,
                            delay: i * 0.5,
                            ease: "easeInOut"
                          }}
                        />
                      ))}
                    </div>

                    {/* Main Image with Enhanced Effects */}
                    <motion.div
                      className="relative overflow-hidden rounded-3xl shadow-2xl"
                      initial={{ scale: 0.8, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{
                        duration: 0.8,
                        delay: 0.6,
                        type: "spring",
                        stiffness: 100
                      }}
                    >
                      <motion.img
                        src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop&auto=format"
                        alt="On-Premise Energy Management System"
                        className="w-full h-auto object-cover"
                        loading="lazy"
                        whileHover={{ scale: 1.05 }}
                        transition={{ duration: 0.7, ease: "easeOut" }}
                      />

                      {/* Animated Gradient Overlay */}
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-t from-green-900/20 via-transparent to-transparent"
                        animate={{
                          background: [
                            "linear-gradient(to top, rgba(20, 83, 45, 0.2) 0%, transparent 50%, transparent 100%)",
                            "linear-gradient(to top, rgba(5, 150, 105, 0.15) 0%, transparent 60%, transparent 100%)",
                            "linear-gradient(to top, rgba(20, 83, 45, 0.2) 0%, transparent 50%, transparent 100%)",
                          ]
                        }}
                        transition={{
                          duration: 4,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      />

                      {/* Floating Tech Elements */}
                      <div className="absolute inset-0 pointer-events-none">
                        <motion.div
                          className="absolute top-4 right-4 bg-green-600/90 backdrop-blur-sm text-white p-2 rounded-lg shadow-xl"
                          initial={{ opacity: 0, scale: 0 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: 1.2, type: "spring" }}
                          whileHover={{ scale: 1.1 }}
                        >
                          <div className="flex items-center space-x-2">
                            <motion.div
                              animate={{ rotate: 360 }}
                              transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                            >
                              <Sparkles className="w-4 h-4" />
                            </motion.div>
                            <span className="text-xs font-bold font-['Open_Sans']">AI-Powered</span>
                          </div>
                        </motion.div>

                        <motion.div
                          className="absolute bottom-4 left-4 bg-emerald-600/90 backdrop-blur-sm text-white p-2 rounded-lg shadow-xl"
                          initial={{ opacity: 0, scale: 0 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: 1.4, type: "spring" }}
                          whileHover={{ scale: 1.1 }}
                        >
                          <div className="flex items-center space-x-2">
                            <motion.div
                              animate={{
                                scale: [1, 1.2, 1],
                                opacity: [1, 0.7, 1]
                              }}
                              transition={{
                                duration: 2,
                                repeat: Infinity,
                                ease: "easeInOut"
                              }}
                            >
                              <TrendingUp className="w-4 h-4" />
                            </motion.div>
                            <span className="text-xs font-bold font-['Open_Sans']">Real-time</span>
                          </div>
                        </motion.div>
                      </div>
                    </motion.div>
                  </motion.div>
                </motion.div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Enhanced Stats Section with Advanced Animations */}
        <motion.div
          ref={statsRef}
          className="py-8 sm:py-10 bg-white relative overflow-hidden"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.8 }}
        >
          {/* Floating Orbs Background */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            {Array.from({ length: 12 }).map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-6 h-6 bg-green-400/10 rounded-full blur-sm"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  y: [0, -50, 0],
                  x: [0, 30, 0],
                  opacity: [0.1, 0.3, 0.1],
                  scale: [0.5, 1.2, 0.5],
                }}
                transition={{
                  duration: 8 + Math.random() * 4,
                  repeat: Infinity,
                  delay: i * 0.5,
                  ease: "easeInOut"
                }}
              />
            ))}
          </div>

          {/* Enhanced Background Pattern with Animation */}
          <motion.div
            className="absolute inset-0 opacity-5"
            animate={{
              opacity: [0.05, 0.1, 0.05]
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </motion.div>

          <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              className="text-center mb-6 sm:mb-8"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <motion.h2
                className="text-2xl sm:text-3xl lg:text-4xl font-black text-gray-900 mb-3 font-['Open_Sans']"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <motion.span
                  className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent"
                  animate={{
                    backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"]
                  }}
                  transition={{
                    duration: 5,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  style={{
                    backgroundSize: "200% 200%"
                  }}
                >
                  Proven Performance
                </motion.span>
              </motion.h2>
              <motion.p
                className="text-base sm:text-lg text-gray-800 font-bold max-w-2xl mx-auto font-['Open_Sans']"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                Savings right out of the box
              </motion.p>
            </motion.div>

            {/* Enhanced Table-style Stats Display with Advanced Animations */}
            <motion.div
              className="bg-white rounded-xl sm:rounded-2xl shadow-2xl border-2 border-green-100 overflow-hidden max-w-4xl mx-auto"
              initial={{ opacity: 0, y: 50, scale: 0.9 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.6 }}
              whileHover={{
                boxShadow: "0 25px 50px rgba(34, 197, 94, 0.15)",
                scale: 1.02
              }}
            >
              {/* Table Header Row with Staggered Animation */}
              <motion.div
                className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 bg-gradient-to-r from-green-50 to-emerald-50 border-b-2 border-green-100"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.8 }}
              >
                {stats.map((stat, index) => (
                  <motion.div
                    key={index}
                    className="p-4 sm:p-6 text-center border-b sm:border-b-0 sm:border-r border-green-100 last:border-b-0 last:border-r-0"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 1.0 + (index * 0.1) }}
                    whileHover={{
                      backgroundColor: "rgba(34, 197, 94, 0.05)",
                      scale: 1.05
                    }}
                  >
                    <motion.h3
                      className="text-sm sm:text-base lg:text-lg font-black text-gray-900 leading-tight font-['Open_Sans']"
                      animate={{
                        color: ["#111827", "#059669", "#111827"]
                      }}
                      transition={{
                        duration: 4,
                        repeat: Infinity,
                        delay: index * 0.5,
                        ease: "easeInOut"
                      }}
                    >
                      {stat.label}
                    </motion.h3>
                  </motion.div>
                ))}
              </motion.div>

              {/* Table Values Row with Enhanced Animations */}
              <motion.div
                className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 bg-white"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 1.2 }}
              >
                {stats.map((stat, index) => {
                  const getDisplayValue = () => {
                    if (stat.countTo === 300) {
                      return countsStarted ? `${customersCount}+` : '0+';
                    } else if (stat.countTo === 20000) {
                      return countsStarted ? `${devicesCount.toLocaleString()}+` : '0+';
                    }
                    return stat.displayValue;
                  };

                  return (
                    <motion.div
                      key={index}
                      className="p-6 sm:p-8 text-center border-b sm:border-b-0 sm:border-r border-green-100 last:border-b-0 last:border-r-0 group relative overflow-hidden"
                      initial={{ opacity: 0, scale: 0.8 }}
                      whileInView={{ opacity: 1, scale: 1 }}
                      viewport={{ once: true }}
                      transition={{
                        duration: 0.6,
                        delay: 1.4 + (index * 0.1),
                        type: "spring",
                        stiffness: 200
                      }}
                      whileHover={{
                        backgroundColor: "rgba(34, 197, 94, 0.05)",
                        scale: 1.05,
                        y: -5
                      }}
                    >
                      {/* Hover Effect Background */}
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-br from-green-400/10 to-emerald-400/10"
                        initial={{ opacity: 0, scale: 0 }}
                        whileHover={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.3 }}
                      />

                      {/* Floating Particles on Hover */}
                      <div className="absolute inset-0 pointer-events-none">
                        {Array.from({ length: 3 }).map((_, i) => (
                          <motion.div
                            key={i}
                            className="absolute w-1 h-1 bg-green-400 rounded-full"
                            style={{
                              left: `${20 + i * 30}%`,
                              top: `${30 + i * 20}%`,
                            }}
                            initial={{ opacity: 0, scale: 0 }}
                            whileHover={{
                              opacity: [0, 1, 0],
                              scale: [0, 1, 0],
                              y: [0, -20, -40]
                            }}
                            transition={{
                              duration: 1.5,
                              delay: i * 0.2,
                              repeat: Infinity
                            }}
                          />
                        ))}
                      </div>

                      <motion.div
                        className="relative text-2xl sm:text-3xl lg:text-4xl font-black text-green-700 group-hover:text-green-800 transition-colors font-['Open_Sans']"
                        animate={{
                          scale: [1, 1.05, 1],
                          textShadow: [
                            "0 0 0px rgba(34, 197, 94, 0)",
                            "0 0 10px rgba(34, 197, 94, 0.3)",
                            "0 0 0px rgba(34, 197, 94, 0)"
                          ]
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          delay: index * 0.5,
                          ease: "easeInOut"
                        }}
                      >
                        {getDisplayValue()}
                      </motion.div>
                    </motion.div>
                  );
                })}
              </motion.div>
            </motion.div>
          </div>
        </motion.div>

        {/* Product Overview Tabs */}
        <div className="py-8 sm:py-10 lg:py-12 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 relative">
          {/* Background Elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-200/20 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-200/20 rounded-full blur-3xl"></div>
          </div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-8 sm:mb-10">
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 mb-3 sm:mb-4 font-['Open_Sans']">
                <span className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  Comprehensive Energy
                </span>
                <br />
                <span className="text-gray-800">Management Solution</span>
              </h2>
              <p className="text-base sm:text-lg lg:text-xl xl:text-2xl text-gray-700 max-w-4xl mx-auto font-medium leading-relaxed font-['Open_Sans']">
                Transform how you monitor, manage, and optimize energy resources across your entire operations with our
                <span className="text-green-600 font-bold"> AI-powered platform</span>
              </p>
            </div>

            {/* Enhanced Tab Navigation with Advanced Animations */}
            <motion.div
              className="flex flex-wrap justify-center mb-8 sm:mb-10 bg-white/80 backdrop-blur-sm rounded-xl sm:rounded-2xl p-2 sm:p-3 shadow-2xl max-w-4xl mx-auto border border-green-100/50"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              {[
                { id: 'overview', label: 'Overview', icon: Target },
                { id: 'features', label: 'Core Features', icon: Settings },
                { id: 'benefits', label: 'Benefits', icon: Award },
                { id: 'modules', label: 'Add-on Modules', icon: Globe }
              ].map((tab, index) => {
                const TabIcon = tab.icon;
                return (
                  <motion.button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`group relative flex items-center px-3 sm:px-4 lg:px-6 py-2 sm:py-3 lg:py-4 rounded-lg sm:rounded-xl font-bold text-sm sm:text-base lg:text-lg transition-all duration-300 font-['Open_Sans'] m-1 overflow-hidden ${
                      activeTab === tab.id
                        ? 'bg-gradient-to-r from-green-600 to-emerald-600 text-white shadow-xl'
                        : 'text-gray-700 hover:text-green-600 hover:bg-green-50/80'
                    }`}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{
                      duration: 0.4,
                      delay: 0.4 + (index * 0.1),
                      type: "spring",
                      stiffness: 200
                    }}
                    whileHover={{
                      scale: 1.05,
                      y: -2
                    }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {/* Active Tab Background Animation */}
                    {activeTab === tab.id && (
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-green-600 to-emerald-600"
                        layoutId="activeTab"
                        transition={{ type: "spring", stiffness: 300, damping: 30 }}
                      />
                    )}

                    {/* Ripple Effect */}
                    <motion.div
                      className="absolute inset-0 bg-white/20 rounded-lg"
                      initial={{ scale: 0, opacity: 0 }}
                      whileTap={{ scale: 1, opacity: 1 }}
                      transition={{ duration: 0.2 }}
                    />

                    <motion.div
                      className="relative z-10"
                      animate={activeTab === tab.id ? { rotate: [0, 360] } : {}}
                      transition={{ duration: 0.6, ease: "easeInOut" }}
                    >
                      <TabIcon className={`w-4 h-4 sm:w-5 sm:h-5 mr-1 sm:mr-2 transition-all ${
                        activeTab === tab.id ? 'text-green-200' : 'text-gray-500 group-hover:text-green-500'
                      }`} />
                    </motion.div>

                    <span className="relative z-10 hidden sm:inline">{tab.label}</span>
                    <span className="relative z-10 sm:hidden">{tab.label.split(' ')[0]}</span>

                    {/* Floating Particles for Active Tab */}
                    {activeTab === tab.id && (
                      <div className="absolute inset-0 pointer-events-none">
                        {Array.from({ length: 3 }).map((_, i) => (
                          <motion.div
                            key={i}
                            className="absolute w-1 h-1 bg-white/60 rounded-full"
                            style={{
                              left: `${20 + i * 30}%`,
                              top: `${30 + i * 20}%`,
                            }}
                            animate={{
                              opacity: [0, 1, 0],
                              scale: [0, 1, 0],
                              y: [0, -10, -20]
                            }}
                            transition={{
                              duration: 2,
                              repeat: Infinity,
                              delay: i * 0.3,
                              ease: "easeOut"
                            }}
                          />
                        ))}
                      </div>
                    )}
                  </motion.button>
                );
              })}
            </motion.div>

            {/* Enhanced Tab Content with Smooth Transitions */}
            <motion.div
              className="bg-white/90 backdrop-blur-sm rounded-2xl sm:rounded-3xl shadow-2xl p-4 sm:p-6 lg:p-8 xl:p-10 border border-green-100/50 relative overflow-hidden"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              {/* Background Animation */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-br from-green-50/50 via-emerald-50/30 to-teal-50/50"
                animate={{
                  opacity: [0.3, 0.6, 0.3]
                }}
                transition={{
                  duration: 6,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />

              <AnimatePresence mode="wait">
                {activeTab === 'overview' && (
                  <motion.div
                    key="overview"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ duration: 0.4 }}
                    className="relative z-10"
                  >
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-10 items-center">
                  <div className="space-y-4 sm:space-y-6">
                    <div className="space-y-3">
                      <h3 className="text-2xl sm:text-3xl lg:text-4xl font-black text-gray-900 font-['Open_Sans']">
                        Why Choose
                        <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent"> On-Premise EnMS?</span>
                      </h3>
                      <p className="text-base sm:text-lg text-gray-800 leading-relaxed font-bold font-['Open_Sans']">
                        In today's competitive industrial landscape, optimizing energy consumption isn't just about reducing costs—it's about
                        <span className="text-green-700 font-black"> sustainability, compliance, and gaining a competitive edge</span>.
                        Our On-Premise EnMS delivers a comprehensive solution that transforms how you monitor, manage, and optimize resources across your entire operations.
                      </p>
                    </div>

                    <div className="space-y-3 sm:space-y-4">
                      <div className="group flex items-start space-x-3 sm:space-x-4 p-3 sm:p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-100 hover:shadow-lg transition-all">
                        <div className="bg-green-600 p-2 rounded-lg group-hover:scale-110 transition-transform flex-shrink-0">
                          <CheckCircle className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                        </div>
                        <div>
                          <h4 className="font-black text-gray-900 text-base sm:text-lg mb-1 font-['Open_Sans']">Cost Optimization</h4>
                          <p className="text-gray-800 font-bold text-sm sm:text-base font-['Open_Sans']">Reduce energy costs by 8-15% through intelligent monitoring and optimization.</p>
                        </div>
                      </div>

                      <div className="group flex items-start space-x-3 sm:space-x-4 p-3 sm:p-4 bg-gradient-to-r from-emerald-50 to-teal-50 rounded-xl border border-emerald-100 hover:shadow-lg transition-all">
                        <div className="bg-emerald-600 p-2 rounded-lg group-hover:scale-110 transition-transform flex-shrink-0">
                          <CheckCircle className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                        </div>
                        <div>
                          <h4 className="font-black text-gray-900 text-base sm:text-lg mb-1 font-['Open_Sans']">Sustainability Goals</h4>
                          <p className="text-gray-800 font-bold text-sm sm:text-base font-['Open_Sans']">Meet environmental targets with comprehensive carbon footprint management.</p>
                        </div>
                      </div>

                      <div className="group flex items-start space-x-3 sm:space-x-4 p-3 sm:p-4 bg-gradient-to-r from-teal-50 to-green-50 rounded-xl border border-teal-100 hover:shadow-lg transition-all">
                        <div className="bg-teal-600 p-2 rounded-lg group-hover:scale-110 transition-transform flex-shrink-0">
                          <CheckCircle className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                        </div>
                        <div>
                          <h4 className="font-black text-gray-900 text-base sm:text-lg mb-1 font-['Open_Sans']">Compliance Ready</h4>
                          <p className="text-gray-800 font-bold text-sm sm:text-base font-['Open_Sans']">Built-in support for ISO 50001 and international energy standards.</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="relative mt-6 lg:mt-0">
                    <div className="relative group">
                      <img
                        src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=700&h=500&fit=crop&auto=format"
                        alt="Energy Management Overview"
                        className="w-full h-auto rounded-xl sm:rounded-2xl shadow-2xl group-hover:scale-105 transition-all duration-500 border-2 sm:border-4 border-green-100"
                        loading="lazy"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-green-600/30 via-transparent to-transparent rounded-xl sm:rounded-2xl"></div>
                    </div>

                    {/* Floating Elements */}
                    <div className="absolute -top-2 sm:-top-4 -right-2 sm:-right-4 bg-green-600 text-white p-2 sm:p-3 rounded-lg sm:rounded-xl shadow-xl">
                      <div className="flex items-center space-x-1 sm:space-x-2">
                        <Sparkles className="w-4 h-4 sm:w-5 sm:h-5" />
                        <span className="font-bold text-xs sm:text-sm font-['Open_Sans']">AI-Powered</span>
                      </div>
                    </div>
                  </div>
                </div>
                  </motion.div>
                )}

                {activeTab === 'features' && (
                  <motion.div
                    key="features"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ duration: 0.4 }}
                    className="relative z-10"
                  >
                <div className="space-y-6 sm:space-y-8">
                  <div className="text-center">
                    <h3 className="text-2xl sm:text-3xl lg:text-4xl font-black text-gray-900 mb-3 font-['Open_Sans']">
                      <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                        Core Platform Features
                      </span>
                    </h3>
                    <p className="text-base sm:text-lg lg:text-xl text-gray-800 font-bold max-w-2xl mx-auto font-['Open_Sans']">
                      Powerful capabilities designed to transform your energy management
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                    {coreFeatures.map((feature, index) => (
                      <div key={index} className="group">
                        <div className="flex items-start space-x-3 sm:space-x-4 p-4 sm:p-6 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 rounded-xl sm:rounded-2xl border border-green-100 hover:border-green-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                          <div className="bg-green-600 p-2 rounded-lg group-hover:scale-110 transition-transform shadow-lg flex-shrink-0">
                            <CheckCircle className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                          </div>
                          <span className="text-gray-800 font-bold text-base sm:text-lg leading-relaxed font-['Open_Sans']">{feature}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                  </motion.div>
                )}

                {activeTab === 'benefits' && (
                  <motion.div
                    key="benefits"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ duration: 0.4 }}
                    className="relative z-10"
                  >
                <div className="space-y-6 sm:space-y-8">
                  <div className="text-center">
                    <h3 className="text-2xl sm:text-3xl lg:text-4xl font-black text-gray-900 mb-3 font-['Open_Sans']">
                      <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                        Key Benefits
                      </span>
                    </h3>
                    <p className="text-base sm:text-lg lg:text-xl text-gray-800 font-bold max-w-2xl mx-auto font-['Open_Sans']">
                      Discover the transformative advantages of our energy management solution
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                    {benefits.map((benefit, index) => {
                      const IconComponent = benefit.icon;
                      const gradients = [
                        'from-green-500 to-emerald-600',
                        'from-emerald-500 to-teal-600',
                        'from-teal-500 to-green-600',
                        'from-green-600 to-emerald-700'
                      ];
                      return (
                        <div key={index} className="group">
                          <div className="relative p-6 sm:p-8 bg-white rounded-xl sm:rounded-2xl border border-green-100 hover:border-green-300 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden">
                            {/* Background Gradient */}
                            <div className={`absolute top-0 left-0 w-full h-2 bg-gradient-to-r ${gradients[index % gradients.length]}`}></div>

                            <div className="flex items-start space-x-4 sm:space-x-6">
                              <div className={`bg-gradient-to-br ${gradients[index % gradients.length]} p-3 sm:p-4 rounded-xl sm:rounded-2xl shadow-lg group-hover:scale-110 transition-transform flex-shrink-0`}>
                                <IconComponent className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
                              </div>
                              <div className="flex-1">
                                <h4 className="text-lg sm:text-xl lg:text-2xl font-black text-gray-900 mb-2 group-hover:text-green-700 transition-colors font-['Open_Sans']">{benefit.title}</h4>
                                <p className="text-gray-800 font-bold text-base sm:text-lg leading-relaxed font-['Open_Sans']">{benefit.description}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
                  </motion.div>
                )}

                {activeTab === 'modules' && (
                  <motion.div
                    key="modules"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ duration: 0.4 }}
                    className="relative z-10"
                  >
                <div className="space-y-6 sm:space-y-8">
                  <div className="text-center">
                    <h3 className="text-2xl sm:text-3xl lg:text-4xl font-black text-gray-900 mb-3 font-['Open_Sans']">
                      <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                        Specialized Add-on Modules
                      </span>
                    </h3>
                    <p className="text-base sm:text-lg lg:text-xl text-gray-800 font-bold max-w-2xl mx-auto font-['Open_Sans']">
                      Extend your energy management capabilities with powerful specialized modules
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                    {addOnModules.map((module, index) => {
                      const IconComponent = module.icon;
                      return (
                        <div key={index} className="group">
                          <div
                            className="bg-white/90 backdrop-blur-sm border border-green-100 rounded-xl sm:rounded-2xl p-6 sm:p-8 hover:shadow-2xl hover:border-green-300 transition-all duration-500 cursor-pointer transform hover:-translate-y-2"
                            onClick={() => setSelectedModule(selectedModule === index ? null : index)}
                          >
                            <div className="flex items-center space-x-3 sm:space-x-4 mb-3 sm:mb-4">
                              <div className="bg-gradient-to-br from-green-500 to-emerald-600 group-hover:from-emerald-500 group-hover:to-teal-600 p-2 sm:p-3 rounded-lg sm:rounded-xl transition-all duration-300 shadow-lg flex-shrink-0">
                                <IconComponent className="w-6 h-6 sm:w-7 sm:h-7 text-white" />
                              </div>
                              <h4 className="font-black text-gray-900 text-base sm:text-lg group-hover:text-green-700 transition-colors font-['Open_Sans']">{module.title}</h4>
                            </div>

                            <p className="text-gray-800 font-bold mb-3 sm:mb-4 leading-relaxed text-sm sm:text-base font-['Open_Sans']">{module.description}</p>

                            {selectedModule === index && (
                              <div className="space-y-2 pt-3 sm:pt-4 border-t border-green-100 animate-in slide-in-from-top duration-300">
                                {module.features.map((feature, featureIndex) => (
                                  <div key={featureIndex} className="flex items-start space-x-2 sm:space-x-3">
                                    <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-700 flex-shrink-0 mt-0.5" />
                                    <span className="text-gray-800 font-bold text-sm sm:text-base font-['Open_Sans']">{feature}</span>
                                  </div>
                                ))}
                              </div>
                            )}

                            <div className="flex items-center text-green-700 font-black mt-3 sm:mt-4 group-hover:text-emerald-700 transition-colors font-['Open_Sans']">
                              <span className="text-sm sm:text-base">{selectedModule === index ? 'Hide Details' : 'View Details'}</span>
                              <ArrowRight className={`w-4 h-4 sm:w-5 sm:h-5 ml-2 transition-transform duration-300 ${selectedModule === index ? 'rotate-90' : 'group-hover:translate-x-1'}`} />
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          </div>
        </div>

        {/* Contact Section - Separate & Distinct */}
        <div className="mt-8 sm:mt-12 py-8 sm:py-12 bg-gradient-to-br from-green-100 via-green-50 to-emerald-50 border-t-4 border-green-200">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            {/* Main Heading */}
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-black text-green-700 mb-4 sm:mb-6 font-['Open_Sans']">
              Need a Custom Energy Management Solution?
            </h2>

            {/* Description */}
            <p className="text-base sm:text-lg lg:text-xl text-green-600 font-bold max-w-3xl mx-auto leading-relaxed mb-6 sm:mb-8 font-['Open_Sans']">
              Our engineers can design energy management systems to your specific requirements with industry-leading
              performance and reliability. Get in touch today to discuss your needs.
            </p>

            {/* Enhanced Contact Support Button with Magnetic Effect */}
            <MagneticButton
              onClick={() => navigate('/contact/sales')}
              className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-bold text-lg rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 font-['Open_Sans']"
            >
              <motion.div
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
                className="mr-3"
              >
                <Users className="w-6 h-6" />
              </motion.div>
              <span>Contact Support</span>

              {/* Sparkle Effects */}
              <div className="absolute inset-0 pointer-events-none">
                {Array.from({ length: 4 }).map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute w-1 h-1 bg-white rounded-full"
                    style={{
                      left: `${20 + i * 20}%`,
                      top: `${30 + i * 15}%`,
                    }}
                    animate={{
                      opacity: [0, 1, 0],
                      scale: [0, 1, 0],
                      rotate: [0, 180, 360]
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      delay: i * 0.3,
                      ease: "easeInOut"
                    }}
                  />
                ))}
              </div>
            </MagneticButton>
          </div>
        </div>

        {/* Enhanced Custom Animations and Effects */}
        <style>{`
          @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-15px) rotate(3deg); }
          }
          @keyframes float-delayed {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-12px) rotate(-2deg); }
          }
          @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
          }
          @keyframes pulse-glow {
            0%, 100% {
              box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
              transform: scale(1);
            }
            50% {
              box-shadow: 0 0 40px rgba(34, 197, 94, 0.6);
              transform: scale(1.02);
            }
          }
          @keyframes morphing-gradient {
            0% { border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%; }
            25% { border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%; }
            50% { border-radius: 50% 60% 30% 60% / 30% 60% 70% 40%; }
            75% { border-radius: 60% 40% 60% 30% / 70% 30% 60% 70%; }
            100% { border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%; }
          }
          @keyframes particle-float {
            0% {
              transform: translateY(100vh) translateX(0px) rotate(0deg);
              opacity: 0;
            }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% {
              transform: translateY(-100px) translateX(100px) rotate(360deg);
              opacity: 0;
            }
          }

          .animate-float {
            animation: float 5s ease-in-out infinite;
          }
          .animate-float-delayed {
            animation: float-delayed 6s ease-in-out infinite;
          }
          .animate-shimmer {
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            background-size: 200% 100%;
            animation: shimmer 2s infinite;
          }
          .animate-pulse-glow {
            animation: pulse-glow 3s ease-in-out infinite;
          }
          .animate-morphing {
            animation: morphing-gradient 8s ease-in-out infinite;
          }
          .animate-particle-float {
            animation: particle-float 8s linear infinite;
          }

          /* Glassmorphism effects */
          .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
          }

          /* Magnetic hover effect */
          .magnetic-hover {
            transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          }

          /* Smooth scroll behavior */
          html {
            scroll-behavior: smooth;
          }

          /* Custom scrollbar */
          ::-webkit-scrollbar {
            width: 8px;
          }
          ::-webkit-scrollbar-track {
            background: rgba(34, 197, 94, 0.1);
          }
          ::-webkit-scrollbar-thumb {
            background: linear-gradient(to bottom, #22c55e, #10b981);
            border-radius: 4px;
          }
          ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(to bottom, #16a34a, #059669);
          }
        `}</style>
      </PageLayout>
    </div>
  );
};

export default OnPremiseSystemsPage;