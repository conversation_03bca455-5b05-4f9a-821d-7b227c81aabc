import React from 'react';

const MultimeterProductPage = () => {
  const features = [
    "Models - A101Y BL-2502",
    "1999/6000 AC/DC V",
    "True RMS reading on AC mode",
    "6000-count large-scale digital display",
    "Autoranging and manual selection",
    "Smart safety hold, NCV",
    "Non-Contact Voltage detection",
    "Data Hold/Relative function key",
    "Data Guard",
    "Shock proof from 6-feet drops",
    "Certificate holster with probe holder and UI case",
    "Safety standard CAT II 1000V"
  ];

  const specifications = [
    {
      parameter: "DC Voltage",
      range: "600mV - 1000V",
      accuracy: "0.5%"
    },
    {
      parameter: "AC Voltage",
      range: "600mV - 1000V",
      accuracy: "1%"
    },
    {
      parameter: "DC Current",
      range: "6A/10A",
      accuracy: "1%"
    },
    {
      parameter: "AC Current",
      range: "6A/10A",
      accuracy: "1.5%"
    },
    {
      parameter: "Resistance",
      range: "600Ω-60MΩ",
      accuracy: "0.8%"
    },
    {
      parameter: "Capacitance",
      range: "10nF - 60mF",
      accuracy: "1.5%"
    },
    {
      parameter: "Frequency Counter",
      range: "60Hz - 10MHz",
      accuracy: "0.1%"
    },
    {
      parameter: "Temperature",
      range: "-40°C - 400°C",
      accuracy: "1%"
    },
    {
      parameter: "Diode Test",
      range: "3V",
      accuracy: "-"
    },
    {
      parameter: "Continuity Beeper",
      range: "<35Ω With tone Buzzer",
      accuracy: "-"
    }
  ];

  return (
    <div className="max-w-6xl mx-auto p-6 bg-white">
      {/* Header */}
      <div className="flex flex-col md:flex-row items-center mb-8">
        <div className="w-full md:w-1/3 flex justify-center mb-6 md:mb-0">
          <img 
            src="/api/placeholder/400/400" 
            alt="TRMS AC DC Digital Multimeter" 
            className="h-64 object-contain"
          />
        </div>
        <div className="w-full md:w-2/3 px-4">
          <h1 className="text-3xl font-bold text-gray-800">
            <span className="text-gray-600">TRMS AC DC</span> <span className="text-yellow-500">DIGITAL MULTIMETERS</span>
          </h1>
          
          <div className="mt-6">
            <ul className="space-y-2">
              {features.map((feature, index) => (
                <li key={index} className="flex items-start">
                  <span className="inline-block w-5 h-5 mr-2 bg-yellow-500 rounded-full flex-shrink-0 mt-1"></span>
                  <span>{feature}</span>
                </li>
              ))}
            </ul>
          </div>
          
          <div className="mt-8 flex space-x-4">
            <button className="px-6 py-2 border border-orange-500 text-orange-500 rounded hover:bg-orange-500 hover:text-white transition-colors">
              PURCHASE
            </button>
            <button className="px-6 py-2 border border-orange-500 text-orange-500 rounded hover:bg-orange-500 hover:text-white transition-colors">
              BROCHURE
            </button>
          </div>
        </div>
      </div>
      
      {/* Specifications Table */}
      <div className="mt-12">
        <h2 className="text-2xl font-bold mb-6">Technical Specifications</h2>
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border border-gray-200">
            <thead>
              <tr>
                <th className="py-3 px-4 bg-yellow-500 text-white text-left">PARAMETER</th>
                <th className="py-3 px-4 bg-yellow-500 text-white text-left">RANGE</th>
                <th className="py-3 px-4 bg-yellow-500 text-white text-left">ACCURACY %</th>
              </tr>
            </thead>
            <tbody>
              {specifications.map((spec, index) => (
                <tr key={index} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                  <td className="py-3 px-4 border-b border-gray-200">{spec.parameter}</td>
                  <td className="py-3 px-4 border-b border-gray-200">{spec.range}</td>
                  <td className="py-3 px-4 border-b border-gray-200">{spec.accuracy}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      
      {/* Additional Information */}
      <div className="mt-12 grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <h2 className="text-2xl font-bold mb-4">Product Description</h2>
          <p className="text-gray-700">
            The TRMS AC DC Digital Multimeter is a professional-grade measurement tool designed for electrical testing. With true RMS reading capability, this multimeter provides accurate measurements on AC mode and features a large 6000-count digital display for easy reading. The device offers both autoranging and manual selection options for versatile operation.
          </p>
        </div>
        <div>
          <h2 className="text-2xl font-bold mb-4">Applications</h2>
          <ul className="list-disc pl-5 space-y-2 text-gray-700">
            <li>Electrical installation testing</li>
            <li>Equipment maintenance</li>
            <li>Automotive diagnostics</li>
            <li>HVAC system troubleshooting</li>
            <li>Electronics repair and testing</li>
          </ul>
        </div>
      </div>
      
      {/* Contact Section */}
      <div className="mt-12 bg-gray-100 p-6 rounded-lg">
        <h2 className="text-2xl font-bold mb-4">Need More Information?</h2>
        <p className="mb-4">Contact our technical support team for additional details or to request a personalized demonstration.</p>
        <button className="px-6 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition-colors">
          Contact Support
        </button>
      </div>
    </div>
  );
};

export default MultimeterProductPage;