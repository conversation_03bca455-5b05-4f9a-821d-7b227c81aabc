import React, { useState } from "react";
import { motion } from "framer-motion";
import PageLayout from "@/components/layout/PageLayout";
import ProductTemplate from "@/components/templates/ProductTemplate";

// PDF Viewer Component
const PdfViewer = ({ showPdfViewer, setShowPdfViewer }) => {
  const pdfUrl = "/KRYKARD-Comprehensive-Product-Catalogue.pdf";

  // Function to directly download the PDF without any dialog
  const handleDownloadPdf = () => {
    // Create a hidden anchor element
    const a = document.createElement('a');

    // Set direct download attributes
    a.style.display = 'none';
    a.href = pdfUrl;
    a.setAttribute('download', 'KRYKARD-Comprehensive-Product-Catalogue.pdf');

    // Append to body
    document.body.appendChild(a);

    // Trigger click programmatically
    a.click();

    // Remove element after download is triggered
    document.body.removeChild(a);

    // Force a download by opening in a new window as backup
    if (navigator.userAgent.indexOf('MSIE') !== -1 || navigator.userAgent.indexOf('Trident/') !== -1) {
      window.open(pdfUrl, '_blank');
    }
  };

  // Function to open PDF in a new tab
  const handleOpenInNewTab = () => {
    window.open(pdfUrl, '_blank');
  };

  // Animation variants for modal
  const modalVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    },
    exit: {
      opacity: 0,
      scale: 0.9,
      transition: {
        duration: 0.2,
        ease: "easeIn"
      }
    }
  };

  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center ${showPdfViewer ? '' : 'hidden'}`}>
      {/* Backdrop with blur effect */}
      <div
        className="absolute inset-0 bg-black bg-opacity-70 backdrop-blur-sm"
        onClick={() => setShowPdfViewer(false)}
      ></div>

      {/* Modal content */}
      <motion.div
        className="relative bg-white dark:bg-slate-800 rounded-xl p-6 w-full max-w-5xl max-h-[90vh] overflow-hidden shadow-2xl border border-slate-200 dark:border-slate-700"
        initial="hidden"
        animate={showPdfViewer ? "visible" : "hidden"}
        exit="exit"
        variants={modalVariants}
      >
        {/* Close button with enhanced styling */}
        <button
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 bg-white dark:bg-slate-700 rounded-full p-1 transition-all duration-200 hover:bg-gray-100 dark:hover:bg-slate-600 z-10"
          onClick={() => setShowPdfViewer(false)}
          aria-label="Close PDF viewer"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        {/* Header with title and actions */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 pb-4 border-b border-slate-200 dark:border-slate-700">
          <h3 className="text-xl font-bold text-blue-800 dark:text-blue-400 mb-3 sm:mb-0">KRYKARD Comprehensive Product Catalogue</h3>
          <div className="flex items-center gap-3">
            <button
              onClick={handleOpenInNewTab}
              className="flex items-center gap-2 bg-slate-100 hover:bg-slate-200 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-200 py-2 px-4 rounded-md transition-colors"
              aria-label="Open PDF in new tab"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
              <span className="hidden sm:inline">Open in New Tab</span>
            </button>
            <button
              onClick={handleDownloadPdf}
              className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors"
              aria-label="Download PDF"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
              </svg>
              <span className="hidden sm:inline">Download PDF</span>
            </button>
          </div>
        </div>

        {/* PDF content with loading indicator */}
        <div className="w-full h-[70vh] relative">
          {/* Loading indicator */}
          <div className="absolute inset-0 flex items-center justify-center bg-slate-50 dark:bg-slate-800 z-0">
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mb-4"></div>
              <p className="text-slate-600 dark:text-slate-300">Loading PDF...</p>
            </div>
          </div>

          {/* Direct PDF embedding */}
          <object
            data={pdfUrl}
            type="application/pdf"
            className="w-full h-full relative z-10"
            width="100%"
            height="100%"
          >
            <div className="flex flex-col items-center justify-center h-full bg-slate-100 dark:bg-slate-700 rounded-lg p-8 text-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-red-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <p className="text-slate-700 dark:text-slate-200 text-lg font-medium mb-2">
                PDF preview is not available in your browser
              </p>
              <p className="text-slate-600 dark:text-slate-300 mb-6">
                You can download the PDF or open it in a new tab instead
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <button
                  onClick={handleOpenInNewTab}
                  className="flex items-center justify-center gap-2 bg-slate-200 hover:bg-slate-300 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-800 dark:text-slate-200 py-3 px-6 rounded-lg transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                  Open in New Tab
                </button>
                <button
                  onClick={handleDownloadPdf}
                  className="flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                  Download PDF
                </button>
              </div>
            </div>
          </object>
        </div>
      </motion.div>
    </div>
  );
};

const AtandraEnergy = () => {
  const [showPdfViewer, setShowPdfViewer] = useState(false);

  // Extract the video ID from YouTube URL
  const videoId = "MM8GX4YHKZ0";

  // Handle PDF catalogue click - show the PDF viewer
  const handleCatalogueClick = () => {
    setShowPdfViewer(true);
  };

  return (
    <PageLayout
      title="Atandra Energy Pvt. Ltd."
      subtitle="Power & Energy Management Solutions"
      category="about"
    >
      <div className="w-full bg-gradient-to-br from-yellow-50 via-blue-50 to-green-50">
        {/* Video Section with Enhanced Styling - Full Width */}
        <div className="w-full py-8 px-4">
          <div className="max-w-6xl mx-auto">
            <div className="relative w-full rounded-xl overflow-hidden shadow-xl">
              <div className="relative pt-0 pb-0 h-0" style={{ paddingBottom: "56.25%" }}>
                <iframe
                  id="videoPlayer"
                  src={`https://www.youtube.com/embed/${videoId}?si=TIT1ZtQjyGlK6OmQ`}
                  title="Atandra Energy Corporate Video"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                  className="absolute top-0 left-0 w-full h-full border-none"
                />
              </div>
            </div>
          </div>
        </div>

        {/* About Us Section with Card-like Design - Full Width */}
        <div className="w-full py-12 px-4">
          <div className="max-w-6xl mx-auto bg-white rounded-2xl shadow-lg p-8 border border-gray-100">
            <h2 className="text-3xl font-bold mb-8 text-center bg-gradient-to-r from-yellow-500 via-blue-500 to-green-500 bg-clip-text text-transparent">
              About Us
            </h2>

            <div className="text-base leading-relaxed space-y-4 text-gray-700">
              <p>
                Atandra Energy Pvt. Ltd., headquartered in Chennai, draws upon a rich foundation of more than 39 years of expertise in the realm of Power & Energy Management.
              </p>
              <p>
                We offer solutions to industrial and commercial establishments under our popular brand KRYKARD. With over 5,00,000 installations of Power Conditioners and over 1,50,000 installations of Portable and Panel Load Managers, KRYKARD is one of the leading brands in Energy Management.
              </p>
              <p>
                Our Servo Stabilizers and Transformers have obtained CE certification, providing our customers with the assurance that these products adhere to rigorous global health, safety, and environmental protection standards.
              </p>
              <p>
                We have the following in-house facilities:
              </p>
            </div>

            {/* Departments List with Enhanced Styling */}
            <div className="mt-8 space-y-5">
              {[
                "An ISO 9001:2015 | 14001 – 2015 | 45001 – 2015 | 500001",
                "R&D department for Power Electronics and Electro-magnetics.",
                "A Software Development department to design, develop, and customize Energy Management Software.",
                "Our state-of-the-art facilities empower us to address the requirements of Indian industries comprehensively and effectively, ensuring they derive maximum benefits from the energy management solutions we provide. Boasting a workforce exceeding 450 employees and an extensive network of branches nationwide, we are well-equipped to seamlessly reach out to our customers and fulfill their needs."
              ].map((item, index) => (
                <div key={index} className="flex items-start p-4 bg-gradient-to-r from-yellow-50 via-blue-50 to-green-50 rounded-lg">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-yellow-400 via-blue-500 to-green-400 mr-4 flex-shrink-0 flex justify-center items-center text-white font-bold">
                    {index + 1}
                  </div>
                  <p className="text-gray-700">{item}</p>
                </div>
              ))}
            </div>

            {/* Product Catalogue and Company Name Section */}
            <div className="mt-12 flex flex-col md:flex-row justify-between items-center gap-8">
              {/* Left side: PDF Catalogue */}
              <button
                type="button"
                className="border-2 border-blue-500 text-blue-600 hover:bg-blue-50 dark:text-blue-300 dark:border-blue-700 dark:hover:bg-blue-900/30 px-8 py-6 rounded-xl font-semibold transition-all duration-300 transform hover:-translate-y-1 relative group"
                onClick={handleCatalogueClick}
              >
                {/* Add a subtle glow effect on hover */}
                <div className="absolute -inset-1 bg-blue-500/20 rounded-xl blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative flex items-center">
                  <span className="mr-2">VIEW PDF BROCHURE</span>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clipRule="evenodd" />
                  </svg>
                </div>
              </button>

              {/* Right side: Company Name */}
              <div className="p-6 rounded-xl bg-gradient-to-r from-yellow-100 via-blue-100 to-green-100">
                <h3 className="text-xl font-bold bg-gradient-to-r from-yellow-600 via-blue-600 to-green-600 bg-clip-text text-transparent">
                  ATANDRA ENERGY MANAGEMENT PVT LTD
                </h3>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Section - Full Width */}
        <div className="w-full bg-gradient-to-r from-yellow-50 via-blue-50 to-green-50 py-16 px-4 border-t border-b border-gray-200">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-10">
              <h2 className="text-3xl font-bold mb-5 text-center text-green-600">
                Need More Information?
              </h2>

              <p className="text-gray-700 text-lg mx-auto text-center mb-10">
                Our team of experts is ready to help you with product specifications, custom solutions, pricing, and any other details you need about the KRYKARD Static Voltage Regulator.
              </p>
            </div>

            <div className="flex justify-center">
              <button
                type="button"
                className="bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white font-semibold py-4 px-10 rounded-xl flex items-center gap-3 shadow-xl transform hover:-translate-y-1 transition-all duration-300"
                style={{ boxShadow: "0 10px 30px rgba(22, 163, 74, 0.4)" }}
                onClick={() => window.location.href = "/contact/sales"}
              >
                <span>Contact Our Experts</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* PDF Viewer Component */}
        <PdfViewer showPdfViewer={showPdfViewer} setShowPdfViewer={setShowPdfViewer} />
      </div>
    </PageLayout>
  );
};

export default AtandraEnergy;