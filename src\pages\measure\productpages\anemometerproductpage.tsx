import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import Layout from "@/components/layout/Layout";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
// import PdfViewer from "@/components/ui/pdf-viewer";

interface ProductSpec {
  label: string;
  description: string;
}

interface TableRow {
  parameter: string;
  range: string;
  accuracy: string;
}

interface Product {
  id: string;
  name: string;
  title: string;
  subtitle?: string;
  model: string;
  image: string;
  description?: string;
  features: string[];
  specs: ProductSpec[];
  hasRangeTable?: boolean;
  rangeData?: TableRow[];
}

const AnemometerProductPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('thermo-anemometer');
  
  const products: Product[] = [
    {
      id: 'thermo-anemometer',
      name: 'Thermo Anemometer',
      title: 'THERMO',
      subtitle: 'ANEMOMETER',
      model: 'C.A 1227',
      image: '/images/thermo-anemometer.png',
      description: 'Equipped with a wide backlit display, the C.A 1227 offers all the useful functions for measuring air speed and flow rate in the field, such as Min, Max, Average, MAP and Hold as well as extensive recording capabilities. With the cones available as accessories, it is possible to perform direct flow rate measurements. The Data Logger Transfer software is available for free download to view the recorded data, configure the C.A 1227, benefit from remote display and generate automatic reports.',
      features: [
        'Models: C.A 1227',
        'Wide backlit display',
        'MAP function for mapping the air speeds measured',
        'MIN, MAX, Average and Hold functions',
        'Flow rate measurement',
        'Recording up to 1 million points',
        'Communicating via USB or Bluetooth',
        'Data Logger Transfer software with automatic report generation'
      ],
      specs: [
        { label: 'Flow rate measurement range', description: '0.25 m/s to 35.0 m/s (intrinsic uncertainty: ±3 % of reading ±4 counts)' },
        { label: 'Flow rate measurement with the cones kit', description: 'Available as an accessory Square cross-section: 346 x 346 mm / Circular cross-section: Ø210 mm' },
        { label: 'Temperature measurement range', description: '-20.0 to +50.0 °C (intrinsic uncertainty: 0 to 50°C: ±0.8°C / -20 to 0 °C: ±1.6°C' },
        { label: 'Min, Max, Average, MAP and Hold functions', description: 'Backlighting' },
        { label: 'Air speed units', description: 'm/s, km/h, fpm, mph' },
        { label: 'Flow rate units', description: 'm3/s, m3/h, l/s, cfm' },
        { label: 'Recording', description: 'up to 1 million points' },
        { label: 'USB or Bluetooth interfaces', description: '' },
        { label: 'Vane diameter', description: '35 mm' },
        { label: 'Casing dimensions', description: '150 x 72 x 32 mm / Sensor: 160 x 80 x 38 mm' },
        { label: 'Weight', description: '400 g with batteries' },
        { label: 'Compatible with the MultiFix accessory', description: '' },
        { label: 'Shockproof protective sheath', description: 'available as an accessory' }
      ]
    },
    {
      id: 'hot-wire-anemometer',
      name: 'Hot Wire Anemometer',
      title: 'HOT WIRE',
      subtitle: 'ANEMOMETER',
      model: 'P01511301',
      image: '/images/hot-wire-anemometer.png',
      features: [
        'Models: P01511301',
        'Air velocity measurement: m/s, km/h, fpm, mph',
        'Min & hold of temperature (°C / °F humidity (%))',
        'Air velocity computation in atmospheric pressure',
        'Air flow measure: CMM, CFM',
        'Calculating the vent profile',
        'Light logistics',
        'Monitoring & 2000pts function with USB interface supply power',
        'Auto Hold function'
      ],
      specs: [
        { label: 'Air velocity', description: 'Measurement in m/s, km/h, fpm, mph' },
        { label: 'Air flow measure', description: 'CMM, CFM' },
        { label: 'Temperature measurement', description: 'In °C and °F' },
        { label: 'Min & hold of temperature', description: '°C / °F humidity (%)' },
        { label: 'Air velocity computation', description: 'In atmospheric pressure' },
        { label: 'Monitoring function', description: '2000pts with USB interface supply power' },
        { label: 'Auto Hold function', description: '' }
      ],
      hasRangeTable: true,
      rangeData: [
        { parameter: 'Air Velocity', range: '0-30 m/s', accuracy: '3' },
        { parameter: 'Air Flow', range: '0-9999m³/min', accuracy: '-' },
        { parameter: 'Temperature(°C)', range: '-20°C - 80°C', accuracy: '0.8' },
        { parameter: 'Temperature(°F)', range: '-4°F - 176°F', accuracy: '1.5' },
        { parameter: 'Humidity', range: '0-100%r.h.', accuracy: '3.5' },
        { parameter: 'Sampling Rate', range: '2 times/sec', accuracy: '-' }
      ]
    },
    {
      id: 'lux-meter',
      name: 'Lux Meter',
      title: 'LUX',
      subtitle: 'METER',
      model: 'CA 1110',
      image: '/images/lux-meter.png',
      features: [
        'Models: CA 1110',
        'Wide measurement range',
        'High precision photometric sensor',
        'Illuminance measurement in Lux or fc',
        'Data hold function',
        'MAX/MIN/AVG measurements',
        'Automatic or manual ranging',
        'Auto power off'
      ],
      specs: [
        { label: 'Measurement range', description: '0.1 to 200,000 lux / 0.01 to 18,580 fc' },
        { label: 'Resolution', description: '0.1 lux / 0.01 fc' },
        { label: 'Accuracy', description: '±3% of reading ±3 digits' },
        { label: 'Spectral response', description: 'Photopic (human eye response)' },
        { label: 'Display', description: 'LCD with backlight' },
        { label: 'Battery life', description: 'Approx. 100 hours' },
        { label: 'Dimensions', description: '160 x 72 x 32 mm' },
        { label: 'Weight', description: '220g (with battery)' }
      ]
    }
  ];
  
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };
  
  const activeProduct = products.find(product => product.id === activeTab);
  
  return (
    <Layout>
      <div className="pt-24 md:pt-32">
        {/* Product Tabs */}
        <div className="bg-white border-b">
          <div className="container mx-auto px-4">
            <div className="flex overflow-x-auto py-4 space-x-6">
              {products.map(product => (
                <button
                  key={product.id}
                  className={`whitespace-nowrap px-4 py-2 font-medium rounded-md transition-colors ${
                    activeTab === product.id 
                      ? 'bg-yellow-500 text-white' 
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => handleTabChange(product.id)}
                >
                  {product.name}
                </button>
              ))}
            </div>
          </div>
        </div>
        
        {/* Main Content */}
        {activeProduct && (
          <main className="flex-grow container mx-auto px-4 py-8">
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              {/* Product Hero Section */}
              <div className="relative">
                <div className={`absolute inset-0 ${activeTab === 'thermo-anemometer' ? 'bg-gray-100' : activeTab === 'hot-wire-anemometer' ? 'bg-yellow-50' : 'bg-gray-50'}`}></div>
                <div className="relative flex flex-col md:flex-row p-6 md:p-12 items-center">
                  <div className="w-full md:w-1/3 flex justify-center mb-8 md:mb-0">
                    <div className="bg-white p-4 rounded-lg shadow-md transform hover:scale-105 transition-transform duration-300">
                      <img 
                        src={activeProduct.image} 
                        alt={activeProduct.name} 
                        className="max-h-80 object-contain"
                      />
                    </div>
                  </div>
                  <div className="w-full md:w-2/3 md:pl-12">
                    <div className="flex flex-col">
                      <h1 className="text-4xl font-normal text-gray-700 mb-2">
                        {activeProduct.title}
                      </h1>
                      {activeProduct.subtitle && (
                        <h2 className="text-4xl font-bold text-yellow-500 mb-6">
                          {activeProduct.subtitle}
                        </h2>
                      )}
                    </div>
                    <p className="text-lg font-medium mb-4">Model: {activeProduct.model}</p>
                    
                    {/* Features List */}
                    <div className="mb-6">
                      <ul className="space-y-3">
                        {activeProduct.features.map((feature, index) => (
                          <li key={index} className="flex items-start">
                            <div className="flex-shrink-0 h-5 w-5 rounded-full bg-yellow-500 mt-1 mr-3"></div>
                            <span className="text-gray-700">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div className="flex space-x-4 mt-6">
                      <Button variant="outline" className="border-yellow-500 text-yellow-500 hover:bg-yellow-50">
                        ENQUIRE
                      </Button>
                      <Button variant="outline" className="border-yellow-500 text-yellow-500 hover:bg-yellow-50">
                        BROCHURE
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Product Description */}
              {activeProduct.description && (
                <div className="p-6 md:p-12 bg-yellow-50 border-t border-yellow-100">
                  <div className="flex flex-col md:flex-row">
                    <div className="w-full md:w-2/3 md:pr-8">
                      <p className="text-gray-700 leading-relaxed">
                        {activeProduct.description}
                      </p>
                    </div>
                    <div className="w-full md:w-1/3 mt-6 md:mt-0">
                      <img 
                        src="/images/product-in-use.jpg" 
                        alt="Product in use" 
                        className="rounded-lg shadow-md w-full h-auto" 
                      />
                    </div>
                  </div>
                </div>
              )}
              
              {/* Product Specifications */}
              <div className="p-6 md:p-12 border-t border-gray-200">
                <h3 className="text-2xl font-bold text-gray-700 mb-8">Specifications</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {activeProduct.specs.map((spec, index) => (
                    <div key={index} className="flex items-start">
                      <div className="flex-shrink-0 flex items-start">
                        <div className="h-2 w-2 rounded-full bg-gray-500 mt-2 mr-2"></div>
                      </div>
                      <div>
                        <span className="font-bold text-gray-700">{spec.label}: </span>
                        <span className="text-gray-600">{spec.description}</span>
                      </div>
                    </div>
                  ))}
                </div>
                
                {/* Range Table */}
                {activeProduct.hasRangeTable && activeProduct.rangeData && (
                  <div className="mt-12 overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr>
                          <th className="px-4 py-3 bg-yellow-500 text-white font-bold text-left">CHARACTERISTICS</th>
                          <th className="px-4 py-3 bg-yellow-500 text-white font-bold text-left">RANGE</th>
                          <th className="px-4 py-3 bg-yellow-500 text-white font-bold text-left">ACCURACY (%)</th>
                        </tr>
                      </thead>
                      <tbody>
                        {activeProduct.rangeData.map((row, index) => (
                          <tr key={index} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                            <td className="px-4 py-3 border border-gray-200">{row.parameter}</td>
                            <td className="px-4 py-3 border border-gray-200">{row.range}</td>
                            <td className="px-4 py-3 border border-gray-200">{row.accuracy}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
              
              {/* Features Section */}
              <div className="p-6 md:p-12 bg-gray-50 border-t border-gray-200">
                <h3 className="text-2xl font-bold text-gray-700 mb-8">Key Benefits</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                    </div>
                    <h4 className="text-lg font-bold text-gray-700 mb-2">High Accuracy</h4>
                    <p className="text-gray-600">Precise measurements for professional field applications with minimal uncertainty.</p>
                  </div>
                  
                  <div className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                      </svg>
                    </div>
                    <h4 className="text-lg font-bold text-gray-700 mb-2">User-Friendly Design</h4>
                    <p className="text-gray-600">Wide backlit display and intuitive controls for easy operation in all working conditions.</p>
                  </div>
                  
                  <div className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                      </svg>
                    </div>
                    <h4 className="text-lg font-bold text-gray-700 mb-2">Advanced Data Management</h4>
                    <p className="text-gray-600">Record up to 1 million points with USB and Bluetooth connectivity for comprehensive analysis.</p>
                  </div>
                </div>
              </div>
              
              {/* Related Products */}
              <div className="p-6 md:p-12 border-t border-gray-200">
                <h3 className="text-2xl font-bold text-gray-700 mb-8">Related Products</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                  {products.filter(product => product.id !== activeTab).map(product => (
                    <div key={product.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                      <div className="p-4">
                        <img 
                          src={product.image} 
                          alt={product.name} 
                          className="w-full h-40 object-contain mb-4" 
                        />
                        <h4 className="text-lg font-bold text-gray-700 mb-2">{product.name}</h4>
                        <p className="text-sm text-gray-600 mb-4">Model: {product.model}</p>
                        <Button 
                          className="w-full bg-yellow-500 text-white hover:bg-yellow-600"
                          onClick={() => handleTabChange(product.id)}
                        >
                          View Details
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              {/* Call to Action */}
              <div className="p-6 md:p-12 bg-gray-800 text-white text-center">
                <h3 className="text-2xl font-bold mb-4">Need Professional Measurement Solutions?</h3>
                <p className="text-gray-300 mb-6 max-w-2xl mx-auto">Contact our team of experts to find the right equipment for your specific field measurement needs.</p>
                <Button asChild className="px-8 py-3 bg-yellow-500 text-white hover:bg-yellow-600">
                  <Link to="/contact">REQUEST A QUOTE</Link>
                </Button>
              </div>
            </div>
          </main>
        )}
      </div>
    </Layout>
  );
};

export default AnemometerProductPage;