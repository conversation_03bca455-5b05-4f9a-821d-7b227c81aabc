import { Shield, Zap, Settings, Award, CheckCircle, Star, Mail, Download, Info } from 'lucide-react';
import PageLayout from "@/components/layout/PageLayout";

const StaticStabilizersProduct = () => {

  const features = [
    {
      icon: <Shield className="w-8 h-8" />,
      title: "Wide Product Range",
      items: [
        "Ratings 15 - 200 kVA 3 Phase",
        "Multiple input ranges to suit various operating environments",
        "LS Series: 15 - 100 kVA",
        "HS Series: 125 - 200 kVA"
      ]
    },
    {
      icon: <Zap className="w-8 h-8" />,
      title: "Fast Voltage Correction",
      items: [
        "Any variation within input range is corrected within 20 ms",
        "Steady Output Voltage - No Hunting or Overshoot",
        "True RMS Sensing and Correction",
        "High Speed 20 kHz PWM switching using DSP technology"
      ]
    },
    {
      icon: <Settings className="w-8 h-8" />,
      title: "Complete Protection",
      items: [
        "Short Circuit, Under voltage, Over voltage & Overload trip",
        "Single Phasing & Phase Reversal trip",
        "Spike & Surge Protection",
        "Service & Manual Bypass provision"
      ]
    },
    {
      icon: <Award className="w-8 h-8" />,
      title: "IGBT Technology",
      items: [
        "Fully Solid State - IGBT Technology",
        "Lower output load supply impedance",
        "No Harmonic distortion",
        "Noise Free - Silent operation"
      ]
    }
  ];

  const specifications = [
    { parameter: "Ratings", "3Phase": "15 to 200 kVA", "1Phase": "3 Phase, 4 Wire" },
    { parameter: "Nature of Cooling", "3Phase": "Air Cooled", "1Phase": "Air Cooled" },
    { parameter: "Control Type", "3Phase": "True RMS Sensing and Correction", "1Phase": "20 kHz PWM switching using DSP" },
    { parameter: "Input Voltage Range", "3Phase": "360 V - 460 V @ 410 V", "1Phase": "320 V - 460 V @ 400 V" },
    { parameter: "Output Voltage", "3Phase": "410 V ±1%", "1Phase": "410 V ±1%" },
    { parameter: "Voltage Regulation", "3Phase": "±1%", "1Phase": "±1%" },
    { parameter: "Efficiency", "3Phase": ">98%", "1Phase": ">98%" },
    { parameter: "Input Frequency", "3Phase": "47 Hz to 53 Hz", "1Phase": "47 Hz to 53 Hz" },
    { parameter: "Wave Form", "3Phase": "Same as Input", "1Phase": "Same as Input" },
    { parameter: "Power Factor Effect", "3Phase": "Nil", "1Phase": "Nil" },
    { parameter: "Voltage Sensing Time", "3Phase": "< 10 ms", "1Phase": "< 10 ms" },
    { parameter: "Voltage Correction Time", "3Phase": "< 10 ms", "1Phase": "< 10 ms" },
    { parameter: "Under/Over Voltage Cutoff", "3Phase": "Electronic cutoff ±15%/10%", "1Phase": "Electronic cutoff ±15%/10%" },
    { parameter: "Overload Cutoff", "3Phase": "CT based @ 110%", "1Phase": "CT based @ 110%" },
    { parameter: "Short Circuit Protection", "3Phase": "MCB/MCCB provided", "1Phase": "MCB/MCCB provided" },
    { parameter: "Single Phasing Prevention", "3Phase": "Provided", "1Phase": "N/A" },
    { parameter: "Phase Reversal Trip", "3Phase": "Provided", "1Phase": "N/A" },
    { parameter: "Transient Protection", "3Phase": "Type 2 Surge Suppressor", "1Phase": "MOV at Output" },
    { parameter: "Manual Bypass", "3Phase": "Provided", "1Phase": "Provided" },
    { parameter: "Emergency Off-Switch", "3Phase": "Provided", "1Phase": "Provided" },
    { parameter: "Display Type", "3Phase": "7 Line LCD Panel", "1Phase": "7 Line LCD Panel" },
    { parameter: "Operating Temperature", "3Phase": "0 to 50°C", "1Phase": "0 to 50°C" },
    { parameter: "Duty Cycle", "3Phase": "Continuous", "1Phase": "Continuous" },
    { parameter: "IP Class", "3Phase": "IP-20, Indoor", "1Phase": "IP-20, Indoor" }
  ];

  return (
    <PageLayout
      title="Static Voltage Stabilizers"
      subtitle="Advanced power protection solutions for your needs"
      category="protect"
    >
      <div className="bg-gradient-to-br from-blue-50 via-blue-100 to-blue-200 font-sans -mx-6 -mt-24 pt-16">
        {/* Hero Content Section - Integrated with PageLayout */}
        <div className="w-full bg-gradient-to-r from-blue-800 via-blue-700 to-blue-900 text-white relative">
          {/* Background Image with Transparency */}
          <div
            className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20"
            style={{
              backgroundImage: "url('https://images.unsplash.com/photo-1581092918056-0c4c3acd3789?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')"
            }}
          ></div>
          <div className="w-full px-6 md:px-12 lg:px-16 py-16 md:py-24 relative z-10">
            <div className="max-w-4xl mx-auto text-center">
              <div className="space-y-8">
                <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                  Static Voltage Stabilizers
                </h2>
                <p className="text-xl md:text-2xl text-blue-100 leading-relaxed">
                  Cutting-edge IGBT technology with DSP control delivering unparalleled voltage regulation and comprehensive power protection for industrial and commercial applications.
                </p>
                <div className="flex items-center justify-center gap-6">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-7 h-7 text-yellow-400 fill-current" />
                    ))}
                    <span className="ml-4 text-xl text-blue-100 font-medium">Industry Leading Technology</span>
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row gap-6 justify-center pt-4">
                  <button
                    onClick={() => {
                      // Redirect to Sales page for quote
                      window.location.href = '/contact/sales';
                    }}
                    className="bg-blue-600 text-white px-8 py-4 rounded-lg font-bold text-lg hover:bg-blue-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
                  >
                    <Mail className="w-6 h-6 inline mr-3" />
                    Get Quote
                  </button>
                  <button
                    onClick={() => {
                      // Create a link to download the PDF brochure
                      const link = document.createElement('a');
                      link.href = '/brochures/static-stabilizers-brochure.pdf';
                      link.download = 'Static-Stabilizers-Brochure.pdf';
                      link.click();
                    }}
                    className="bg-white text-blue-800 px-8 py-4 rounded-lg font-bold text-lg hover:bg-blue-50 transition-all duration-300 transform hover:scale-105 shadow-lg"
                  >
                    <Download className="w-6 h-6 inline mr-3" />
                    Download Brochure
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

      {/* Overview Section - Full Width */}
      <div className="w-full bg-white">
        <div className="w-full px-6 md:px-12 lg:px-16 py-16">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-8">
              Advanced Power Protection Solutions
            </h2>
            <p className="text-xl md:text-2xl text-gray-700 leading-relaxed text-justify max-w-6xl mx-auto">
              Our Static Voltage Stabilizers combine cutting-edge IGBT technology with DSP control to deliver unparalleled voltage regulation and comprehensive power protection for industrial and commercial applications. Built to meet international standards with superior performance and reliability.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl shadow-xl p-8 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border border-blue-200">
                <div className="flex flex-col items-center text-center mb-6">
                  <div className="bg-gradient-to-br from-blue-600 to-blue-800 rounded-2xl p-4 shadow-lg mb-4">
                    <div className="text-white">{feature.icon}</div>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900">{feature.title}</h3>
                </div>
                <ul className="space-y-4">
                  {feature.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-green-600 mt-1 mr-3 flex-shrink-0" />
                      <span className="text-base text-gray-800 leading-relaxed text-justify font-medium">{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Technical Specifications Section - Full Width */}
      <div className="w-full bg-gradient-to-br from-blue-900 to-blue-800">
        <div className="w-full px-6 md:px-12 lg:px-16 py-16">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Technical Specifications
            </h2>
            <p className="text-xl text-blue-100 leading-relaxed text-justify max-w-4xl mx-auto">
              Detailed technical parameters and standards ensuring optimal performance and reliability across all applications.
            </p>
          </div>

          <div className="bg-white rounded-3xl shadow-2xl overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-gradient-to-r from-blue-700 to-blue-800">
                    <th className="px-8 py-6 text-left text-lg font-bold text-white border-b-2 border-blue-600">
                      Parameter
                    </th>
                    <th className="px-8 py-6 text-center text-lg font-bold text-white border-b-2 border-blue-600">
                      3-Phase
                    </th>
                    <th className="px-8 py-6 text-center text-lg font-bold text-white border-b-2 border-blue-600">
                      1-Phase / 2-Phase
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {specifications.map((spec, index) => (
                    <tr key={index} className={`${index % 2 === 0 ? 'bg-blue-50' : 'bg-white'} hover:bg-blue-100 transition-colors duration-200`}>
                      <td className="px-8 py-5 text-base font-bold text-gray-900 border-b border-blue-200 bg-gradient-to-r from-blue-100 to-blue-50">
                        {spec.parameter}
                      </td>
                      <td className="px-8 py-5 text-base text-gray-800 border-b border-blue-200 text-center font-medium">
                        {spec['3Phase']}
                      </td>
                      <td className="px-8 py-5 text-base text-gray-800 border-b border-blue-200 text-center font-medium">
                        {spec['1Phase']}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="bg-gradient-to-r from-blue-100 to-blue-50 px-8 py-6 border-t-2 border-blue-200">
              <p className="text-base text-gray-800 font-medium flex items-center justify-center">
                <Info className="w-6 h-6 inline mr-3 text-blue-600" />
                All specifications are subject to standard tolerances and testing conditions as per international standards.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Key Features Section - Full Width */}
      <div className="w-full bg-gradient-to-br from-blue-100 to-blue-200">
        <div className="w-full px-6 md:px-12 lg:px-16 py-16">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-8">
              Key Features & Benefits
            </h2>
            <p className="text-xl md:text-2xl text-gray-700 leading-relaxed text-justify max-w-6xl mx-auto">
              Discover the advanced features that make our Static Voltage Stabilizers the preferred choice for critical power applications. Each feature is designed to deliver maximum performance, safety, and reliability.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {features.map((feature, index) => (
              <div key={index} className="bg-white rounded-3xl shadow-2xl p-10 hover:shadow-3xl transition-all duration-500 transform hover:scale-105 border-2 border-blue-300">
                <div className="flex items-center mb-8">
                  <div className="bg-gradient-to-br from-blue-600 to-blue-800 rounded-2xl p-5 shadow-xl">
                    <div className="text-white">{feature.icon}</div>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 ml-6">{feature.title}</h3>
                </div>
                <div className="space-y-5">
                  {feature.items.map((item, itemIndex) => (
                    <div key={itemIndex} className="flex items-start">
                      <div className="bg-green-100 rounded-full p-2 mr-4 mt-1">
                        <CheckCircle className="w-5 h-5 text-green-600" />
                      </div>
                      <span className="text-lg text-gray-800 leading-relaxed text-justify font-medium">{item}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Call to Action Section - Full Width */}
      <div className="w-full bg-gradient-to-r from-blue-800 via-blue-700 to-blue-900 text-white">
        <div className="w-full px-6 md:px-12 lg:px-16 py-20">
          <div className="text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-8">
              Ready to Upgrade Your Power Protection?
            </h2>
            <p className="text-xl md:text-2xl text-blue-100 mb-12 max-w-4xl mx-auto leading-relaxed text-justify">
              Get in touch with our experts to find the perfect Static Voltage Stabilizer solution for your needs. We provide comprehensive support from consultation to installation and maintenance.
            </p>
            <div className="flex justify-center">
              <button
                onClick={() => {
                  // Redirect to Sales page
                  window.location.href = '/contact/sales';
                }}
                className="bg-blue-600 text-white px-10 py-5 rounded-xl font-bold text-xl hover:bg-blue-500 transition-all duration-300 transform hover:scale-105 shadow-xl"
              >
                <Mail className="w-6 h-6 inline mr-3" />
                Contact Expert
              </button>
            </div>
          </div>
        </div>
      </div>
      </div>
    </PageLayout>
  );
};

export default StaticStabilizersProduct;