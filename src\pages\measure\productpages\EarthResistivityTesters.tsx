import React, { useState } from 'react';
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import PageLayout from "@/components/layout/PageLayout";

const EarthResistivityTesters = () => {
  const [activeTab, setActiveTab] = useState('6470n');

  const testerData = {
    '6470n': {
      title: 'C.A 6470N',
      image: '/api/placeholder/400/320',
      features: [
        'Earth tester offering soil resistivity, earth coupling and continuity measurements',
        'Ergonomic, comprehensive and accurate measurements with direct access via rotary switch',
        'Compact, rugged and leakproof design for on-site use',
        'Color-coded terminals for simplified connection',
        'Large backlit LCD screen and rechargeable battery',
        'USB communication output for data export',
        'Approximately 10x greater accuracy with four-pole earth measurements',
        'Measurement range: 0.001 Ω to 100 kΩ',
        'Adjustable measurement frequency between 41 Hz and 512 Hz'
      ],
      specs: [
        'Power supply: NiMh rechargeable battery with external charger',
        'IP53 protection',
        'Compliant with IEC 61557-1-4-5',
        'Safety: IEC 61010 50V CAT IV',
        'Dimensions: 272 x 250 x 128 mm, 3 kg'
      ]
    },
    '6472': {
      title: 'C.A 6472 / C.A 6474',
      image: '/api/placeholder/400/320',
      features: [
        '3P measurement up to 10 kΩ and stray voltages up to 60 V RMS',
        '4P and selective 4P measurement',
        'Earth loop measurement with 2 clamps from 0.01 Ω to 500 Ω',
        'Earth coupling measurement',
        'Soil resistivity measurement using Wenner or Schlumberger methods',
        'Ground potential measurement',
        'Continuity and resistance measurement',
        'Pylon earth measurement with the C.A 6474',
        'Extensive measurement frequency range (41 to 5,078 Hz)',
        'Possibility to analyze frequency behavior of earth connections'
      ],
      specs: [
        'Storage of 512 test results',
        'USB optical link',
        'Ground Tester Transfer PC software to process the stored data',
        'Power supply: NiMh battery with external charger',
        'IP53 protection',
        'IEC 61557-1-4-5',
        'IEC 61010 50V CAT IV',
        'Dimensions: 272 x 250 x 128 mm, 3 kg'
      ]
    }
  };

  const activeData = testerData[activeTab];

  return (
    <PageLayout
      title="Earth Resistivity Testers"
      subtitle="Precision instruments for measuring soil resistivity and earth resistance for safe grounding systems."
      category="measure"
    >
      <section className="mb-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8 text-center"
        >
          <h2 className="text-3xl font-bold mb-4">Advanced Earth Resistivity Testing Solutions</h2>
          <p className="text-muted-foreground mb-6 max-w-3xl mx-auto">
            High-precision devices for comprehensive earth measurements and soil resistivity analysis.
          </p>
        </motion.div>

        <div className="flex flex-col md:flex-row w-full max-w-6xl mx-auto bg-white">
          <div className="w-full md:w-1/3 p-4">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              className="bg-yellow-400 p-4 rounded-md"
            >
              <nav className="flex flex-col space-y-2">
                <button
                  onClick={() => setActiveTab('6470n')}
                  className={`p-2 rounded text-left ${activeTab === '6470n' ? 'bg-yellow-600 text-black' : 'bg-yellow-300'}`}
                >
                  C.A 6470N / 6471 Series
                </button>
                <button
                  onClick={() => setActiveTab('6472')}
                  className={`p-2 rounded text-left ${activeTab === '6472' ? 'bg-yellow-600 text-black' : 'bg-yellow-300'}`}
                >
                  C.A 6472 / 6474 Series
                </button>
              </nav>
            </motion.div>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="w-full md:w-2/3 p-4"
          >
            <div className="flex flex-col md:flex-row mb-6">
              <div className="w-full md:w-1/2 p-4">
                <img
                  src={activeData.image}
                  alt={`${activeData.title} Earth & Resistivity Tester`}
                  className="w-full h-auto rounded-md border-4 border-yellow-400"
                />
              </div>

              <div className="w-full md:w-1/2 p-4">
                <h2 className="text-2xl font-bold mb-4">{activeData.title}</h2>
                <div className="bg-yellow-100 p-4 rounded-md">
                  <h3 className="text-xl font-semibold mb-2">Performance</h3>
                  <p className="text-sm">
                    {activeTab === '6470n' ?
                      'The C.A 6470N offers approximately 10 times greater accuracy thanks to its four-pole earth measurements. This method allows measurements of low earth resistance values, which are essential to meet the requirements of power distribution and transmission companies.' :
                      'The C.A 6472 can be used for quick, comprehensive surveys of all earthing configurations, offering all the necessary earth measurement functions in a single instrument. When used with the C.A 6474, it can also perform pylon earth measurements.'
                    }
                  </p>
                </div>
              </div>
            </div>

            <div className="mb-6">
              <h3 className="text-xl font-semibold mb-2">Key Features</h3>
              <ul className="space-y-2">
                {activeData.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center mr-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h3 className="text-xl font-semibold mb-2">Other Specifications</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {activeData.specs.map((spec, index) => (
                  <div key={index} className="bg-gray-100 p-2 rounded">
                    <span className="text-sm">{spec}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="mt-6 flex space-x-4">
              <button className="bg-yellow-400 hover:bg-yellow-500 text-gray-800 font-bold py-2 px-4 rounded">
                ENQUIRE
              </button>
              <button className="bg-white border-2 border-yellow-400 hover:bg-yellow-100 text-gray-800 font-bold py-2 px-4 rounded">
                BROCHURE
              </button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Related Products Section */}
      <section className="mb-16 pt-8 border-t border-border">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8 text-center"
        >
          <h2 className="text-3xl font-bold mb-4">Related Products</h2>
          <p className="text-muted-foreground mb-6 max-w-3xl mx-auto">
            Explore our other safety instruments and testing solutions
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="bg-background border border-border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow"
          >
            <div className="p-4 flex flex-col h-full">
              <div className="mb-4">
                <img
                  src="/api/placeholder/400/320"
                  alt="Insulation Testers"
                  className="w-full h-48 object-contain"
                />
              </div>
              <h3 className="text-xl font-semibold mb-2">Insulation Testers</h3>
              <p className="text-muted-foreground text-sm mb-4 flex-grow">
                High-precision devices for measuring insulation resistance and integrity in electrical systems.
              </p>
              <Link
                to="/measure/safety-instruments/insulation-testers"
                className="text-primary hover:text-primary/80 text-sm font-medium"
              >
                View Products
              </Link>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-background border border-border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow"
          >
            <div className="p-4 flex flex-col h-full">
              <div className="mb-4">
                <img
                  src="/api/placeholder/400/320"
                  alt="Installation Testers"
                  className="w-full h-48 object-contain"
                />
              </div>
              <h3 className="text-xl font-semibold mb-2">Installation Testers</h3>
              <p className="text-muted-foreground text-sm mb-4 flex-grow">
                Comprehensive testing devices for electrical installations to ensure compliance with safety standards.
              </p>
              <Link
                to="/measure/safety-instruments/installation-testers"
                className="text-primary hover:text-primary/80 text-sm font-medium"
              >
                View Products
              </Link>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="bg-background border border-border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow"
          >
            <div className="p-4 flex flex-col h-full">
              <div className="mb-4">
                <img
                  src="/api/placeholder/400/320"
                  alt="Ratio Meters"
                  className="w-full h-48 object-contain"
                />
              </div>
              <h3 className="text-xl font-semibold mb-2">Ratio Meters</h3>
              <p className="text-muted-foreground text-sm mb-4 flex-grow">
                Specialized meters for measuring transformer turns ratio and current transformer testing.
              </p>
              <Link
                to="/measure/safety-instruments/ratiometer"
                className="text-primary hover:text-primary/80 text-sm font-medium"
              >
                View Products
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Product Selection Help Section */}
      <section className="mt-16 bg-muted/30 p-8 rounded-lg">
        <h2 className="text-2xl font-bold mb-6 text-center">Need Help Selecting the Right Product?</h2>
        <p className="text-center text-muted-foreground mb-8">
          Our team of experts can help you choose the right solution for your specific needs.
        </p>
        <div className="flex justify-center">
          <Link
            to="/contact"
            className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-primary text-primary-foreground hover:bg-primary/90 h-10 py-2 px-4"
          >
            Contact Our Experts
          </Link>
        </div>
      </section>
    </PageLayout>
  );
};

export default EarthResistivityTesters;