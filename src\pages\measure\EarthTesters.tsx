import React, { useState, useRef, useEffect } from "react";
import { useNavigate, useLocation } from 'react-router-dom';
import PageLayout from "@/components/layout/PageLayout";
import {
  ChevronRight,
  ChevronLeft,
  FileText,
  Mail,
  ArrowRight,
  ZoomIn,
  Eye,
  Zap,
  Cable,
  Gauge,
  Wifi,
  ArrowLeft,
  Check,
  Shield,
  BarChart,
  ExternalLink
} from "lucide-react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

// Modern Background Component
const ModernBackground = () => {
  return (
    <div className="fixed inset-0 pointer-events-none z-[-1] overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Abstract shapes */}
      <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-yellow-100 rounded-bl-full opacity-30"></div>
      <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-yellow-200 rounded-tr-full opacity-20"></div>

      {/* Subtle grid pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
    </div>
  );
};

// PDF URL for brochure
const PDF_URL = "/T&M April 2025.pdf";

// Enhanced Hero Section Component
const HeroSection = ({ onRequestDemo, onViewBrochure }) => {
  return (
    <div className="relative py-20 md:py-32 overflow-hidden font-['Open_Sans']">
      {/* Hero Background Elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 right-0 w-3/4 h-full bg-yellow-50 rounded-bl-[100px] transform -skew-x-12"></div>
        <div className="absolute bottom-20 left-0 w-64 h-64 bg-yellow-400 rounded-full opacity-10"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Text Content */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-6"
          >
            <div className="inline-block bg-yellow-400 py-1 px-3 rounded-full mb-2">
              <span className="text-sm font-semibold text-gray-900 font-['Open_Sans']">KRYKARD Precision Instruments</span>
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight font-['Open_Sans']">
              EARTH <span className="text-yellow-400">TESTERS</span>
            </h1>

            <p className="text-lg md:text-xl text-gray-800 leading-relaxed font-medium text-justify font-['Open_Sans']">
              Professional-grade instruments for accurate ground resistance measurements and comprehensive earth system analysis.
            </p>

            <div className="pt-4 flex flex-wrap gap-4">
              <Button
                className="px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-semibold rounded-lg shadow-md transition duration-300 flex items-center space-x-2 font-['Open_Sans']"
                onClick={onRequestDemo}
              >
                <span>Request Demo</span>
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button
                className="px-6 py-3 bg-white border-2 border-yellow-400 text-gray-900 font-semibold rounded-lg shadow-sm transition duration-300 hover:bg-gray-50 flex items-center space-x-2 font-['Open_Sans']"
                onClick={onViewBrochure}
              >
                <span>View Brochure</span>
                <FileText className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </motion.div>

          {/* Product Image */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative flex items-center justify-center h-full"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-200 to-yellow-50 rounded-full opacity-30 blur-2xl transform scale-110"></div>
            <motion.div
              animate={{
                y: [0, -15, 0],
              }}
              transition={{
                repeat: Infinity,
                duration: 3,
                ease: "easeInOut"
              }}
              className="relative z-10 flex justify-center"
            >
              <img
                src="/earth testers/C.A-6470N.png"
                alt="Krykard Earth Tester"
                className="max-h-[800px] w-auto object-contain drop-shadow-2xl transform md:scale-[1.5] scale-[1.2]"
              />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

// Animated Product Card
const ProductCard = ({
  product,
  onViewDetails,
  colors = {
    primary: 'yellow-400',
    secondary: 'yellow-50'
  }
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="group h-full font-['Open_Sans']"
    >
      <div className={`h-full rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 bg-white border border-gray-100 flex flex-col`}>
        {/* Product Image with Colored Background */}
        <div className={`relative p-4 md:p-6 flex justify-center items-center bg-${colors.secondary} h-48 md:h-56 overflow-hidden group-hover:bg-opacity-80 transition-all duration-700`}>
          <motion.img
            src={product.image}
            alt={product.name}
            className="h-40 md:h-48 object-contain z-10 drop-shadow-xl"
            whileHover={{ rotate: [-1, 1, -1], transition: { repeat: Infinity, duration: 2 } }}
          />
          <div className={`absolute top-4 left-4 bg-${colors.primary} text-white text-xs font-bold py-1 px-3 rounded-full font-['Open_Sans']`}>
            {product.id}
          </div>
        </div>

        {/* Product Content - Flex grow to push button to bottom */}
        <div className="p-4 md:p-6 flex flex-col flex-grow">
          <h3 className="text-lg md:text-xl font-bold text-gray-900 group-hover:text-yellow-400 transition-colors duration-300 font-['Open_Sans'] mb-4">
            {product.name}
          </h3>

          {/* Key Features - Fixed height container */}
          <div className="space-y-2 flex-grow">
            {product.features.slice(0, 3).map((feature, idx) => (
              <div key={idx} className="flex items-start">
                <Check className="h-4 w-4 text-yellow-400 mt-1 mr-2 flex-shrink-0" />
                <span className="text-gray-800 text-sm font-medium text-justify font-['Open_Sans']">{feature}</span>
              </div>
            ))}
          </div>

          {/* Specs Badge */}
          <div className="flex flex-wrap gap-2 pt-4 mb-4">
            <span className="inline-block bg-gray-100 rounded-full px-3 py-1 text-xs font-semibold text-gray-700 font-['Open_Sans']">
              {product.category}
            </span>
          </div>

          {/* View Details Button - Always at bottom */}
          <Button
            onClick={() => onViewDetails(product.id)}
            className={`w-full py-3 px-4 bg-${colors.primary} hover:bg-yellow-500 text-center font-semibold text-gray-900 rounded-lg transition-all duration-300 transform group-hover:-translate-y-1 flex items-center justify-center font-['Open_Sans'] mt-auto`}
          >
            <span>View Details</span>
            <ChevronRight className="ml-1 h-4 w-4" />
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

// Feature Highlight Component
const FeatureHighlight = ({ icon, title, description }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
      whileHover={{ y: -8, boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)" }}
      className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 p-4 md:p-6 h-full border-b-4 border-yellow-400 font-['Open_Sans']"
    >
      <div className="flex flex-col h-full">
        <div className="bg-gradient-to-br from-yellow-400 to-yellow-400 w-12 h-12 rounded-lg flex items-center justify-center mb-4 shadow-md">
          {icon}
        </div>
        <h3 className="text-lg md:text-xl font-bold text-gray-900 mb-3 font-['Open_Sans']">{title}</h3>
        <p className="text-gray-800 flex-grow text-sm md:text-base text-justify font-['Open_Sans']">{description}</p>
      </div>
    </motion.div>
  );
};

// Application Card Component
const ApplicationCard = ({ icon, title, description }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      viewport={{ once: true }}
      className="bg-white p-4 md:p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-2 border-b-4 border-yellow-400 font-['Open_Sans']"
    >
      <div className="bg-yellow-100 w-12 md:w-16 h-12 md:h-16 rounded-lg flex items-center justify-center mb-4 text-yellow-400">
        {icon}
      </div>
      <h3 className="text-lg md:text-xl font-semibold mb-3 text-gray-900 font-['Open_Sans']">{title}</h3>
      <p className="text-gray-800 font-medium text-sm md:text-base text-justify font-['Open_Sans']">{description}</p>
    </motion.div>
  );
};

// SpecItem component for product details
const SpecItem = ({ icon, text }) => (
  <div className="flex items-center space-x-3 py-3 border-b border-yellow-100 font-['Open_Sans']">
    <div className="bg-yellow-100 p-2 rounded-lg">
      {icon}
    </div>
    <span className="text-gray-800 font-medium text-justify">{text}</span>
  </div>
);

// Product Detail View with enhanced UI
const ProductDetailView = ({ product, onBackToList }) => {
  const [activeTab, setActiveTab] = useState("features");

  return (
    <div id="product-detail-view" className="bg-white rounded-2xl shadow-lg overflow-hidden font-['Open_Sans']">
      <div className="p-4 md:p-6">
        <button
          onClick={onBackToList}
          className="flex items-center text-yellow-400 hover:text-yellow-500 mb-6 font-['Open_Sans']"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Earth Testers
        </button>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 mb-8">
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-200 to-yellow-50 rounded-full opacity-30 blur-xl transform scale-90"></div>
            <motion.div
              animate={{ y: [0, -10, 0] }}
              transition={{ repeat: Infinity, duration: 3, ease: "easeInOut" }}
              className="relative z-10 bg-yellow-50 p-4 md:p-6 rounded-lg flex items-center justify-center"
            >
              <img
                src={product.image}
                alt={product.name}
                className="max-h-48 md:max-h-64 object-contain drop-shadow-xl transform transition-transform duration-500 hover:scale-110"
              />
            </motion.div>
            <div className="mt-4 text-center">
              <span className="bg-yellow-400 px-3 py-1 rounded-full text-sm font-medium text-white font-['Open_Sans']">
                {product.id}
              </span>
            </div>
          </div>

          <div>
            <div className="bg-yellow-100 px-2 py-1 rounded text-xs font-medium text-yellow-800 inline-block mb-2 font-['Open_Sans']">
              {product.category}
            </div>
            <h1 className="text-xl md:text-2xl font-bold mb-2 text-gray-900 font-['Open_Sans']">{product.name}</h1>
            <div className="w-16 h-1 bg-yellow-400 mb-4"></div>
            <p className="text-gray-800 mb-6 text-sm md:text-base text-justify font-['Open_Sans']">{product.description}</p>

            <div className="flex border-b border-gray-200 mb-6">
              <button
                className={`py-2 px-4 font-medium font-['Open_Sans'] text-sm md:text-base ${
                  activeTab === "features"
                    ? "border-b-2 border-yellow-400 text-yellow-400"
                    : "text-gray-500 hover:text-gray-700"
                }`}
                onClick={() => setActiveTab("features")}
              >
                Salient Features
              </button>
              <button
                className={`py-2 px-4 font-medium font-['Open_Sans'] text-sm md:text-base ${
                  activeTab === "measurements"
                    ? "border-b-2 border-yellow-400 text-yellow-400"
                    : "text-gray-500 hover:text-gray-700"
                }`}
                onClick={() => setActiveTab("measurements")}
              >
                Measurements
              </button>
            </div>
          </div>
        </div>

        <div className="mt-8">
          {activeTab === "features" && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h2 className="text-lg md:text-xl font-bold mb-4 text-gray-900 flex items-center font-['Open_Sans']">
                <span className="w-2 h-6 bg-yellow-400 rounded-full mr-2"></span>
                Salient Features
              </h2>
              <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
                {product.features.map((feature: string, index: number) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className="bg-yellow-50 p-4 rounded-lg flex items-start hover:bg-yellow-100 hover:shadow-md transition-all duration-300"
                  >
                    <div className="bg-yellow-400 p-1 rounded-full mr-3 mt-0.5">
                      <Check className="h-4 w-4 text-white" />
                    </div>
                    <span className="text-gray-800 text-sm md:text-base text-justify font-['Open_Sans']">{feature}</span>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {activeTab === "measurements" && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h2 className="text-lg md:text-xl font-bold mb-4 text-gray-900 flex items-center font-['Open_Sans']">
                <span className="w-2 h-6 bg-yellow-400 rounded-full mr-2"></span>
                Measurements
              </h2>
              <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
                {product.measurements.map((measurement: string, index: number) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className="bg-yellow-50 p-4 rounded-lg flex items-start hover:bg-yellow-100 hover:shadow-md transition-all duration-300"
                  >
                    <div className="bg-yellow-400 p-1 rounded-full mr-3 mt-0.5">
                      <Gauge className="h-4 w-4 text-white" />
                    </div>
                    <span className="text-gray-800 text-sm md:text-base text-justify font-['Open_Sans']">{measurement}</span>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
};

// Contact Section Component
const ContactSection = ({ onContactClick }) => {
  return (
    <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 py-6 md:py-8 px-4 rounded-2xl shadow-lg font-['Open_Sans']">
      <div className="max-w-4xl mx-auto text-center">
        <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-gray-900 mb-4 font-['Open_Sans']">Need Help Selecting the Right Earth Tester?</h2>
        <p className="text-gray-800 mb-6 md:mb-8 max-w-3xl mx-auto font-medium text-sm md:text-base text-center font-['Open_Sans']">
          Our team of experts can help you choose the right earth testing solution for your specific application needs. Contact us for personalized recommendations.
        </p>
        <Button
          className="inline-flex items-center px-6 md:px-8 py-3 md:py-4 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-semibold rounded-lg shadow-md transition duration-300 transform hover:-translate-y-1 text-sm md:text-base font-['Open_Sans']"
          onClick={onContactClick}
        >
          Contact Our Experts
          <ArrowRight className="ml-2 h-4 md:h-5 w-4 md:w-5" />
        </Button>
      </div>
    </div>
  );
};

// Earth Testers data
const earthTesters = [
  {
    id: "ca-6424",
    name: "2P/3P Earth Tester CA 6424",
    description: "Professional earth resistance tester with backlit LCD for 2-pole and 3-pole measurements.",
    features: [
      "Backlit custom 206-segment LCD",
      "Auto Power off",
      "Noise indication",
      "Measurement Mode: V, I, R 2P (Ω), R 3P (Ω)"
    ],
    measurements: [
      "Voltage: Upto 600 V",
      "2P earth resistance/accuracy: 0.05 Ω to 50 kΩ/± (2%R +1count)",
      "3P earth resistance/accuracy: 0.5 Ω-50.00 kΩ/± (2%R +1count)",
      "RH stake resistance: 0.05 Ω to 49.99 kΩ",
      "U₀ voltage measurement: Upto 600 VAC",
      "Leakage current: Upto 60.00 A"
    ],
    image: "/earth testers/6424-removebg-preview.png",
    category: "2P/3P Earth Tester",
    colors: {
      primary: 'yellow-400',
      secondary: 'yellow-100'
    }
  },
  {
    id: "ca-6460-6462",
    name: "4P Earth Tester CA 6460/CA 6462",
    description: "Advanced 4-pole earth resistance and resistivity tester with digital display for precise measurements.",
    features: [
      "Large backlit digital display with 2,000 counts",
      "3 fault presence indicators to validate measurement",
      "Battery",
      "CA 6460 - Non Rechargeable/ Rechargeable Batteries",
      "CA 6462-Rechargeable Batteries only"
    ],
    measurements: [
      "3-in-1 tester",
      "Resistivity: (\"Wenner method\" (4-rod method))",
      "Ground Resistance: (\"TAGG method\" (62% method))",
      "Resistance Range: 0.01 to 2,000 Ω (3 automatic ranges)",
      "Test current: 10mA, 1mA, 0.1mA",
      "Accuracy: ±2% ±1point",
      "Frequency: 128Hz"
    ],
    image: "/earth testers/C.A-6460.png",
    category: "4P Earth Tester",
    colors: {
      primary: 'yellow-400',
      secondary: 'yellow-100'
    }
  },
  {
    id: "ca-6470-6471",
    name: "3P/4P Earth Tester CA 6470N/CA 6471",
    description: "Multi-function earth and resistivity tester with advanced features for professional use.",
    features: [
      "Backlit LCD display featuring (3 simultaneous display levels)",
      "Noise interference detection",
      "Alarm function",
      "Memory: 512 memory locations",
      "Communication: USB"
    ],
    measurements: [
      "4-in-1 tester (CA 6470N): Earth, Resistivity, Coupling, Continuity",
      "5-in-1 tester (CA 6471): Earth, Selective earth, Resistivity, Coupling, Continuity",
      "3-pole, 4-pole measurements Range: 0.01 Ω to 99.9 kΩ",
      "Frequency: 41 to 512 Hz",
      "Selective 4-pole measurements, 2 clamps (CA 6471)",
      "Range: 0.01 Ω to 500 Ω",
      "Frequency: 128 Hz, 1367 Hz, 1611 Hz, 1758 Hz",
      "Resistivity Range: 0.01 Ω to 99.9 kΩ",
      "Frequency: 128 Hz"
    ],
    image: "/earth testers/C.A-6470N.png",
    category: "Advanced 3P/4P Earth Tester",
    colors: {
      primary: 'yellow-400',
      secondary: 'yellow-100'
    }
  },
  {
    id: "ca-6472-6474",
    name: "3P/4P Earth Tester CA 6472/CA 6474",
    description: "Professional earth tester with advanced features for pylons and comprehensive earth system analysis.",
    features: [
      "Backlit LCD display featuring (3 simultaneous display levels)",
      "Automatic & Expert mode",
      "Earth measurement on Pylons with earth cable (with CA 6474 option)",
      "Alarm function",
      "Memory: 512-record memory",
      "Communication: USB",
      "Measurement with CA 6474 Range: 0.001 Ω to 99.9 kΩ",
      "Frequency: 41 to 5078 Hz"
    ],
    measurements: [
      "3P, 4P/4P Selective Method: Range: 0.01 Ω to 99.9 kΩ",
      "Frequency: 41 to 5,078 Hz",
      "Earth Measurement (2 clamps) Range: 0.01 Ω to 500 Ω",
      "Frequency: 1,367 Hz (auto) & 1,367 Hz, 1,611 Hz, 1,758 Hz (manual)",
      "Resistivity: Range: 0.01 Ω to 99.9 kΩ",
      "Frequency: 41 to 128 Hz",
      "Earth Potential Range: 0.01 mV to 65.00 V",
      "Frequency: 41 to 128 Hz",
      "DC Resistance Range: 0.001 Ω to 99.9 kΩ"
    ],
    image: "/earth testers/C.A-6472.png",
    category: "Advanced 3P/4P Earth Tester",
    colors: {
      primary: 'yellow-400',
      secondary: 'yellow-100'
    }
  }
  ];

// Custom icon for Lightning
const Lightning = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
    <path d="M13 3v7h6l-8 11v-7H5l8-11z" />
  </svg>
);

// Custom icon for Factory
const Factory = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
    <path d="M2 20a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8l-7 5V8l-7 5V4a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z" />
    <path d="M17 18h1" />
    <path d="M12 18h1" />
    <path d="M7 18h1" />
  </svg>
);

// Main Earth Testers component
const EarthTesters = () => {
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [activeTab, setActiveTab] = useState("overview");
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const productCardsRef = useRef(null);
  const navigate = useNavigate();
  const location = useLocation();  // Effect to check URL parameters for initial tab and product
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const tab = params.get('tab');
    const productId = params.get('product');

    if (tab) setActiveTab(tab);
    if (productId) {
      const product = findProductById(productId);
      if (product) setSelectedProduct(product);

      // If we're viewing product details, ensure the UI focuses on the product
      if (tab === 'details' && product) {
        // Small delay to ensure the component is rendered
        setTimeout(() => {
          // Find the product detail section by ID and scroll directly to it
          const productDetailSection = document.getElementById('product-detail-view');
          if (productDetailSection) {
            productDetailSection.scrollIntoView({ behavior: 'auto', block: 'start' });

            // Also focus on the corresponding product button for better UX
            const productButton = document.getElementById(`product-button-${productId}`);
            if (productButton) {
              productButton.scrollIntoView({ behavior: 'auto', block: 'nearest' });
            }
              // Add a visual highlight effect to the product image
            const productImage = document.querySelector('#product-detail-view img.object-contain');
            if (productImage) {
              // Add a pulse animation
              productImage.classList.add('animate-pulse');
              setTimeout(() => {
                productImage.classList.remove('animate-pulse');
              }, 1500);
            }
          }
        }, 100);
      }
    }
  }, [location]);

  // Find product by ID
  const findProductById = (id: string) => {
    return earthTesters.find(product => product.id === id);
  };

  // Handle view details click
  const handleViewDetails = (id: string) => {
    const product = findProductById(id);
    if (product) {
      setSelectedProduct(product);
      setActiveTab("details");
      navigate(`?tab=details&product=${id}`, { replace: true });

      // Add immediate focus on product detail through direct targeting
      setTimeout(() => {
        const productDetailSection = document.getElementById('product-detail-view');
        if (productDetailSection) {
          productDetailSection.scrollIntoView({ behavior: 'auto', block: 'start' });
            // Add visual feedback with pulse animation on the product image
          const productImage = document.querySelector('#product-detail-view img.object-contain');
          if (productImage) {
            productImage.classList.add('animate-pulse');
            setTimeout(() => {
              productImage.classList.remove('animate-pulse');
            }, 1500);
          }
        }
      }, 50);
    }
  };

  // Handle back to list
  const handleBackToList = () => {
    setSelectedProduct(null);
    setActiveTab("overview");
    navigate(`?tab=overview`, { replace: true });
  };

  // Handler for Request Demo button
  const handleRequestDemo = () => {
    navigate("/contact/sales");
  };

  // Handler for View Brochure button
  const handleViewBrochure = () => {
    window.open(PDF_URL, '_blank');
  };



  // Navigation tabs data
  const navTabs = [
    { id: "overview", label: "Overview", icon: <Gauge className="h-5 w-5" /> },
    { id: "details", label: "Product Details", icon: <Zap className="h-5 w-5" /> },
    { id: "applications", label: "Applications", icon: <Shield className="h-5 w-5" /> }
  ];

  return (
    <PageLayout
      title="Earth Testers"
      subtitle="Our comprehensive range of earth testing devices for precise resistance monitoring and ground system analysis"
      category="measure"
    >
      {/* Modern Background */}
      <ModernBackground />

      {/* Premium Modern Navigation Tabs - Responsive Design */}
      <div className="sticky top-0 z-30 bg-white shadow-lg border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4">
          {/* Desktop Navigation */}
          <div className="hidden md:flex justify-center py-2">
            <div className="bg-gray-50 p-1.5 rounded-full flex shadow-sm">
              {navTabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => {
                    setActiveTab(tab.id);
                    if (tab.id === "details" && selectedProduct) {
                      navigate(`?tab=${tab.id}&product=${selectedProduct.id}`, { replace: true });
                    } else {
                      navigate(`?tab=${tab.id}`, { replace: true });
                      // If going back to overview, clear selected product
                      if (tab.id === "overview") {
                        setSelectedProduct(null);
                      }
                    }
                    setIsMobileMenuOpen(false);
                  }}
                  className={cn(
                    "relative px-6 py-2.5 font-medium rounded-full transition-all duration-300 flex items-center mx-1 overflow-hidden",
                    activeTab === tab.id
                      ? "bg-gradient-to-r from-yellow-500 to-yellow-400 text-white shadow-md transform -translate-y-0.5"
                      : "text-gray-700 hover:bg-yellow-50 hover:text-yellow-500"
                  )}
                >
                  <div className="flex items-center relative z-10">
                    <span className="mr-2">{tab.icon}</span>
                    <span>{tab.label}</span>
                  </div>
                  {activeTab === tab.id && (
                    <motion.div
                      layoutId="navIndicator"
                      className="absolute inset-0 bg-gradient-to-r from-yellow-500 to-yellow-400 -z-0"
                      initial={false}
                      transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                    />
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Mobile Navigation */}
          <div className="md:hidden py-2 flex justify-between items-center">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="text-gray-700 hover:text-yellow-500 focus:outline-none"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            <span className="font-semibold text-gray-900 text-lg">
              {navTabs.find(tab => tab.id === activeTab)?.label}
            </span>

            <div className="w-6"></div> {/* Spacer for balanced layout */}
          </div>

          {/* Mobile Menu Dropdown */}
          <div className={`md:hidden overflow-hidden transition-all duration-300 ease-in-out ${isMobileMenuOpen ? 'max-h-60' : 'max-h-0'}`}>
            <div className="bg-white rounded-lg shadow-lg p-2 mb-2">
              {navTabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => {
                    setActiveTab(tab.id);
                    if (tab.id === "details" && selectedProduct) {
                      navigate(`?tab=${tab.id}&product=${selectedProduct.id}`, { replace: true });
                    } else {
                      navigate(`?tab=${tab.id}`, { replace: true });
                      // If going back to overview, clear selected product
                      if (tab.id === "overview") {
                        setSelectedProduct(null);
                      }
                    }
                    setIsMobileMenuOpen(false);
                  }}
                  className={`w-full text-left px-4 py-3 rounded-lg my-1 flex items-center ${
                    activeTab === tab.id
                      ? "bg-yellow-50 text-yellow-600 font-medium"
                      : "text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  <span className="mr-3">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {activeTab === "overview" && !selectedProduct && (
        <>
          {/* Hero Section */}
          <HeroSection
            onRequestDemo={handleRequestDemo}
            onViewBrochure={handleViewBrochure}
          />

          {/* Key Features Section */}
          <div className="py-12 md:py-16 bg-gradient-to-br from-yellow-50 to-white font-['Open_Sans']">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-8 md:mb-12">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                  viewport={{ once: true }}
                >
                  <h2 className="text-2xl md:text-3xl font-bold text-yellow-400 mb-4 font-['Open_Sans']">Why Choose Our Earth Testers?</h2>
                  <p className="mt-4 text-base md:text-lg text-gray-800 max-w-3xl mx-auto font-medium text-center font-['Open_Sans']">
                    Precision-engineered instruments that combine accuracy, durability, and advanced features for professional earth resistance testing
                  </p>
                </motion.div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
                <FeatureHighlight
                  icon={<ZoomIn className="h-6 w-6 text-white" />}
                  title="High Precision"
                  description="Advanced technology for precise resistance measurements with industry-leading accuracy up to ±2% of reading."
                />

                <FeatureHighlight
                  icon={<Gauge className="h-6 w-6 text-white" />}
                  title="Multiple Methods"
                  description="Support for 2-pole, 3-pole, 4-pole, and selective testing techniques for comprehensive earth system analysis."
                />

                <FeatureHighlight
                  icon={<Cable className="h-6 w-6 text-white" />}
                  title="Data Management"
                  description="Built-in memory and connectivity options for storing measurements and transferring data to computers for documentation."
                />
              </div>
            </div>
          </div>

          {/* Products Section */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-16 font-['Open_Sans']" ref={productCardsRef}>
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-12 md:mb-16"
            >
              <span className="inline-block bg-yellow-100 text-yellow-800 px-4 py-1 rounded-full text-sm font-semibold mb-4 font-['Open_Sans']">
                PROFESSIONAL SERIES
              </span>
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-6 text-gray-900 font-['Open_Sans']">
                Our Earth Tester Range
              </h2>
              <p className="max-w-3xl mx-auto text-gray-800 text-base md:text-lg font-medium text-center font-['Open_Sans']">
                Choose the perfect earth tester for your ground resistance measurement needs
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 gap-6 md:gap-8">
              {earthTesters.map(product => (
                <ProductCard
                  key={product.id}
                  product={product}
                  onViewDetails={handleViewDetails}
                  colors={product.colors}
                />
              ))}
            </div>
          </div>

          {/* Applications Section */}
          <div className="py-8 md:py-12 bg-gradient-to-br from-yellow-50 to-white rounded-2xl my-8 md:my-12 shadow-lg font-['Open_Sans']">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-8 md:mb-12">
                <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4 font-['Open_Sans']">Application Areas</h2>
                <p className="text-base md:text-lg text-gray-800 max-w-3xl mx-auto font-medium text-center font-['Open_Sans']">
                  Our earth testers are designed for a wide range of ground resistance measurement applications
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
                <ApplicationCard
                  icon={<Zap className="h-6 md:h-8 w-6 md:w-8" />}
                  title="Power Distribution"
                  description="For utility companies and power distribution networks. Ensures proper grounding for safe and efficient power transmission."
                />
                <ApplicationCard
                  icon={<Wifi className="h-6 md:h-8 w-6 md:w-8" />}
                  title="Telecommunications"
                  description="For antenna and tower grounding systems. Critical for signal integrity and protection of sensitive equipment."
                />
                <ApplicationCard
                  icon={<Lightning className="h-6 md:h-8 w-6 md:w-8" />}
                  title="Lightning Protection"
                  description="For lightning arrestor systems verification. Ensures effective paths to ground for lightning strikes to prevent damage."
                />
                <ApplicationCard
                  icon={<Factory className="h-6 md:h-8 w-6 md:w-8" />}
                  title="Industrial"
                  description="For equipment grounding in industrial settings. Ensures workplace safety and protects sensitive machinery from electrical faults."
                />
              </div>
            </div>
          </div>

          {/* Contact Section */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">
            <ContactSection onContactClick={handleRequestDemo} />
          </div>
        </>
      )}

      {activeTab === "details" && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12 font-['Open_Sans']">
          {/* Product Selector/Tabs */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-gradient-to-r from-yellow-50 to-white rounded-xl shadow-md p-4 md:p-6 mb-6 md:mb-8 relative overflow-hidden"
            id="product-selector"
          >
            <div className="absolute top-0 right-0 w-64 h-64 bg-yellow-200 rounded-full opacity-20 transform translate-x-1/2 -translate-y-1/2 blur-3xl"></div>

            <h2 className="text-lg md:text-xl font-bold text-gray-900 mb-4 relative z-10 font-['Open_Sans']">
              Select <span className="text-yellow-400">Earth Tester</span> Model
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 relative z-10">
              {earthTesters.map((tester) => (
                <motion.button
                  key={tester.id}
                  id={`product-button-${tester.id}`}
                  whileHover={{ y: -3, boxShadow: "0 8px 20px -5px rgba(0, 0, 0, 0.1)" }}
                  whileTap={{ y: 0 }}
                  onClick={() => {
                    setSelectedProduct(tester);
                    navigate(`?tab=details&product=${tester.id}`, { replace: true });
                  }}
                  className={`relative rounded-lg transition-all duration-300 overflow-hidden ${
                    selectedProduct?.id === tester.id
                      ? "ring-1 ring-yellow-400"
                      : "hover:ring-1 hover:ring-yellow-300"
                  }`}
                >
                  <div className={`h-full py-3 md:py-4 px-3 flex flex-col items-center text-center ${
                    selectedProduct?.id === tester.id
                      ? "bg-yellow-400 text-black"
                      : "bg-white hover:bg-yellow-50"
                  }`}>
                    <div className="mb-2">
                      <Gauge className={`h-6 md:h-8 w-6 md:w-8 ${selectedProduct?.id === tester.id ? "text-black" : "text-yellow-400"}`} />
                    </div>

                    <h3 className={`text-base md:text-lg font-bold mb-1 font-['Open_Sans'] ${selectedProduct?.id === tester.id ? "text-black" : "text-gray-900"}`}>
                      {tester.id}
                    </h3>

                    <div className={`text-xs font-['Open_Sans'] ${selectedProduct?.id === tester.id ? "text-black" : "text-gray-500"}`}>
                      {tester.category}
                    </div>

                    {selectedProduct?.id === tester.id && (
                      <div className="mt-2 bg-white bg-opacity-50 rounded-full px-3 py-0.5 text-xs font-semibold text-black font-['Open_Sans']">
                        Selected
                      </div>
                    )}
                  </div>
                </motion.button>
              ))}
            </div>
          </motion.div>

          {/* Product Detail View */}
          {selectedProduct && (
            <ProductDetailView
              product={selectedProduct}
              onBackToList={handleBackToList}
            />
          )}

          {/* Comparison Table */}
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden mt-6 md:mt-8 font-['Open_Sans']">
            <div className="bg-gradient-to-r from-yellow-400 to-yellow-400 p-3 md:p-4">
              <h3 className="text-lg md:text-2xl font-bold text-center text-white font-['Open_Sans']">Model Comparison</h3>
            </div>
            <div className="p-2 sm:p-4 md:p-6 overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr>
                    <th className="px-2 sm:px-4 md:px-6 py-2 md:py-3 bg-yellow-50 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider rounded-tl-lg font-['Open_Sans']">Feature</th>
                    {earthTesters.map((tester, idx) => (
                      <th key={idx} className={`px-2 sm:px-4 md:px-6 py-2 md:py-3 bg-yellow-50 text-center text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider font-['Open_Sans'] ${idx === earthTesters.length - 1 ? 'rounded-tr-lg' : ''}`}>
                        {tester.id}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {[
                    { name: 'Poles', values: ['2P/3P', '4P', '3P/4P', '3P/4P'] },
                    { name: 'Display', values: ['LCD', 'Digital', 'LCD', 'LCD'] },
                    { name: 'Memory', values: ['No', 'No', '512 loc.', '512 rec.'] },
                    { name: 'Communication', values: ['No', 'No', 'USB', 'USB'] },
                    { name: 'Max Resistance', values: ['50 kΩ', '2,000 Ω', '99.9 kΩ', '99.9 kΩ'] },
                    { name: 'Voltage Measurement', values: ['600 V', 'No', 'Yes', 'Yes'] }
                  ].map((feature, idx) => (
                    <motion.tr
                      key={idx}
                      initial={{ opacity: 0, y: 5 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: idx * 0.05 }}
                      viewport={{ once: true }}
                      className={idx % 2 === 0 ? 'bg-white hover:bg-yellow-50 transition-colors duration-200' : 'bg-gray-50 hover:bg-yellow-50 transition-colors duration-200'}
                    >
                      <td className="px-2 sm:px-4 md:px-6 py-3 md:py-4 whitespace-nowrap text-xs sm:text-sm font-medium text-gray-900 font-['Open_Sans']">{feature.name}</td>
                      {feature.values.map((value, i) => (
                        <td key={i} className="px-2 sm:px-4 md:px-6 py-3 md:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-800 text-center font-['Open_Sans']">
                          {value}
                        </td>
                      ))}
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Enhanced Contact Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
            className="mt-20"
          >
            <ContactSection onContactClick={handleRequestDemo} />
          </motion.div>
        </div>
      )}

      {activeTab === "applications" && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12 font-['Open_Sans']">
          <div className="text-center mb-8 md:mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4 font-['Open_Sans']">Applications</h1>
              <p className="text-base md:text-lg text-gray-800 max-w-3xl mx-auto text-center font-['Open_Sans']">
                Earth testers are versatile instruments designed for a wide range of professional applications in ground resistance testing
              </p>
            </motion.div>
          </div>

          {/* Application Categories */}
          <div className="py-8 md:py-12 bg-gradient-to-br from-yellow-50 to-white rounded-2xl shadow-lg mb-12 md:mb-16">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
                <ApplicationCard
                  icon={<Zap className="h-6 md:h-8 w-6 md:w-8" />}
                  title="Power Distribution"
                  description="For utility companies and power distribution networks. Ensures proper grounding for safe and efficient power transmission."
                />
                <ApplicationCard
                  icon={<Wifi className="h-6 md:h-8 w-6 md:w-8" />}
                  title="Telecommunications"
                  description="For antenna and tower grounding systems. Critical for signal integrity and protection of sensitive equipment."
                />
                <ApplicationCard
                  icon={<Lightning className="h-6 md:h-8 w-6 md:w-8" />}
                  title="Lightning Protection"
                  description="For lightning arrestor systems verification. Ensures effective paths to ground for lightning strikes to prevent damage."
                />
                <ApplicationCard
                  icon={<Factory className="h-6 md:h-8 w-6 md:w-8" />}
                  title="Industrial"
                  description="For equipment grounding in industrial settings. Ensures workplace safety and protects sensitive machinery from electrical faults."
                />
              </div>
            </div>
          </div>

          {/* Industry Applications */}
          <div className="bg-white rounded-2xl shadow-lg p-4 md:p-8 mb-12 md:mb-16">
            <h2 className="text-xl md:text-2xl font-bold text-gray-900 mb-6 font-['Open_Sans']">Industry-Specific Solutions</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 mb-6 md:mb-8">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-400">
                  <Shield className="h-5 md:h-6 w-5 md:w-6" />
                </div>
                <div>
                  <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-2 font-['Open_Sans']">Safety Compliance</h3>
                  <p className="text-gray-800 font-medium text-sm md:text-base text-justify font-['Open_Sans']">
                    Verify grounding systems meet electrical safety standards and regulations to ensure personnel safety and equipment protection.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-400">
                  <BarChart className="h-5 md:h-6 w-5 md:w-6" />
                </div>
                <div>
                  <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-2 font-['Open_Sans']">Data Logging</h3>
                  <p className="text-gray-800 font-medium text-sm md:text-base text-justify font-['Open_Sans']">
                    Record and analyze ground resistance trends over time to identify deterioration and schedule preventative maintenance.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-400">
                  <Cable className="h-5 md:h-6 w-5 md:w-6" />
                </div>
                <div>
                  <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-2 font-['Open_Sans']">Installation Verification</h3>
                  <p className="text-gray-800 font-medium text-sm md:text-base text-justify font-['Open_Sans']">
                    Confirm proper installation of new grounding systems in construction projects and critical infrastructure.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-400">
                  <ZoomIn className="h-5 md:h-6 w-5 md:w-6" />
                </div>
                <div>
                  <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-2 font-['Open_Sans']">Soil Resistivity</h3>
                  <p className="text-gray-800 font-medium text-sm md:text-base text-justify font-['Open_Sans']">
                    Analyze soil conditions before installing ground systems to determine optimal grounding electrode configurations.
                  </p>
                </div>
              </motion.div>
            </div>

            {/* View Brochure button */}
            <div className="flex justify-center mt-6 md:mt-8">
              <Button
                className="w-full sm:w-auto px-6 py-3 bg-gradient-to-r from-yellow-400 to-yellow-400 hover:from-yellow-500 hover:to-yellow-500 text-gray-900 font-semibold rounded-lg shadow-md transition duration-300 flex items-center justify-center space-x-2 text-sm md:text-base font-['Open_Sans']"
                onClick={handleViewBrochure}
              >
                <span>View Brochure</span>
                <FileText className="ml-2 h-4 md:h-5 w-4 md:w-5" />
              </Button>
            </div>
          </div>

          {/* Contact Section */}
          <div className="mt-12 md:mt-16">
            <ContactSection onContactClick={handleRequestDemo} />
          </div>
        </div>
      )}

      {/* No PDF Viewer Modal - Using direct link instead */}
    </PageLayout>
  );
};

export default EarthTesters;