import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { Link } from "react-router-dom";
import Layout from "@/components/layout/Layout";
import SubpageHeader from "@/components/SubPageHeader";
import ProductCategoryHeader from "@/components/ProductCategoryHeader";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
// import PdfViewer from "@/components/ui/pdf-viewer";

const DranetzHDPQGuideProductPage = () => {
  // Define tabs for the ProductCategoryHeader
  const productTabs = [
    { name: "Dranetz Xplorer", path: "/measure/class-a-analyzers/dranetz-xplorer" },
    { name: "Dranetz Guide", path: "/measure/class-a-analyzers/dranetz-guide" },
    { name: "Dranetz Visa", path: "/measure/class-a-analyzers/dranetz-visa" }
  ];

  // Technical specifications
  const specifications = {
    measurements: [
      "Power Quality parameters according to IEC 61000-4-30 Class A",
      "Voltage, Current, Power, Energy, Harmonics, Flicker",
      "Transients down to 40µs",
      "10K cycle pre/post trigger buffer",
      "Inrush with 10K cycle recording"
    ],
    connectivity: [
      "USB for direct PC connection and data transfer",
      "LAN for network integration",
      "Wireless (WiFi) for remote monitoring",
      "GPS time synchronization for accurate timestamping"
    ],
    physical: [
      "Dimensions: 12\" x 8\" x 3\" (305mm x 203mm x 76mm)",
      "Weight: 4.5 lbs (2 kg)",
      "IP54 rated for dust and water protection",
      "Operating temperature: -10°C to 50°C",
      "Battery life: Up to 4 hours of continuous operation"
    ]
  };

  return (
    <Layout>
      {/* Main Title Header with added top padding */}
      <AnimatedSection>
        <div className="pt-12">
          <SubpageHeader
            title="Dranetz HDPQ Guide"
            subtitle="Premium Class A analyzer with advanced features for complete power quality analysis"
          />
        </div>
      </AnimatedSection>

      {/* Product Category SubHeader */}
      <AnimatedSection>
        <ProductCategoryHeader
          tabs={productTabs}
          showCompareButton={true}
        />
      </AnimatedSection>

      {/* Main content */}
      <div className="container max-w-7xl mx-auto px-6 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16 items-center">
          {/* Product Image */}
          <AnimatedSection>
            <img
              src="https://firebasestorage.googleapis.com/v0/b/atandra.firebasestorage.app/o/Atandraimages%2FHDPQ_Guide.png?alt=media&token=47f1be5f-f3ce-4ecd-9b8c-ccb8a2c329d1?raw=true"
              alt="Dranetz HDPQ Guide"
              className="w-full h-auto shadow-lg rounded-lg"
            />
          </AnimatedSection>

          {/* Product Description */}
          <AnimatedSection>
            <h2 className="text-3xl font-bold mb-6">Dranetz HDPQ Guide</h2>
            <p className="text-gray-600 mb-4">
              The Dranetz HDPQ Guide is a premium Class A analyzer designed for comprehensive power quality analysis with advanced features for detailed investigation of electrical issues.
            </p>
            <p className="text-gray-600 mb-6">
              With the ability to capture transients as short as 40µs and a 10K cycle pre/post trigger buffer, the HDPQ Guide provides unparalleled insight into power quality events.
            </p>

            <h3 className="text-xl font-bold mb-4">Key Features</h3>
            <ul className="list-disc pl-5 mb-6 text-gray-600">
              <li>IEC 61000-4-30 Class A compliance</li>
              <li>Isolated 3 Phase 4 CT Analyser</li>
              <li>Inrush With 10K Cycle Recording</li>
              <li>10K cycle pre/post trigger buffer</li>
              <li>Transients 40µs</li>
              <li>USB, LAN, Wireless (Wifi) Connectivity</li>
              <li>GPS time synchronization</li>
            </ul>

            <div className="flex flex-wrap gap-4">
              <Button asChild>
                <Link to="/contact">Request a Quote</Link>
              </Button>
              <Button variant="outline" asChild>
                <a href="/downloads/dranetz-hdpq-guide-datasheet.pdf" target="_blank" rel="noopener noreferrer">
                  Download Datasheet
                </a>
              </Button>
            </div>
          </AnimatedSection>
        </div>

        {/* Technical Specifications */}
        <AnimatedSection>
          <h2 className="text-2xl font-bold mb-8 text-center">Technical Specifications</h2>
          <Tabs defaultValue="measurements" className="w-full">
            <TabsList className="w-full justify-center mb-8">
              <TabsTrigger value="measurements">Measurements</TabsTrigger>
              <TabsTrigger value="connectivity">Connectivity</TabsTrigger>
              <TabsTrigger value="physical">Physical</TabsTrigger>
            </TabsList>

            <TabsContent value="measurements">
              <Card className="p-6 shadow-lg">
                <CardContent className="pt-6">
                  <ul className="space-y-2">
                    {specifications.measurements.map((spec, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-blue-500 mr-2">✓</span>
                        {spec}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="connectivity">
              <Card className="p-6 shadow-lg">
                <CardContent className="pt-6">
                  <ul className="space-y-2">
                    {specifications.connectivity.map((spec, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-blue-500 mr-2">✓</span>
                        {spec}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="physical">
              <Card className="p-6 shadow-lg">
                <CardContent className="pt-6">
                  <ul className="space-y-2">
                    {specifications.physical.map((spec, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-blue-500 mr-2">✓</span>
                        {spec}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </AnimatedSection>

        {/* Applications */}
        <AnimatedSection>
          <h2 className="text-2xl font-bold mb-8 text-center">Applications</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {[
              {
                title: "Industrial Facilities",
                description: "Monitor power quality in manufacturing facilities to prevent equipment damage and production downtime."
              },
              {
                title: "Utility Monitoring",
                description: "Verify compliance with grid codes and standards for power quality at utility interconnections."
              },
              {
                title: "Commercial Buildings",
                description: "Identify power quality issues in commercial buildings that may affect sensitive equipment and data centers."
              },
              {
                title: "Renewable Energy Sites",
                description: "Monitor power quality at renewable energy generation sites to ensure grid compatibility."
              }
            ].map((application, index) => (
              <Card key={index} className="p-6 shadow-lg">
                <h3 className="text-xl font-bold mb-2">{application.title}</h3>
                <p className="text-gray-600">{application.description}</p>
              </Card>
            ))}
          </div>
        </AnimatedSection>

        {/* Call to Action */}
        <AnimatedSection>
          <div className="bg-gray-100 p-8 rounded-lg text-center shadow-lg">
            <h2 className="text-2xl font-bold mb-4">Need More Information?</h2>
            <p className="text-gray-600 mb-6">
              Contact our power quality experts for a personalized demonstration or quote for the Dranetz HDPQ Guide.
            </p>
            <Button size="lg" asChild>
              <Link to="/contact">Contact Our Experts</Link>
            </Button>
          </div>
        </AnimatedSection>
      </div>
    </Layout>
  );
};

const AnimatedSection = ({ children }) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 50 }}
      animate={inView ? { opacity: 1, y: 0 } : {}}
      transition={{ duration: 0.6 }}
    >
      {children}
    </motion.div>
  );
};

export default DranetzHDPQGuideProductPage;