import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { Link } from "react-router-dom";
import Layout from "@/components/layout/Layout";
import SubpageHeader from "@/components/SubPageHeader";
import ProductCategoryHeader from "@/components/ProductCategoryHeader";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
// import PdfViewer from "@/components/ui/pdf-viewer";

const DranetzXplorerProductPage = () => {
  // Define tabs for the ProductCategoryHeader
  const productTabs = [
    { name: "Dranetz Xplorer", path: "/measure/class-a-analyzers/dranetz-xplorer" },
    { name: "Dranetz Guide", path: "/measure/class-a-analyzers/dranetz-guide" },
    { name: "Dranetz Visa", path: "/measure/class-a-analyzers/dranetz-visa" }
  ];

  // Technical specifications
  const specifications = {
    differentialVoltage: [
      "0-1000Vrms AC+DC: +/- 0.1% reading +/- 0.05% FS",
      "0-1000A-10,000A DC: +/- 0.1% reading, range order dependent 0.15% - 0.25% full scale",
      "Transients: +/- 10 time/±V ~ +/- 0.5% 3u",
      "Common mode reject: 0-100Vrms +/- 1% of reading, +/- 0.025% FS"
    ],
    current: [
      "Range (probe dependent): +/- 0.1% +/- 0.1% reading +/- 0.025% FS",
      "Transients: Range probe dependent",
      "Measures: High Speed, range probe dependent, 10% of reading +/- 0.5% FS"
    ],
    frequency: [
      "Range: 45-65Hz +/- 0.03Hz (10 sec window)"
    ],
    physical: [
      "Size: 10\"W x 8\"H x 2.75\"D (25.4cm x 20.3cm x 6.98cm)",
      "Weight: 4.2lbs, 1.9kg",
      "Operating temperature: 0°C to 50°C (32° to 122°F)",
      "Storage temperature: -20°C to 55°C (-4° to 131°F)",
      "Humidity: 10-90% non-condensing"
    ],
    powerEnergy: [
      "Real Power (W): +/- 0.2% reading +/- 0.04%/range order dependent",
      "Apparent Power (VA): +/- 0.2% reading +/- 0.04%, range order dependent",
      "Reactive Power (VAR): +/- 0.2% reading +/- 0.04%, range order dependent",
      "Power Factor: +/- 0.5% typical, +/- 0.02% FS",
      "Displacement PF: +/- 0.5% typ 0.1"
    ]
  };

  // Features for the 4 category cards
  const categories = [
    {
      title: "Communications",
      features: [
        "VNC, Web, USB, WiFi, Bluetooth",
        "Internal Wi-Fi optional (External Wi-Fi also available as an add-on)",
        "Apps & software work with multiple devices and allow freehand analysis"
      ]
    },
    {
      title: "Safe & Rugged",
      features: [
        "Arc flash safe design",
        "IP65 rated dust and water resistant",
        "Rugged hardware with proven design for shock absorbing construction"
      ]
    },
    {
      title: "User Interface",
      features: [
        "Pinch and zoom touch display control using finger styles",
        "Simple UI that is user driven, has context shortcuts, and a dashboard quick access console"
      ]
    },
    {
      title: "Productivity",
      features: [
        "Information Screen capability with easily accessible and intuitive visualization tools",
        "Bigger display for more viewable real-estate (Dranetz HDPQ series only)"
      ]
    }
  ];

  return (
    <Layout>
      {/* Main Title Header with added top padding */}
      <AnimatedSection>
        <div className="pt-12">
          <SubpageHeader
            title="Dranetz HDPQ Xplorer"
            subtitle="High-performance Class A analyzer with advanced features for comprehensive power quality analysis"
          />
        </div>
      </AnimatedSection>

      {/* Product Category SubHeader */}
      <AnimatedSection>
        <ProductCategoryHeader
          tabs={productTabs}
          showCompareButton={true}
        />
      </AnimatedSection>

      {/* Main content */}
      <div className="container max-w-7xl mx-auto px-6 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16 items-center">
          {/* Product Image */}
          <AnimatedSection>
            <img
              src="https://firebasestorage.googleapis.com/v0/b/atandra.firebasestorage.app/o/Atandraimages%2FHDPQ_Xplorer.png?alt=media&token=ac1514fe-58f2-4208-8ee4-701e210a6ab2?raw=true"
              alt="Dranetz HDPQ Xplorer"
              className="w-full h-auto shadow-lg rounded-lg"
            />
          </AnimatedSection>

          {/* Product Description */}
          <AnimatedSection>
            <h2 className="text-3xl font-bold mb-6">Dranetz HDPQ Xplorer</h2>
            <p className="text-gray-600 mb-4">
              The Dranetz HDPQ Xplorer is a high-performance Class A analyzer designed for comprehensive power quality analysis with advanced features for detailed investigation of electrical issues.
            </p>
            <p className="text-gray-600 mb-6">
              With high-speed transient conversion at 0Hz, 1MHz 2000Vp and eight channels (4 voltage & 4 current), the HDPQ Xplorer provides unparalleled insight into power quality events.
            </p>

            <h3 className="text-xl font-bold mb-4">Key Features</h3>
            <ul className="list-disc pl-5 mb-6 text-gray-600">
              <li>High Definition PQ & Energy Recording - Captures, RMS, LVF simultaneously</li>
              <li>High Speed Transient Conversion - 0Hz, 1MHz 2000Vp</li>
              <li>Eight Channels, 4 Voltage & 4 Current</li>
              <li>Multiple communication options: VNC, Web, USB, WiFi, Bluetooth</li>
              <li>IP65 rated dust and water resistant</li>
              <li>Arc flash safe design</li>
            </ul>

            <div className="flex flex-wrap gap-4">
              <Button asChild>
                <Link to="/contact">Request a Quote</Link>
              </Button>
              <Button variant="outline" asChild>
                <a href="/downloads/dranetz-hdpq-xplorer-datasheet.pdf" target="_blank" rel="noopener noreferrer">
                  Download Brochure
                </a>
              </Button>
            </div>
          </AnimatedSection>
        </div>

        {/* Product Categories */}
        <AnimatedSection>
          <h2 className="text-2xl font-bold mb-8 text-center">Product Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {categories.map((category, index) => (
              <Card key={index} className="shadow-lg">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold mb-4">Dranetz HDPQ Xplorer Family - {category.title}</h3>
                  <ul className="space-y-2">
                    {category.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start">
                        <span className="text-yellow-500 mr-2">✓</span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </AnimatedSection>

        {/* Technical Specifications */}
        <AnimatedSection>
          <h2 className="text-2xl font-bold mb-8 text-center mt-16">Technical Specifications</h2>
          <Tabs defaultValue="differentialVoltage" className="w-full">
            <TabsList className="w-full justify-center mb-8">
              <TabsTrigger value="differentialVoltage">Differential Voltage</TabsTrigger>
              <TabsTrigger value="current">Current</TabsTrigger>
              <TabsTrigger value="frequency">Frequency</TabsTrigger>
              <TabsTrigger value="physical">Physical</TabsTrigger>
              <TabsTrigger value="powerEnergy">Power/Energy</TabsTrigger>
            </TabsList>

            <TabsContent value="differentialVoltage">
              <Card className="p-6 shadow-lg">
                <CardContent className="pt-6">
                  <h3 className="text-lg font-bold mb-4">Differential Voltage: 10 bit resolution</h3>
                  <ul className="space-y-2">
                    {specifications.differentialVoltage.map((spec, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-yellow-500 mr-2">✓</span>
                        {spec}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="current">
              <Card className="p-6 shadow-lg">
                <CardContent className="pt-6">
                  <h3 className="text-lg font-bold mb-4">Current: 512 sps, 16 bit resolution</h3>
                  <ul className="space-y-2">
                    {specifications.current.map((spec, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-yellow-500 mr-2">✓</span>
                        {spec}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="frequency">
              <Card className="p-6 shadow-lg">
                <CardContent className="pt-6">
                  <h3 className="text-lg font-bold mb-4">Frequency: 10 sec window</h3>
                  <ul className="space-y-2">
                    {specifications.frequency.map((spec, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-yellow-500 mr-2">✓</span>
                        {spec}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="physical">
              <Card className="p-6 shadow-lg">
                <CardContent className="pt-6">
                  <h3 className="text-lg font-bold mb-4">Physical Dimensions</h3>
                  <ul className="space-y-2">
                    {specifications.physical.map((spec, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-yellow-500 mr-2">✓</span>
                        {spec}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="powerEnergy">
              <Card className="p-6 shadow-lg">
                <CardContent className="pt-6">
                  <h3 className="text-lg font-bold mb-4">Power/Energy - 1 Second sampling</h3>
                  <ul className="space-y-2">
                    {specifications.powerEnergy.map((spec, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-yellow-500 mr-2">✓</span>
                        {spec}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </AnimatedSection>

        {/* Applications */}
        <AnimatedSection>
          <h2 className="text-2xl font-bold mb-8 text-center mt-16">Applications</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {[
              {
                title: "Industrial Facilities",
                description: "Monitor power quality in manufacturing facilities to prevent equipment damage and production downtime."
              },
              {
                title: "Utility Monitoring",
                description: "Verify compliance with grid codes and standards for power quality at utility interconnections."
              },
              {
                title: "Commercial Buildings",
                description: "Identify power quality issues in commercial buildings that may affect sensitive equipment and data centers."
              },
              {
                title: "Renewable Energy Sites",
                description: "Monitor power quality at renewable energy generation sites to ensure grid compatibility."
              }
            ].map((application, index) => (
              <Card key={index} className="p-6 shadow-lg">
                <h3 className="text-xl font-bold mb-2">{application.title}</h3>
                <p className="text-gray-600">{application.description}</p>
              </Card>
            ))}
          </div>
        </AnimatedSection>

        {/* Call to Action */}
        <AnimatedSection>
          <div className="bg-gray-100 p-8 rounded-lg text-center shadow-lg mt-16">
            <h2 className="text-2xl font-bold mb-4">Need More Information?</h2>
            <p className="text-gray-600 mb-6">
              Contact our power quality experts for a personalized demonstration or quote for the Dranetz HDPQ Xplorer.
            </p>
            <Button size="lg" asChild>
              <Link to="/contact">Contact Our Experts</Link>
            </Button>
          </div>
        </AnimatedSection>
      </div>
    </Layout>
  );
};

const AnimatedSection = ({ children }) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 50 }}
      animate={inView ? { opacity: 1, y: 0 } : {}}
      transition={{ duration: 0.6 }}
    >
      {children}
    </motion.div>
  );
};

export default DranetzXplorerProductPage;