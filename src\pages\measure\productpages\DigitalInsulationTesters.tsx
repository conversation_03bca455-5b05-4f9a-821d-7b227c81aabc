import React, { useState } from 'react';
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import PageLayout from "@/components/layout/PageLayout";
// import PdfViewer from "@/components/ui/pdf-viewer";

const DigitalInsulationTesters = () => {
  const [activeTab, setActiveTab] = useState('digital');

  const navItems = [
    { id: 'overview', label: 'Overview' },
    { id: 'installation', label: 'Installation Testers' },
    { id: 'insulation', label: 'Insulation Testers' },
    { id: 'insulation-continuity', label: 'Insulation & Continuity Testers' },
    { id: 'digital', label: 'Digital Insulation Testers' },
    { id: 'earth', label: 'Earth & Resistivity Testers' },
    { id: 'radiometer', label: 'Radiometer' }
  ];

  return (
    <PageLayout
      title="Digital Insulation Testers"
      subtitle="High-performance testing equipment for precise insulation measurements up to 10 kV."
      category="measure"
    >
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-6xl mx-auto bg-background"
      >
        {/* Navigation */}
        <nav className="border-b border-border">
          <ul className="flex flex-wrap">
            {navItems.map((item) => (
              <li key={item.id}>
                <button
                  onClick={() => setActiveTab(item.id)}
                  className={`px-4 py-3 text-sm ${
                    activeTab === item.id
                      ? 'border-b-2 border-primary font-medium'
                      : 'text-muted-foreground'
                  }`}
                >
                  {item.label}
                </button>
              </li>
            ))}
          </ul>
        </nav>

        {/* Main Content */}
        <div className="flex flex-col p-4">
          {/* Header */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="flex flex-col md:flex-row mb-8"
          >
            <div className="w-full md:w-2/5 p-4 flex justify-center items-center">
              <img
                src="/api/placeholder/400/320"
                alt="Digital Insulation Tester"
                className="max-w-full h-auto"
              />
            </div>
            <div className="w-full md:w-3/5 p-4">
              <h1 className="text-4xl font-bold text-foreground mb-4">
                DIGITAL INSULATION<br />
                <span className="text-primary">TESTERS</span>
              </h1>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 bg-primary rounded-full flex items-center justify-center mr-2 mt-1">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-primary-foreground" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span>Models -C.A 6550, C.A 6555</span>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 bg-primary rounded-full flex items-center justify-center mr-2 mt-1">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-primary-foreground" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span>10,000 V insulation tester With its site-proof casing, the C.A 6550 insulation tester is used to check equipment insulation during manufacturing, on-site installation work, periodic inspections and recommissioning of installations.</span>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 bg-primary rounded-full flex items-center justify-center mr-2 mt-1">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-primary-foreground" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span>It is ideal for electricity companies, manufacturers, installers, maintenance firms, industry, R&D centres and infrastructure.</span>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 bg-primary rounded-full flex items-center justify-center mr-2 mt-1">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-primary-foreground" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span>Specially adapted for checking insulation on rotating machinery with a 12 kV power supply or higher, the C.A 6550 complies with the IEEE43 standard which recommends insulation testing at 5-10 kV.</span>
                </li>
              </ul>
              <div className="mt-6 flex space-x-4">
                <button className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-background border border-primary text-primary hover:bg-muted/50 h-10 py-2 px-4">
                  ENQUIRE
                </button>
                <button className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-background border border-primary text-primary hover:bg-muted/50 h-10 py-2 px-4">
                  BROCHURE
                </button>
              </div>
            </div>
          </motion.div>

          {/* Features Section */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="flex flex-col md:flex-row bg-muted/30 p-6 mb-8 rounded"
          >
            <div className="w-full md:w-2/3 p-4">
              <h2 className="text-2xl font-bold mb-4">Key Features</h2>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Wide measurement range from 10 kΩ to 25 TΩ</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Fixed or programmable test voltage from 40V to 10 kV</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>5mA max. charging current</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Large backlit LCD screen with digital display, bargraph and R(t)+u(t), i(t) and i(u) graphs</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Automatic ratio calculation: DAR / PI / DD / DR (ppm/V)</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Multiple test modes: voltage ramp and step with "burning", "early break" and "I-limit" modes</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Filters to optimize measurement stability</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Calculation of R at a reference temperature</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>80,000-measurement storage capacity and real-time clock</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Optically-isolated USB communication for transfer onto PC and report generation with DataView® software</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Power supply NiMH rechargeable battery with external charger</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Compliant with IEC 61557-1-2</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Electrical safety: IEC 61010 1000V CAT IV</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Dimensions: 406 x 330 x 174 mm / 6 kg</span>
                </li>
              </ul>
            </div>
            <div className="w-full md:w-1/3 p-4 flex justify-center items-center">
              <img
                src="/api/placeholder/400/320"
                alt="Digital Insulation Tester with accessories"
                className="max-w-full h-auto rounded"
              />
            </div>
          </motion.div>

          {/* Applications Section */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="mb-8"
          >
            <h2 className="text-2xl font-bold mb-4">Applications</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-muted/30 p-4 rounded">
                <h3 className="font-semibold mb-2">Industrial Applications</h3>
                <p className="text-muted-foreground">Ideal for checking equipment insulation during manufacturing, on-site installation work, and periodic inspections.</p>
              </div>
              <div className="bg-muted/30 p-4 rounded">
                <h3 className="font-semibold mb-2">Electrical Infrastructure</h3>
                <p className="text-muted-foreground">Perfect for electricity companies, power distribution networks, and high-voltage installations.</p>
              </div>
              <div className="bg-muted/30 p-4 rounded">
                <h3 className="font-semibold mb-2">Rotating Machinery</h3>
                <p className="text-muted-foreground">Specially adapted for checking insulation on rotating machinery with a 12 kV power supply or higher.</p>
              </div>
              <div className="bg-muted/30 p-4 rounded">
                <h3 className="font-semibold mb-2">R&D and Testing</h3>
                <p className="text-muted-foreground">Advanced features and precise measurements make it suitable for research and development centers.</p>
              </div>
            </div>
          </motion.div>

          {/* Compliance Section */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="bg-muted/30 p-6 rounded mb-8"
          >
            <h2 className="text-2xl font-bold mb-4">Standards Compliance</h2>
            <ul className="space-y-2">
              <li className="flex items-center">
                <span className="text-primary mr-2">✓</span>
                <span>IEC 61557-1-2: Electrical safety in low voltage distribution systems</span>
              </li>
              <li className="flex items-center">
                <span className="text-primary mr-2">✓</span>
                <span>IEC 61010 1000V CAT IV: Safety requirements for electrical equipment</span>
              </li>
              <li className="flex items-center">
                <span className="text-primary mr-2">✓</span>
                <span>IEEE43: Recommended practice for testing insulation resistance of rotating machinery</span>
              </li>
            </ul>
          </motion.div>

          {/* Product Selection Help Section */}
          <motion.section 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="mt-16 bg-muted/30 p-8 rounded-lg"
          >
            <h2 className="text-2xl font-bold mb-6 text-center">Need Help Selecting the Right Product?</h2>
            <p className="text-center text-muted-foreground mb-8">
              Our team of experts can help you choose the right solution for your specific needs.
            </p>
            <div className="flex justify-center">
              <Link 
                to="/contact" 
                className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-primary text-primary-foreground hover:bg-primary/90 h-10 py-2 px-4"
              >
                Contact Our Experts
              </Link>
            </div>
          </motion.section>
        </div>
      </motion.div>
    </PageLayout>
  );
};

export default DigitalInsulationTesters;