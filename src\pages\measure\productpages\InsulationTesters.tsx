import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import PageLayout from "@/components/layout/PageLayout";
import { motion } from "framer-motion";

const InsulationTesters = () => {
  const [activeTab, setActiveTab] = useState('insulation');
  const [activeModelTab, setActiveModelTab] = useState('ca6500');

  const navItems = [
    { id: 'overview', label: 'Overview' },
    { id: 'installation', label: 'Installation Testers' },
    { id: 'insulation', label: 'Insulation Testers' },
    { id: 'insulation-continuity', label: 'Insulation & Continuity Testers' },
    { id: 'digital', label: 'Digital Insulation Testers' },
    { id: 'earth', label: 'Earth & Resistivity Testers' },
    { id: 'radiometer', label: 'Radiometer' }
  ];

  const models = [
    {
      id: 'ca6500',
      title: 'C.A 6500 Series',
      models: 'Models -C.A 6505, C.A 6541, C.A 6543, C.A 6545, C.A 6547, C.A 6549',
      image: '/api/placeholder/400/320',
      description: 'The C.A 6541 is a high-performance 50V to 1kV insulation tester',
      subDescription: 'It supports the most demanding site conditions and is ideal for qualitative insulation analysis.',
      features: [
        'Insulation: up to 4 TΩ (5 cal.) at 50/100/250/500 and 1,000 V DC',
        'automatic calculation of insulation quality ratios: DAR and PI',
        'programmable test duration and plotting of the R(t) curve (automatic storage of 20 samples)',
        'automatic indication of voltage before and after measurement: 1 to 1,000 V AC/DC',
        'resistance: 0.01 Ω to 400 kΩ',
        'continuity: 0.01Ω to +40 Ω (audible beep and lead compensation)',
        'automatic measurement capacity after each insulation measurement: 0.005 to 4.999 µF',
        'microprocessor-controlled, large LCD screen with digital display + bargraph, backlighting',
        'SMOOTH function, insulation test voltages can be locked, programmable alarms',
        'long battery life: 6 x LR14 batteries (21,000 insulation tests of 5s each)',
        'remote control probe available as an option',
        'electrical safety: IEC 61010 600V CAT III and IEC 61557',
        'dimensions: 240 x 185 x 110mm; weight: 3.4 kg'
      ]
    },
    {
      id: 'atest',
      title: 'ATESR S 1100',
      models: 'Models - ATEST S 1100',
      image: '/api/placeholder/400/320',
      description: 'TRMS Auto AC/DC Voltage Detection',
      subDescription: 'Innovative Insulation Resistance Tester',
      features: [
        'TRMS Auto AC/DC Voltage Detection',
        'Innovative Insulation Resistance Tester',
        '4000 count extra large digital display',
        'Insulation test range: 0.01M Ω to 20G Ω',
        'Automatic calculation of (PI) and (DAR)',
        'Convenient compare (Pass/Fail) function',
        'Ergonomically designed Remote probe for repetitive testing',
        'Measurement of TRMS AC Voltages up to 600V DC',
        'Compact, robust and shockproof housing, with protective holster',
        'Memory: 5 x 100 memories available',
        'Supplied with active test probe'
      ]
    }
  ];

  const specifications = [
    { parameter: 'DC Voltage', range: '600V', accuracy: '1' },
    { parameter: 'AC Voltage', range: '600V', accuracy: '1.5' },
    { parameter: 'Earth Bond Resistance', range: '40Ω ~ 40kΩ', accuracy: '1.5' },
    { parameter: 'Test Voltage', range: '50V, 100V, 250V, 500V, 1000V', accuracy: '' },
    { parameter: 'Insulation Resistance', range: '0.001M Ω ~ 20.0GΩ', accuracy: '1.5' },
    { parameter: 'Continuity', range: 'under 200mA', accuracy: '' }
  ];

  const activeModel = models.find(model => model.id === activeModelTab);

  return (
    <PageLayout
      title="Insulation Testers"
      subtitle="High-precision devices for measuring insulation resistance and integrity in electrical systems."
      category="measure"
    >
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8 text-center"
      >
        <h2 className="text-3xl font-bold mb-4">Insulation Testers</h2>
        <p className="text-muted-foreground mb-6 max-w-3xl mx-auto">
          Professional testing devices to ensure the safety and performance of electrical insulation systems.
        </p>
      </motion.div>

      <div className="flex flex-col w-full max-w-6xl mx-auto bg-background border border-border rounded-lg overflow-hidden shadow-sm mb-8">
        {/* Navigation */}
        <nav className="border-b">
          <ul className="flex flex-wrap">
            {navItems.map((item) => (
              <li key={item.id}>
                <button
                  onClick={() => setActiveTab(item.id)}
                  className={`px-4 py-3 text-sm ${
                    activeTab === item.id
                      ? 'border-b-2 border-primary font-medium'
                      : 'text-muted-foreground'
                  }`}
                >
                  {item.label}
                </button>
              </li>
            ))}
          </ul>
        </nav>

        {/* Model Selector Tabs */}
        <div className="border-b">
          <ul className="flex flex-wrap">
            {models.map((model) => (
              <li key={model.id}>
                <button
                  onClick={() => setActiveModelTab(model.id)}
                  className={`px-4 py-2 text-sm ${
                    activeModelTab === model.id
                      ? 'bg-primary/10 font-medium'
                      : 'text-muted-foreground'
                  }`}
                >
                  {model.title}
                </button>
              </li>
            ))}
          </ul>
        </div>

        {/* Main Content */}
        <div className="flex flex-col p-4">
          {/* Header */}
          <div className="flex flex-col md:flex-row mb-8">
            <div className="w-full md:w-1/3 p-4 flex justify-center items-center">
              <img
                src={activeModel.image}
                alt={`${activeModel.title} Insulation Tester`}
                className="max-w-full h-auto"
              />
            </div>
            <div className="w-full md:w-2/3 p-4">
              <h1 className="text-4xl font-bold text-primary mb-4">
                INSULATION <span className="text-primary-foreground bg-primary px-2">TESTERS</span>
              </h1>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 bg-primary rounded-full flex items-center justify-center mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span>{activeModel.models}</span>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 bg-primary rounded-full flex items-center justify-center mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span>{activeModel.description}</span>
                </li>
                {activeModel.subDescription && (
                  <li className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 bg-primary rounded-full flex items-center justify-center mr-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span>{activeModel.subDescription}</span>
                  </li>
                )}
              </ul>
              <div className="mt-6 flex space-x-4">
                <button className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-primary bg-background hover:bg-primary/10 text-primary py-2 px-4">
                  ENQUIRE
                </button>
                <button className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-primary bg-background hover:bg-primary/10 text-primary py-2 px-4">
                  BROCHURE
                </button>
              </div>
            </div>
          </div>

          {/* Features Section */}
          <div className="flex flex-col md:flex-row bg-muted/30 p-6 mb-8 rounded">
            <div className="w-full md:w-2/3 p-4">
              <h2 className="text-2xl font-bold mb-4">Features</h2>
              <ul className="space-y-2">
                {activeModel.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-primary mr-2">•</span>
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div className="w-full md:w-1/3 p-4 flex justify-center items-center">
              <img
                src={activeModel.image}
                alt={`${activeModel.title} with accessories`}
                className="max-w-full h-auto rounded"
              />
            </div>
          </div>

          {/* Specifications Table */}
          {activeModelTab === 'atest' && (
            <div className="mb-8">
              <h2 className="text-2xl font-bold mb-4">Technical Specifications</h2>
              <div className="overflow-x-auto">
                <table className="min-w-full bg-background">
                  <thead>
                    <tr className="bg-primary text-primary-foreground">
                      <th className="py-2 px-4 text-left">PARAMETER</th>
                      <th className="py-2 px-4 text-left">RANGE</th>
                      <th className="py-2 px-4 text-left">ACCURACY %</th>
                    </tr>
                  </thead>
                  <tbody>
                    {specifications.map((spec, index) => (
                      <tr key={index} className={index % 2 === 0 ? 'bg-muted/30' : 'bg-background'}>
                        <td className="py-2 px-4 border border-border">{spec.parameter}</td>
                        <td className="py-2 px-4 border border-border">{spec.range}</td>
                        <td className="py-2 px-4 border border-border">{spec.accuracy}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Related Products Section */}
      <motion.section 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="mb-16"
      >
        <h2 className="text-2xl font-bold mb-6">Related Products</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[
            {
              id: "ca-6541",
              name: "C.A 6541/C.A 6543",
              description: "Professional insulation tester with data logging capabilities.",
              features: [
                "Insulation Test range: 2kΩ to 4TΩ",
                "Test Voltage: 50V, 100V, 250V, 500V, 1000V"
              ],
              image: "/api/placeholder/400/320"
            },
            {
              id: "ca-6505",
              name: "C.A 6505/C.A 6545",
              description: "High-end insulation testers for demanding applications.",
              features: [
                "Insulation Test range: 10kΩ to 10TΩ",
                "Test Voltage: 500V, 1000V, 2500V, 5000V"
              ],
              image: "/api/placeholder/400/320"
            },
            {
              id: "ca-6550",
              name: "C.A 6550/ C.A 6555",
              description: "Advanced insulation tester with high storage capacity.",
              features: [
                "Insulation Test range: 10kΩ to 25TΩ/30TΩ",
                "Test Voltage: 100V/15kV"
              ],
              image: "/api/placeholder/400/320"
            }
          ].map((product) => (
            <div
              key={product.id}
              className="bg-background border border-border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow"
            >
              <div className="p-4 flex flex-col h-full">
                <div className="mb-4">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-48 object-contain"
                  />
                </div>
                <h3 className="text-xl font-semibold mb-2">{product.name}</h3>
                <p className="text-muted-foreground text-sm mb-4 flex-grow">{product.description}</p>
                <ul className="space-y-1 mb-4">
                  {product.features.map((feature, index) => (
                    <li key={index} className="text-sm flex items-start">
                      <span className="mr-2 text-primary">•</span>
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
                <Link
                  to={`/measure/safety-instruments/insulation-testers/${product.id}`}
                  className="text-primary hover:text-primary/80 text-sm font-medium"
                >
                  Learn More
                </Link>
              </div>
            </div>
          ))}
        </div>
      </motion.section>

      {/* Product Selection Help Section */}
      <section className="mt-16 bg-muted/30 p-8 rounded-lg">
        <h2 className="text-2xl font-bold mb-6 text-center">Need Help Selecting the Right Product?</h2>
        <p className="text-center text-muted-foreground mb-8">
          Our team of experts can help you choose the right solution for your specific needs.
        </p>
        <div className="flex justify-center">
          <Link 
            to="/contact" 
            className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-primary text-primary-foreground hover:bg-primary/90 h-10 py-2 px-4"
          >
            Contact Our Experts
          </Link>
        </div>
      </section>
    </PageLayout>
  );
};

export default InsulationTesters;