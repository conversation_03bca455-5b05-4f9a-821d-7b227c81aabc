import React from "react";
import { <PERSON> } from "react-router-dom";
import { motion } from "framer-motion";
import PageLayout from "@/components/layout/PageLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription } from "@/components/ui/card";
import { ChevronRight, Leaf, BarChart, Lightbulb, Battery, Sun, Building, Cloud, Server, Clock, Zap, Gauge, Factory, Cpu, Plug, Wifi, LineChart, Settings, ArrowRight, FileText } from "lucide-react";

// Enhanced product data with more detailed information
const conserveProducts = [
  {
    id: "on-premise",
    name: "On Premise System",
    slug: "on-premise-system",
    description: "Advanced voltage regulation for critical industrial applications",
    icon: Factory,
    detailPath: "/conserve/on-premise-systems",
    features: [
      "Real-time energy consumption monitoring",
      "Automated voltage optimization",
      "Customizable energy-saving profiles"
    ]
  },
  {
    id: "cloud",
    name: "Cloud Energy Management",
    slug: "cloud-energy-management",
    description: "Remote monitoring and control of energy systems",
    icon: Wifi,
    detailPath: "/conserve/cloud-energy-management",
    features: [
      "Centralized dashboard for multiple locations",
      "Predictive maintenance alerts",
      "Energy usage analytics and reporting"
    ]
  },
  {
    id: "lighting",
    name: "Lighting Energy Server",
    slug: "lighting-energy-server",
    description: "Smart lighting control for energy efficiency",
    icon: Lightbulb,
    detailPath: "/conserve/lighting-energy-saver",
    features: [
      "Daylight harvesting capabilities",
      "Occupancy-based lighting control",
      "Integration with building management systems"
    ]
  },
  {
    id: "energy-audits",
    name: "Energy Audits",
    slug: "energy-audits",
    description: "Comprehensive ESG reporting and energy audit solutions",
    icon: FileText,
    detailPath: "/conserve/energy-audits",
    features: [
      "Automated ESG data collection and reporting",
      "AI-powered anomaly detection",
      "Regulatory compliance templates (BRSR, GRI, CSRD)"
    ]
  },
];

// Define the product type
interface Product {
  id: string;
  name: string;
  slug: string;
  description: string;
  icon: React.ElementType;
  detailPath: string;
  features: string[];
}

const Conserve = () => {
  // Create groups of products for each section (only using what's available)
  const firstRow = conserveProducts.slice(0, 3);
  const secondRow = conserveProducts.slice(3, 6);
  const thirdRow = conserveProducts.slice(6, 9);

  // Enhanced product card component with proper routing
  const ProductCard = ({ product }: { product: Product }) => {
    const Icon = product.icon;
    return (
      <Card className="h-full overflow-hidden bg-white shadow-sm hover:shadow-md transition-all duration-300 border-0 hover:border-green-200 group">
        <CardHeader className="pb-0">
          <div className="flex items-center mb-4">
            <div className="p-3 bg-green-100 rounded-full mr-4 group-hover:bg-green-200 transition-colors">
              <Icon className="h-6 w-6 text-green-600" />
            </div>
            <CardTitle className="text-xl font-bold text-green-900">{product.name}</CardTitle>
          </div>
          <CardDescription className="text-green-700 mt-2">{product.description}</CardDescription>

          {product.features && (
            <div className="mt-4">
              <ul className="space-y-2">
                {product.features.map((feature: string, idx: number) => (
                  <li key={idx} className="flex items-start">
                    <div className="mr-2 mt-1 text-green-500">
                      <div className="h-1.5 w-1.5 rounded-full bg-green-500"></div>
                    </div>
                    <span className="text-sm text-green-700">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </CardHeader>

        <CardFooter className="mt-auto pt-4">
          <Link to={product.detailPath} className="w-full">
            <Button
              className="w-full bg-green-500 hover:bg-green-600 text-white rounded-md flex items-center justify-center group"
            >
              <span>Learn More</span>
              <ChevronRight size={16} className="ml-1 transition-transform duration-300 group-hover:translate-x-1" />
            </Button>
          </Link>
        </CardFooter>
      </Card>
    );
  };

  return (
    <PageLayout
      title="CONSERVE"
      subtitle="Optimize energy consumption"
      category="conserve"
    >
      {/* Container to limit page width */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6">
        {/* Hero Section - UPDATED based on Measure.tsx reference */}
        <div className="py-16 mb-16">
          <div className="grid grid-cols-1 md:grid-cols-12 gap-8 items-center">
            <div className="md:col-span-7">
              <h1 className="text-3xl md:text-5xl font-bold text-green-500 mb-4 leading-tight">
                ENERGY CONSERVATION SOLUTIONS
              </h1>
              <div className="w-24 h-1 bg-green-400 mb-6"></div>
              <p className="text-green-800 text-lg max-w-2xl">
                Our flagship KRYKARD Static Voltage Regulator and other solutions help reduce your carbon footprint and operational costs with intelligent energy management
              </p>
              <div className="flex flex-wrap gap-4 mt-8">
                <Link to="/contact/sales">
                  <Button className="bg-green-500 hover:bg-green-600 text-white rounded-full px-6 py-2 font-medium flex items-center gap-2">
                    <span>Enquiry</span>
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </div>

            <div className="md:col-span-5">
              <div className="relative w-72 h-72 mx-auto">
                {/* Glowing background effect */}
                <div className="absolute inset-0 rounded-full bg-green-200/30 blur-xl"></div>

                {/* Main center icon with pulsing animation */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 p-5 rounded-full bg-green-300/40 backdrop-blur-sm animate-pulse">
                  <Leaf className="h-20 w-20 text-green-800 drop-shadow-lg" />
                </div>

                {/* Orbiting icons */}
                <div className="absolute inset-0">
                  {/* Top icon */}
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-green-200/40 backdrop-blur-sm rounded-full p-3 shadow-lg">
                    <BarChart className="h-10 w-10 text-green-700" />
                  </div>

                  {/* Right icon */}
                  <div className="absolute top-1/2 right-0 transform translate-x-1/2 -translate-y-1/2 bg-green-200/40 backdrop-blur-sm rounded-full p-3 shadow-lg">
                    <Lightbulb className="h-10 w-10 text-green-700" />
                  </div>

                  {/* Bottom icon */}
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 bg-green-200/40 backdrop-blur-sm rounded-full p-3 shadow-lg">
                    <Battery className="h-10 w-10 text-green-700" />
                  </div>

                  {/* Left icon */}
                  <div className="absolute top-1/2 left-0 transform -translate-x-1/2 -translate-y-1/2 bg-green-200/40 backdrop-blur-sm rounded-full p-3 shadow-lg">
                    <Sun className="h-10 w-10 text-green-700" />
                  </div>
                </div>

                {/* Floating small icons with different animation */}
                <div className="absolute top-1/4 right-1/4 bg-green-200/40 backdrop-blur-sm rounded-full p-2 animate-float">
                  <Clock className="h-6 w-6 text-green-700" />
                </div>

                <div className="absolute bottom-1/4 left-1/4 bg-green-200/40 backdrop-blur-sm rounded-full p-2 animate-float" style={{ animationDelay: '2s' }}>
                  <Server className="h-6 w-6 text-green-700" />
                </div>
              </div>
            </div>
          </div>
        </div>

      {/* First Row of Products */}
      {firstRow.length > 0 && (
        <div className="mb-16">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-2xl font-bold text-green-800">Voltage Regulation & Control</h2>
            <div className="h-px bg-green-200 flex-grow ml-6"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {firstRow.map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <ProductCard product={product} />
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Second Row of Products - Only show if there are products */}
      {secondRow && secondRow.length > 0 && (
        <div className="mb-16">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-2xl font-bold text-green-800">System Optimization</h2>
            <div className="h-px bg-green-200 flex-grow ml-6"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {secondRow.map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <ProductCard product={product} />
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Third Row of Products - Only show if there are products */}
      {thirdRow && thirdRow.length > 0 && (
        <div className="mb-16">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-2xl font-bold text-green-800">Smart Technologies</h2>
            <div className="h-px bg-green-200 flex-grow ml-6"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {thirdRow.map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <ProductCard product={product} />
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Feature Section */}
      <div className="mb-16 bg-green-50 rounded-lg p-8 shadow-sm">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-green-800 mb-3">KRYKARD Static Voltage Regulator Benefits</h2>
          <p className="text-green-700 max-w-2xl mx-auto">Our flagship product delivers measurable results for industrial and commercial applications</p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
          {[
            { title: "Energy Savings", value: "Up to 30% reduction", icon: Leaf },
            { title: "Equipment Lifespan", value: "Extended by 25%", icon: Settings },
            { title: "Voltage Stability", value: "±0.5% regulation", icon: Zap },
            { title: "ROI Timeline", value: "12-18 months typical", icon: LineChart }
          ].map((feature, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-sm text-center hover:shadow-md transition-all duration-300">
              <div className="flex justify-center mb-3">
                {React.createElement(feature.icon, { className: "h-8 w-8 text-green-500" })}
              </div>
              <h3 className="text-green-800 font-bold mb-2">{feature.title}</h3>
              <p className="text-green-600">{feature.value}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Extended Benefits Section */}
      <div className="mb-16">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-2xl font-bold text-green-800">KRYKARD Product Advantages</h2>
          <div className="h-px bg-green-200 flex-grow ml-6"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border border-green-100 hover:shadow-md transition-all duration-300">
            <div className="flex items-center mb-4">
              <div className="p-3 bg-green-100 rounded-full mr-4">
                <Leaf className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="text-xl font-bold text-green-900">Environmental Impact</h3>
            </div>
            <p className="text-green-700">KRYKARD solutions reduce greenhouse gas emissions with verified energy savings of up to 30% in most industrial applications.</p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-green-100 hover:shadow-md transition-all duration-300">
            <div className="flex items-center mb-4">
              <div className="p-3 bg-green-100 rounded-full mr-4">
                <LineChart className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="text-xl font-bold text-green-900">Performance Analytics</h3>
            </div>
            <p className="text-green-700">Built-in monitoring provides real-time data on energy consumption, voltage stability, and system performance.</p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-green-100 hover:shadow-md transition-all duration-300">
            <div className="flex items-center mb-4">
              <div className="p-3 bg-green-100 rounded-full mr-4">
                <Cpu className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="text-xl font-bold text-green-900">Smart Integration</h3>
            </div>
            <p className="text-green-700">KRYKARD products seamlessly integrate with existing infrastructure and can be monitored via our cloud-based management platform.</p>
          </div>
        </div>
      </div>

      {/* Contact Information Section */}
      <div className="mt-16 mb-16 bg-green-50 p-10 rounded-lg shadow-sm overflow-hidden relative">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-green-600 mb-6">Need More Information?</h2>
          <p className="text-green-700 text-lg mb-8 max-w-2xl mx-auto">
            Our team of experts is ready to help you with product specifications, custom solutions,
            pricing, and any other details you need about the KRYKARD Static Voltage Regulator.
          </p>
          <div className="flex justify-center">
            <Link to="/contact/sales">
              <Button className="bg-green-500 hover:bg-green-600 text-white shadow-md px-8 py-2 text-lg font-medium">
                Contact Our Experts
              </Button>
            </Link>
          </div>
        </div>
      </div>
      </div>
    </PageLayout>
  );
};

export default Conserve;