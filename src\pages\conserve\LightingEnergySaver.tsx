import React, { useState, useEffect, useRef } from 'react';
import {
  Building2,
  Users,
  BarChart3,
  Shield,
  Clock,
  CheckCircle,
  Home,
  Wrench,
  Building,
  TrendingUp,
  Bell,
  Gauge,
  Eye,
  Award,
  Monitor,
  Lightbulb,
  Target,
  Settings,
  Globe,
  ChevronDown
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import PageLayout from "@/components/layout/PageLayout";

const LightingEnergySaverPage = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedModule, setSelectedModule] = useState<number | null>(null);
  const [countsStarted, setCountsStarted] = useState(false);
  const statsRef = useRef<HTMLDivElement>(null);

  const stats = [
    { label: 'Energy Savings', value: '30-50%', displayValue: '30-50%', icon: TrendingUp, color: 'from-green-500 to-emerald-600' },
    { label: 'ROI Period', value: '1-2 Years', displayValue: '1-2 Years', icon: Award, color: 'from-emerald-500 to-teal-600' },
    { label: 'Installations', value: '1000+', displayValue: '1000+', countTo: 1000, icon: Monitor, color: 'from-teal-500 to-green-600' },
    { label: 'LED Lifespan', value: '50,000h', displayValue: '50,000h', countTo: 50000, icon: Lightbulb, color: 'from-green-600 to-emerald-700' }
  ];

  // Count-up animation state
  const [installationsCount, setInstallationsCount] = useState(0);
  const [lifespanCount, setLifespanCount] = useState(0);

  // Intersection Observer for triggering count-up
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && !countsStarted) {
          setCountsStarted(true);

          // Animate installations count
          const installationsTarget = 1000;
          const installationsDuration = 2000;
          const installationsStartTime = Date.now();

          const animateInstallations = () => {
            const elapsed = Date.now() - installationsStartTime;
            const progress = Math.min(elapsed / installationsDuration, 1);
            const current = Math.floor(progress * installationsTarget);
            setInstallationsCount(current);

            if (progress < 1) {
              requestAnimationFrame(animateInstallations);
            }
          };

          // Animate lifespan count
          const lifespanTarget = 50000;
          const lifespanDuration = 2500;
          const lifespanStartTime = Date.now();

          const animateLifespan = () => {
            const elapsed = Date.now() - lifespanStartTime;
            const progress = Math.min(elapsed / lifespanDuration, 1);
            const current = Math.floor(progress * lifespanTarget);
            setLifespanCount(current);

            if (progress < 1) {
              requestAnimationFrame(animateLifespan);
            }
          };

          // Start animations
          requestAnimationFrame(animateInstallations);
          requestAnimationFrame(animateLifespan);
        }
      },
      { threshold: 0.3 }
    );

    if (statsRef.current) {
      observer.observe(statsRef.current);
    }

    return () => observer.disconnect();
  }, [countsStarted]);

  const addOnModules = [
    {
      title: 'Smart LED Technology',
      description: 'Advanced LED lighting systems with intelligent controls for optimal energy efficiency',
      icon: Lightbulb,
      features: ['High-efficiency LEDs', 'Intelligent dimming', 'Color temperature control', 'Energy optimization']
    },
    {
      title: 'Automated Controls',
      description: 'Motion sensors, daylight harvesting, and programmable scheduling systems',
      icon: Gauge,
      features: ['Motion detection', 'Daylight harvesting', 'Programmable scheduling', 'Occupancy sensing']
    },
    {
      title: 'Energy Monitoring',
      description: 'Real-time energy consumption tracking with detailed analytics',
      icon: BarChart3,
      features: ['Real-time monitoring', 'Energy analytics', 'Usage optimization', 'Performance reports']
    },
    {
      title: 'Remote Management',
      description: 'Cloud-based control system for monitoring and managing lighting remotely',
      icon: Monitor,
      features: ['Cloud-based control', 'Remote monitoring', 'Multi-location management', 'Mobile access']
    },
    {
      title: 'Maintenance Alerts',
      description: 'Predictive maintenance notifications and automated fault detection',
      icon: Bell,
      features: ['Predictive maintenance', 'Fault detection', 'Alert notifications', 'Maintenance scheduling']
    },
    {
      title: 'Custom Solutions',
      description: 'Tailored lighting designs for specific environments and applications',
      icon: Eye,
      features: ['Custom design', 'Environment-specific', 'Application tailored', 'Professional installation']
    }
  ];

  const coreFeatures = [
    'Smart LED lighting technology',
    'Automated control systems',
    'Real-time energy monitoring',
    'Remote management capabilities',
    'Predictive maintenance alerts',
    'Custom lighting solutions',
    'Mobile and web access',
    'Professional installation support'
  ];

  const benefits = [
    {
      title: 'Energy Savings',
      description: 'Reduce lighting energy consumption by 30-50% with smart LED technology',
      icon: TrendingUp
    },
    {
      title: 'Lower Maintenance',
      description: 'LED lifespan up to 50,000 hours significantly reduces replacement costs',
      icon: Clock
    },
    {
      title: 'Improved Productivity',
      description: 'Better lighting quality enhances workplace performance and comfort',
      icon: Users
    },
    {
      title: 'Environmental Impact',
      description: 'Significant reduction in carbon footprint and environmental impact',
      icon: Shield
    }
  ];

  const industries = [
    { title: "Office Buildings", icon: Building },
    { title: "Manufacturing Facilities", icon: Wrench },
    { title: "Retail Stores", icon: Building2 },
    { title: "Warehouses", icon: Home },
    { title: "Educational Institutions", icon: Users },
    { title: "Healthcare Facilities", icon: Shield }
  ];

  return (
    <div className="font-['Open_Sans'] min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 w-full overflow-x-hidden">
      <PageLayout
        title="Lighting Energy Saver"
        subtitle="Smart LED Lighting Solutions"
        category="conserve"
      >
        {/* Modern Blended Hero Section */}
        <div className="relative overflow-hidden">
          {/* Seamless Background Blend */}
          <div className="absolute inset-0 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50"></div>

          {/* Organic Floating Elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-br from-green-200/30 to-emerald-300/20 rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute bottom-10 right-10 w-80 h-80 bg-gradient-to-br from-emerald-200/25 to-teal-300/20 rounded-full blur-2xl animate-pulse delay-1000"></div>
            <div className="absolute top-1/3 right-1/4 w-64 h-64 bg-gradient-to-br from-teal-200/20 to-green-300/15 rounded-full blur-xl animate-pulse delay-500"></div>
          </div>

          {/* Subtle Grid Pattern */}
          <div className="absolute inset-0 opacity-[0.02]">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='%23059669' stroke-width='1'%3E%3Cpath d='M0 0h100v100H0z'/%3E%3Cpath d='M0 50h100M50 0v100'/%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </div>

          <div className="relative px-4 sm:px-6 lg:px-8 py-16 lg:py-24">
            <div className="max-w-7xl mx-auto">
              <div className="grid lg:grid-cols-12 gap-12 lg:gap-16 items-center">
                {/* Left Content - 7 columns */}
                <div className="lg:col-span-7 text-center lg:text-left space-y-8">
                  {/* Floating Badge */}
                  <div className="inline-flex items-center px-6 py-3 rounded-full bg-white/80 backdrop-blur-sm border border-green-200/50 shadow-lg">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-3 animate-pulse"></div>
                    <span className="text-sm font-bold text-green-700 tracking-wide font-['Open_Sans']">Smart LED Technology</span>
                  </div>

                  {/* Main Heading */}
                  <div className="space-y-4">
                    <h1 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-black leading-tight font-['Open_Sans']">
                      <span className="text-gray-900 block">Smart</span>
                      <span className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent block">
                        Lighting Energy
                      </span>
                      <span className="text-gray-800 block">Solutions</span>
                    </h1>

                    {/* Decorative Line */}
                    <div className="flex justify-center lg:justify-start">
                      <div className="w-24 h-1 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full"></div>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-lg lg:text-xl text-gray-800 leading-relaxed font-semibold max-w-2xl mx-auto lg:mx-0 font-['Open_Sans']">
                    Transform your lighting infrastructure with our comprehensive LED platform designed for
                    <span className="text-green-700 font-black"> maximum energy savings</span>,
                    <span className="text-emerald-700 font-black"> intelligent automation</span>, and
                    <span className="text-teal-700 font-black"> superior performance</span>
                  </p>

                  {/* Key Highlights */}
                  <div className="flex flex-wrap gap-4 justify-center lg:justify-start">
                    <div className="flex items-center space-x-2 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-full border border-green-200 shadow-md">
                      <CheckCircle className="w-4 h-4 text-green-700" />
                      <span className="text-sm font-bold text-gray-800 font-['Open_Sans']">30-50% Energy Savings</span>
                    </div>
                    <div className="flex items-center space-x-2 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-full border border-emerald-200 shadow-md">
                      <CheckCircle className="w-4 h-4 text-emerald-700" />
                      <span className="text-sm font-bold text-gray-800 font-['Open_Sans']">50,000h LED Life</span>
                    </div>
                    <div className="flex items-center space-x-2 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-full border border-teal-200 shadow-md">
                      <CheckCircle className="w-4 h-4 text-teal-700" />
                      <span className="text-sm font-bold text-gray-800 font-['Open_Sans']">Smart Controls</span>
                    </div>
                  </div>
                </div>

                {/* Right Content - 5 columns */}
                <div className="lg:col-span-5 relative">
                  <div className="relative group">
                    {/* Main Image */}
                    <div className="relative overflow-hidden rounded-3xl shadow-2xl">
                      <img
                        src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop&auto=format"
                        alt="Smart LED Lighting System"
                        className="w-full h-auto object-cover group-hover:scale-105 transition-all duration-700"
                        loading="lazy"
                      />

                      {/* Gradient Overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-green-900/20 via-transparent to-transparent"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Section - Table Format */}
        <div ref={statsRef} className="py-8 sm:py-10 bg-white relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </div>

          <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-6 sm:mb-8">
              <h2 className="text-2xl sm:text-3xl lg:text-4xl font-black text-gray-900 mb-3 font-['Open_Sans']">
                <span className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  Lighting Performance
                </span>
              </h2>
              <p className="text-base sm:text-lg text-gray-800 font-bold max-w-2xl mx-auto font-['Open_Sans']">
                Experience exceptional results with our smart LED lighting solutions
              </p>
            </div>

            {/* Table-style Stats Display */}
            <div className="bg-white rounded-xl sm:rounded-2xl shadow-2xl border-2 border-green-100 overflow-hidden max-w-4xl mx-auto">
              {/* Table Header Row */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 bg-gradient-to-r from-green-50 to-emerald-50 border-b-2 border-green-100">
                {stats.map((stat, index) => (
                  <div key={index} className="p-4 sm:p-6 text-center border-b sm:border-b-0 sm:border-r border-green-100 last:border-b-0 last:border-r-0">
                    <h3 className="text-sm sm:text-base lg:text-lg font-black text-gray-900 leading-tight font-['Open_Sans']">
                      {stat.label}
                    </h3>
                  </div>
                ))}
              </div>

              {/* Table Values Row */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 bg-white">
                {stats.map((stat, index) => {
                  const getDisplayValue = () => {
                    if (stat.countTo === 1000) {
                      return countsStarted ? `${installationsCount}+` : '0+';
                    } else if (stat.countTo === 50000) {
                      return countsStarted ? `${lifespanCount.toLocaleString()}h` : '0h';
                    }
                    return stat.displayValue;
                  };

                  return (
                    <div key={index} className="p-6 sm:p-8 text-center border-b sm:border-b-0 sm:border-r border-green-100 last:border-b-0 last:border-r-0 group hover:bg-green-50 transition-all duration-300">
                      <div className="text-2xl sm:text-3xl lg:text-4xl font-black text-green-700 group-hover:text-green-800 transition-colors font-['Open_Sans']">
                        {getDisplayValue()}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Section with Tabs */}
        <div className="py-12 sm:py-16 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 relative">
          {/* Background Elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-200/20 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-200/20 rounded-full blur-3xl"></div>
          </div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-8 sm:mb-10">
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 mb-3 sm:mb-4 font-['Open_Sans']">
                <span className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  Comprehensive LED Lighting
                </span>
                <br />
                <span className="text-gray-800">Management Solution</span>
              </h2>
              <p className="text-base sm:text-lg lg:text-xl xl:text-2xl text-gray-700 max-w-4xl mx-auto font-medium leading-relaxed font-['Open_Sans']">
                Transform how you illuminate, manage, and optimize lighting across your entire operations with our
                <span className="text-green-600 font-bold"> smart LED platform</span>
              </p>
            </div>

            {/* Tab Navigation */}
            <div className="flex flex-wrap justify-center mb-8 sm:mb-10 bg-white/80 backdrop-blur-sm rounded-xl sm:rounded-2xl p-2 sm:p-3 shadow-2xl max-w-4xl mx-auto border border-green-100/50">
              {[
                { id: 'overview', label: 'Overview', icon: Target },
                { id: 'features', label: 'Core Features', icon: Settings },
                { id: 'benefits', label: 'Benefits', icon: Award },
                { id: 'modules', label: 'LED Modules', icon: Globe }
              ].map((tab) => {
                const TabIcon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`group flex items-center px-3 sm:px-4 lg:px-6 py-2 sm:py-3 lg:py-4 rounded-lg sm:rounded-xl font-bold text-sm sm:text-base lg:text-lg transition-all duration-300 font-['Open_Sans'] m-1 ${
                      activeTab === tab.id
                        ? 'bg-gradient-to-r from-green-600 to-emerald-600 text-white shadow-lg transform scale-105'
                        : 'text-green-700 hover:bg-green-50 hover:text-green-800'
                    }`}
                  >
                    <TabIcon className="w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:scale-110 transition-transform" />
                    {tab.label}
                  </button>
                );
              })}
            </div>

            {/* Tab Content */}
            <div className="bg-white/90 backdrop-blur-sm rounded-2xl sm:rounded-3xl shadow-2xl p-4 sm:p-6 lg:p-8 xl:p-10 border border-green-100/50">
              {activeTab === 'overview' && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-10 items-center">
                  <div className="space-y-4 sm:space-y-6">
                    <div className="space-y-3">
                      <h3 className="text-2xl sm:text-3xl lg:text-4xl font-black text-gray-900 font-['Open_Sans']">
                        Why Choose
                        <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent"> Smart LED Lighting?</span>
                      </h3>
                      <p className="text-base sm:text-lg text-gray-800 leading-relaxed font-bold font-['Open_Sans']">
                        In today's energy-conscious world, smart LED lighting isn't just about reducing costs—it's about
                        <span className="text-green-700 font-black"> sustainability, efficiency, and gaining a competitive edge</span>.
                        Our Smart LED Lighting delivers a comprehensive solution that transforms how you illuminate, manage, and optimize lighting across your entire operations.
                      </p>
                    </div>

                    <div className="space-y-3 sm:space-y-4">
                      <div className="group flex items-start space-x-3 sm:space-x-4 p-3 sm:p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-100 hover:shadow-lg transition-all">
                        <div className="bg-green-600 p-2 rounded-lg group-hover:scale-110 transition-transform flex-shrink-0">
                          <CheckCircle className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                        </div>
                        <div>
                          <h4 className="font-black text-gray-900 text-base sm:text-lg mb-1 font-['Open_Sans']">Energy Efficiency</h4>
                          <p className="text-gray-800 font-bold text-sm sm:text-base font-['Open_Sans']">Achieve 30-50% energy savings with advanced LED technology.</p>
                        </div>
                      </div>

                      <div className="group flex items-start space-x-3 sm:space-x-4 p-3 sm:p-4 bg-gradient-to-r from-emerald-50 to-teal-50 rounded-xl border border-emerald-100 hover:shadow-lg transition-all">
                        <div className="bg-emerald-600 p-2 rounded-lg group-hover:scale-110 transition-transform flex-shrink-0">
                          <CheckCircle className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                        </div>
                        <div>
                          <h4 className="font-black text-gray-900 text-base sm:text-lg mb-1 font-['Open_Sans']">Smart Controls</h4>
                          <p className="text-gray-800 font-bold text-sm sm:text-base font-['Open_Sans']">Automated systems with motion sensors and daylight harvesting.</p>
                        </div>
                      </div>

                      <div className="group flex items-start space-x-3 sm:space-x-4 p-3 sm:p-4 bg-gradient-to-r from-teal-50 to-green-50 rounded-xl border border-teal-100 hover:shadow-lg transition-all">
                        <div className="bg-teal-600 p-2 rounded-lg group-hover:scale-110 transition-transform flex-shrink-0">
                          <CheckCircle className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                        </div>
                        <div>
                          <h4 className="font-black text-gray-900 text-base sm:text-lg mb-1 font-['Open_Sans']">Long Lifespan</h4>
                          <p className="text-gray-800 font-bold text-sm sm:text-base font-['Open_Sans']">LED lifespan up to 50,000 hours reduces maintenance costs.</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="relative">
                    <div className="bg-white/90 backdrop-blur-sm border border-green-100 rounded-3xl p-6 sm:p-8 shadow-2xl hover:shadow-3xl transition-all duration-300">
                      <div className="bg-gradient-to-br from-green-500 to-emerald-600 p-4 rounded-2xl w-16 h-16 sm:w-20 sm:h-20 flex items-center justify-center mb-4 sm:mb-6 shadow-lg">
                        <Lightbulb className="w-8 h-8 sm:w-10 sm:h-10 text-white" />
                      </div>
                      <h3 className="text-xl sm:text-2xl font-black text-gray-900 mb-3 sm:mb-4 font-['Open_Sans']">
                        Smart LED Technology
                      </h3>
                      <p className="text-gray-800 font-bold leading-relaxed text-sm sm:text-base font-['Open_Sans']">
                        Advanced LED lighting systems with intelligent controls for optimal energy efficiency,
                        superior illumination quality, and seamless integration with your existing infrastructure.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'features' && (
                <div className="space-y-6 sm:space-y-8">
                  <div className="text-center mb-6 sm:mb-8">
                    <h3 className="text-2xl sm:text-3xl lg:text-4xl font-black text-gray-900 mb-3 sm:mb-4 font-['Open_Sans']">
                      Core <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">LED Features</span>
                    </h3>
                    <p className="text-base sm:text-lg text-gray-700 max-w-3xl mx-auto font-medium font-['Open_Sans']">
                      Comprehensive smart LED lighting capabilities designed for modern businesses
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                    {coreFeatures.map((feature, index) => (
                      <div key={index} className="group">
                        <div className="flex items-start space-x-3 sm:space-x-4 p-4 sm:p-6 bg-white rounded-xl sm:rounded-2xl border border-green-100 hover:border-green-300 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
                          <div className="bg-green-600 p-2 rounded-lg group-hover:scale-110 transition-transform flex-shrink-0">
                            <CheckCircle className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                          </div>
                          <p className="text-base sm:text-lg text-gray-800 font-bold leading-relaxed group-hover:text-green-700 transition-colors font-['Open_Sans']">
                            {feature}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {activeTab === 'benefits' && (
                <div className="space-y-6 sm:space-y-8">
                  <div className="text-center mb-6 sm:mb-8">
                    <h3 className="text-2xl sm:text-3xl lg:text-4xl font-black text-gray-900 mb-3 sm:mb-4 font-['Open_Sans']">
                      LED Lighting <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">Benefits</span>
                    </h3>
                    <p className="text-base sm:text-lg text-gray-700 max-w-3xl mx-auto font-medium font-['Open_Sans']">
                      Experience transformative advantages with our smart LED lighting solutions
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                    {benefits.map((benefit, index) => {
                      const IconComponent = benefit.icon;
                      const gradients = [
                        'from-green-500 to-emerald-600',
                        'from-emerald-500 to-teal-600',
                        'from-teal-500 to-green-600',
                        'from-green-600 to-emerald-700'
                      ];
                      return (
                        <div key={index} className="group">
                          <div className="relative p-6 sm:p-8 bg-white rounded-xl sm:rounded-2xl border border-green-100 hover:border-green-300 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden">
                            {/* Background Gradient */}
                            <div className={`absolute top-0 left-0 w-full h-2 bg-gradient-to-r ${gradients[index % gradients.length]}`}></div>

                            <div className={`inline-flex items-center justify-center w-16 h-16 rounded-xl bg-gradient-to-br ${gradients[index % gradients.length]} mb-4 group-hover:scale-110 transition-all duration-300 shadow-lg`}>
                              <IconComponent className="w-8 h-8 text-white" />
                            </div>
                            <h4 className="text-xl sm:text-2xl font-black text-gray-900 mb-3 group-hover:text-green-700 transition-colors font-['Open_Sans']">
                              {benefit.title}
                            </h4>
                            <p className="text-gray-800 leading-relaxed font-bold text-sm sm:text-base font-['Open_Sans']">
                              {benefit.description}
                            </p>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {activeTab === 'modules' && (
                <div className="space-y-6 sm:space-y-8">
                  <div className="text-center mb-6 sm:mb-8">
                    <h3 className="text-2xl sm:text-3xl lg:text-4xl font-black text-gray-900 mb-3 sm:mb-4 font-['Open_Sans']">
                      LED <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">Add-on Modules</span>
                    </h3>
                    <p className="text-base sm:text-lg text-gray-700 max-w-3xl mx-auto font-medium font-['Open_Sans']">
                      Extend your LED lighting capabilities with specialized modules for every industry
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                    {addOnModules.map((module, index) => {
                      const IconComponent = module.icon;
                      return (
                        <div key={index} className="group">
                          <div
                            className="bg-white/90 backdrop-blur-sm border border-green-100 rounded-xl sm:rounded-2xl p-6 sm:p-8 hover:shadow-2xl hover:border-green-300 transition-all duration-500 cursor-pointer transform hover:-translate-y-2"
                            onClick={() => setSelectedModule(selectedModule === index ? null : index)}
                          >
                            <div className="flex items-center space-x-3 sm:space-x-4 mb-3 sm:mb-4">
                              <div className="bg-gradient-to-br from-green-500 to-emerald-600 p-3 rounded-xl group-hover:scale-110 transition-transform shadow-lg">
                                <IconComponent className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
                              </div>
                              <div className="flex-1">
                                <h4 className="text-lg sm:text-xl font-black text-gray-900 group-hover:text-green-700 transition-colors font-['Open_Sans']">
                                  {module.title}
                                </h4>
                              </div>
                              <ChevronDown className={`w-5 h-5 text-gray-500 transition-transform duration-300 ${selectedModule === index ? 'rotate-180' : ''}`} />
                            </div>
                            <p className="text-gray-800 font-bold text-sm sm:text-base mb-3 sm:mb-4 font-['Open_Sans']">
                              {module.description}
                            </p>
                            {selectedModule === index && (
                              <div className="space-y-2 pt-3 sm:pt-4 border-t border-green-100">
                                {module.features.map((feature, featureIndex) => (
                                  <div key={featureIndex} className="flex items-center space-x-2">
                                    <CheckCircle className="w-4 h-4 text-green-600 flex-shrink-0" />
                                    <span className="text-gray-700 font-bold text-sm font-['Open_Sans']">{feature}</span>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>

                  <div className="mt-8 text-center">
                    <div className="bg-white/90 backdrop-blur-sm border border-green-100 rounded-2xl p-6 sm:p-8 shadow-lg">
                      <h4 className="text-xl sm:text-2xl font-black text-gray-900 mb-4 font-['Open_Sans']">
                        Perfect for Every Industry
                      </h4>
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                        {industries.map((industry, index) => {
                          const IconComponent = industry.icon;
                          return (
                            <div key={index} className="text-center group">
                              <div className="bg-gradient-to-br from-green-500 to-emerald-600 w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-2 group-hover:scale-110 transition-transform shadow-lg">
                                <IconComponent className="w-6 h-6 text-white" />
                              </div>
                              <p className="text-sm font-bold text-gray-800 group-hover:text-green-700 transition-colors font-['Open_Sans']">
                                {industry.title}
                              </p>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Contact Section - Separate & Distinct */}
        <div className="mt-8 sm:mt-12 py-8 sm:py-12 bg-gradient-to-br from-green-100 via-green-50 to-emerald-50 border-t-4 border-green-200">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            {/* Main Heading */}
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-black text-green-700 mb-4 sm:mb-6 font-['Open_Sans']">
              Need a Custom LED Lighting Solution?
            </h2>

            {/* Description */}
            <p className="text-base sm:text-lg lg:text-xl text-green-600 font-bold max-w-3xl mx-auto leading-relaxed mb-6 sm:mb-8 font-['Open_Sans']">
              Our lighting engineers can design LED systems to your specific requirements with industry-leading
              efficiency and performance. Get in touch today to discuss your lighting needs.
            </p>

            {/* Contact Support Button */}
            <button
              onClick={() => navigate('/contact/sales')}
              className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-bold text-lg rounded-full shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 font-['Open_Sans']"
            >
              <Users className="w-6 h-6 mr-3" />
              Contact Support
            </button>
          </div>
        </div>

        {/* Custom Animations */}
        <style>{`
          @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-15px) rotate(3deg); }
          }
          @keyframes float-delayed {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-12px) rotate(-2deg); }
          }
          .animate-float {
            animation: float 5s ease-in-out infinite;
          }
          .animate-float-delayed {
            animation: float-delayed 6s ease-in-out infinite;
          }
        `}</style>
      </PageLayout>
    </div>
  );
};

export default LightingEnergySaverPage;