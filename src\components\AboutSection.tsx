// import React, { useState, useEffect } from "react";
// import { motion } from "framer-motion";
// import { useInView } from "react-intersection-observer";
// import { Trophy, Users, CheckSquare, Clock } from "lucide-react";

// // Import the Button component
// import { Button } from "@/components/ui/button";

// // Enhanced tri-color gradient button component
// const GradientButton = ({ children, className, variant = "primary", ...props }) => {
//   const getButtonStyle = () => {
//     switch (variant) {
//       case "primary":
//         return "bg-gradient-to-r from-blue-600 via-green-600 to-yellow-500 hover:from-blue-700 hover:via-green-700 hover:to-yellow-600";
//       case "blue":
//         return "bg-gradient-to-r from-blue-600 to-blue-800 hover:from-blue-700 hover:to-blue-900";
//       case "green":
//         return "bg-gradient-to-r from-green-600 to-green-800 hover:from-green-700 hover:to-green-900";
//       case "yellow":
//         return "bg-gradient-to-r from-yellow-500 to-amber-600 hover:from-yellow-600 hover:to-amber-700";
//       case "outline":
//         return "bg-transparent border border-white/30 hover:bg-white/10 text-white";
//       default:
//         return "bg-gradient-to-r from-blue-600 via-green-600 to-yellow-500 hover:from-blue-700 hover:via-green-700 hover:to-yellow-600";
//     }
//   };

//   return (
//     <Button
//       className={`${getButtonStyle()} border-none shadow-lg relative overflow-hidden hover:scale-110 transition-all duration-300 ${className}`}
//       {...props}
//     >
//       <span className="relative z-10">{children}</span>
//       <motion.div
//         className="absolute inset-0 bg-white/10 opacity-0 hover:opacity-20 transition-opacity duration-300"
//         whileHover={{ opacity: 0.2 }}
//         transition={{ duration: 0.3 }}
//       ></motion.div>
//     </Button>
//   );
// };

// // Achievement Cards Component
// const AchievementCards = () => {
//   // Achievement data based on the image
//   const achievements = [
//     {
//       icon: <Trophy className="w-12 h-12" />,
//       title: "1",
//       subtitle: ["MANUFACTURER", "OF SERVO STABILISER"],
//       gradient: "from-yellow-500 to-yellow-400",
//       textGradient: "from-yellow-400 to-green-400"
//     },
//     {
//       icon: <Users className="w-12 h-12" />,
//       title: "100+",
//       subtitle: ["SERVICE", "CENTRES"],
//       gradient: "from-cyan-500 to-cyan-400",
//       textGradient: "from-cyan-400 to-cyan-500"
//     },
//     {
//       icon: <CheckSquare className="w-12 h-12" />,
//       title: "PREFERRED SUPPLIER",
//       subtitle: ["OF LARGE CORPORATES", "& OEM'S"],
//       gradient: "from-cyan-500 to-green-400",
//       textGradient: "from-yellow-400 to-green-400"
//     },
//     {
//       icon: <span className="text-5xl font-bold">CE</span>,
//       title: "",
//       subtitle: ["CE CERTIFIED", "PRODUCTS"],
//       gradient: "from-cyan-500 to-cyan-400",
//       textGradient: "from-cyan-400 to-cyan-500"
//     },
//     {
//       icon: <div className="relative">
//         <div className="text-5xl font-bold">39</div>
//         <Clock className="w-8 h-8 absolute -right-3 -bottom-2 rotate-45" />
//       </div>,
//       title: "",
//       subtitle: ["39 YEARS", "EXPERIENCE"],
//       gradient: "from-yellow-400 to-green-400",
//       textGradient: "from-yellow-400 to-green-400"
//     }
//   ];

//   return (
//     <div className="grid grid-cols-2 gap-4 h-full">
//       {achievements.map((achievement, idx) => (
//         <motion.div
//           key={idx}
//           initial={{ opacity: 0, y: 20 }}
//           whileInView={{ opacity: 1, y: 0 }}
//           transition={{ duration: 0.5, delay: idx * 0.1 }}
//           viewport={{ once: true }}
//           className={`relative bg-gray-900 rounded-xl overflow-hidden flex flex-col items-center justify-center p-4 text-center ${idx === 4 ? "col-span-2" : ""}`}
//         >
//           {/* Background gradient */}
//           <div className={`absolute inset-0 bg-gradient-to-br ${achievement.gradient} opacity-10`}></div>

//           {/* Icon with gradient color */}
//           <div className={`text-transparent bg-clip-text bg-gradient-to-b ${achievement.gradient}`}>
//             {achievement.icon}
//           </div>

//           {/* Main title */}
//           {achievement.title && (
//             <h3 className={`text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-b ${achievement.gradient}`}>
//               {achievement.title}
//             </h3>
//           )}

//           {/* Subtitle with gradient text */}
//           <div className={`text-transparent bg-clip-text bg-gradient-to-b ${achievement.textGradient} leading-tight`}>
//             {achievement.subtitle.map((line, i) => (
//               <p key={i} className="text-lg font-bold">{line}</p>
//             ))}
//           </div>
//         </motion.div>
//       ))}
//     </div>
//   );
// };

// // Main About Section Component
// const AboutSection = () => {
//   const [aboutRef, aboutInView] = useInView({ threshold: 0.2 });

//   // Counters logic for About section
//   const [installations, setInstallations] = useState(0);
//   const [clients, setClients] = useState(0);

//   useEffect(() => {
//     if (aboutInView) {
//       const installationsInterval = setInterval(() => {
//         setInstallations((prev) => {
//           if (prev < 500000) return prev + 5000;
//           clearInterval(installationsInterval);
//           return 500000;
//         });
//       }, 10);
//       const clientsInterval = setInterval(() => {
//         setClients((prev) => {
//           if (prev < 150000) return prev + 2000;
//           clearInterval(clientsInterval);
//           return 150000;
//         });
//       }, 10);
//       return () => {
//         clearInterval(installationsInterval);
//         clearInterval(clientsInterval);
//       };
//     }
//   }, [aboutInView]);

//   return (
//     <section
//       id="about"
//       ref={aboutRef}
//       className="relative py-24 px-6 bg-gradient-to-b from-gray-950 to-black overflow-hidden"
//     >
//       <div className="absolute inset-0 bg-gradient-to-br from-blue-900/5 via-green-900/5 to-yellow-900/5"></div>

//       <div className="container max-w-7xl mx-auto relative z-10">
//         <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
//           <motion.div
//             initial={{ opacity: 0, x: -30 }}
//             animate={aboutInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -30 }}
//             transition={{ duration: 0.6 }}
//           >
//             <span className="inline-block bg-gradient-to-r from-blue-900/80 via-green-900/80 to-yellow-900/80 text-white backdrop-blur-sm px-4 py-1 rounded-full text-sm font-medium mb-4 border border-white/20">
//               Energy Innovation
//             </span>
//             <h2 className="text-5xl md:text-6xl lg:text-7xl font-bold mb-6 text-white">
//               <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-green-400 to-yellow-400">ABOUT US</span>
//             </h2>
//             <p className="text-gray-300 mb-12">
//             Atandra Energy Pvt. Ltd., headquartered in Chennai, drawsupon a rich foundation of more than 39 years of expertise inthe realm of Power & Energy Management.
//             </p>

//             <div className="grid grid-cols-2 gap-8 mb-6 mt-8">
//               <div className="text-center">
//                 <div className="relative group">
//                   <motion.div
//                     className="absolute inset-0 bg-blue-500/30 rounded-full blur-xl opacity-50 group-hover:opacity-70 transition-opacity duration-300"
//                     animate={{
//                       scale: [1, 1.1, 1],
//                       opacity: [0.5, 0.7, 0.5]
//                     }}
//                     transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
//                   ></motion.div>
//                   <h3 className="text-4xl font-bold text-white mb-2 relative">
//                     {installations.toLocaleString()}+
//                   </h3>
//                 </div>
//                 <p className="text-blue-300">Installations of Power Conditioners</p>
//               </div>
//               <div className="text-center">
//                 <div className="relative group">
//                   <motion.div
//                     className="absolute inset-0 bg-green-500/30 rounded-full blur-xl opacity-50 group-hover:opacity-70 transition-opacity duration-300"
//                     animate={{
//                       scale: [1, 1.1, 1],
//                       opacity: [0.5, 0.7, 0.5]
//                     }}
//                     transition={{ duration: 3.5, repeat: Infinity, ease: "easeInOut" }}
//                   ></motion.div>
//                   <h3 className="text-4xl font-bold text-white mb-2 relative">
//                     {clients.toLocaleString()}+
//                   </h3>
//                 </div>
//                 <p className="text-green-300">Installations of Portable and panel load managers </p>
//               </div>
//             </div>

//             <h3 className="text-xl font-bold mb-4 text-white">Sustainability Commitment</h3>
//             <p className="text-gray-300 mb-6">
//             State-of-the-art facilities empower us to address therequirements of Indian industries comprehensively,effectively & efficiently, ensuring they derive maximumbenefits from the power conditioning & energymanagement solutions we provide.
//             </p>

//             <a href="/about/company" className="inline-block">
//               <GradientButton 
//                 className="relative overflow-hidden group text-lg px-8 py-3 font-semibold"
//               >
//                 View Details
//               </GradientButton>
//             </a>
//           </motion.div>

//           {/* Achievement cards based on the uploaded image */}
//           <motion.div
//             initial={{ opacity: 0, x: 30 }}
//             animate={aboutInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 30 }}
//             transition={{ duration: 0.6 }}
//             className="rounded-lg overflow-hidden h-full"
//           >
//             <AchievementCards />
//           </motion.div>
//         </div>
//       </div>
//     </section>
//   );
// };

// export default AboutSection;