import React, { useState } from 'react';
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import PageLayout from "@/components/layout/PageLayout";
import { Button } from "@/components/ui/button";

const InsulationContinuityTesters = () => {
  const [activeTab, setActiveTab] = useState('overview');

  const navItems = [
    { id: 'overview', label: 'Overview' },
    { id: 'installation', label: 'Installation Testers' },
    { id: 'insulation', label: 'Insulation Testers' },
    { id: 'insulation-continuity', label: 'Insulation & Continuity Testers' },
    { id: 'digital', label: 'Digital Insulation Testers' },
    { id: 'earth', label: 'Earth & Resistivity Testers' },
    { id: 'radiometer', label: 'Radiometer' }
  ];

  return (
    <PageLayout
      title="Insulation & Continuity Testers"
      subtitle="Professional testing equipment for electrical systems safety and maintenance."
      category="measure"
    >
      {/* Navigation */}
      <motion.nav 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="border-b mb-8"
      >
        <ul className="flex flex-wrap">
          {navItems.map((item) => (
            <li key={item.id}>
              <button
                onClick={() => setActiveTab(item.id)}
                className={`px-4 py-3 text-sm ${
                  activeTab === item.id
                    ? 'border-b-2 border-primary font-medium'
                    : 'text-muted-foreground'
                }`}
              >
                {item.label}
              </button>
            </li>
          ))}
        </ul>
      </motion.nav>

      {/* Main Content */}
      <div className="flex flex-col">
        {/* Header */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
          className="flex flex-col md:flex-row mb-8"
        >
          <div className="w-full md:w-1/3 p-4 flex justify-center items-center">
            <img
              src="/api/placeholder/400/320"
              alt="Insulation and Continuity Tester"
              className="max-w-full h-auto rounded-lg shadow-sm"
            />
          </div>
          <div className="w-full md:w-2/3 p-4">
            <h1 className="text-4xl font-bold text-gray-700 mb-4">
              INSULATION &<br />
              <span className="text-gray-600">CONTINUITY</span> <span className="text-primary">TESTERS</span>
            </h1>
            <ul className="space-y-3">
              <li className="flex items-start">
                <div className="flex-shrink-0 w-6 h-6 bg-primary rounded-full flex items-center justify-center mr-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span>Models -C.A 6522, C.A 6524, C.A 6526, C.A 6532, C.A 6534, C.A 6536</span>
              </li>
              <li className="flex items-start">
                <div className="flex-shrink-0 w-6 h-6 bg-primary rounded-full flex items-center justify-center mr-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span>1 kV Insulation and Continuity Tester</span>
              </li>
              <li className="flex items-start">
                <div className="flex-shrink-0 w-6 h-6 bg-primary rounded-full flex items-center justify-center mr-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span>Industrial maintenance</span>
              </li>
              <li className="flex items-start">
                <div className="flex-shrink-0 w-6 h-6 bg-primary rounded-full flex items-center justify-center mr-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span>40 GΩ / Test voltage 250-500-1,000 V</span>
              </li>
              <li className="flex items-start">
                <div className="flex-shrink-0 w-6 h-6 bg-primary rounded-full flex items-center justify-center mr-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span>Manual / Lock / Duration modes</span>
              </li>
              <li className="flex items-start">
                <div className="flex-shrink-0 w-6 h-6 bg-primary rounded-full flex items-center justify-center mr-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span>Continuity, Voltage</span>
              </li>
            </ul>
            <div className="mt-6 flex space-x-4">
              <Button asChild>
                <Link to="/contact/sales">ENQUIRE</Link>
              </Button>
              <button className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-primary text-primary hover:bg-primary/10 h-10 py-2 px-4">
                BROCHURE
              </button>
            </div>
          </div>
        </motion.div>

        {/* Ergonomics Section */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="flex flex-col md:flex-row bg-muted/30 p-6 mb-8 rounded-lg"
        >
          <div className="w-full md:w-1/2 p-4">
            <h2 className="text-2xl font-bold mb-4">Ergonomics</h2>
            <p className="mb-4 text-muted-foreground">
              The new C.A 652x & C.A 653x insulation testers are compact, lightweight and simple
              to use, offering greater comfort for users:
            </p>
            <ul className="space-y-4">
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span>
                  Casing designed with 600V CAT IV safety and IP54 / IK04 protection for a
                  comfortable grip in total safety with easy access to the TEST button, even when
                  the user is wearing safety gloves
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span>
                  Magnetic stand for fixing the product on a metal surface so that users have their
                  hands free when measuring and reading the result
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span>
                  Wide backlit screen with double digital display and logarithmic bargraph,
                  secondary display showing the actual test voltage, the test current and the timer
                  value, in addition to the value measured
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span>
                  Remote control with torch for error-free identification of the points of
                  measurement, even in environments with poor lighting
                </span>
              </li>
            </ul>
          </div>
          <div className="w-full md:w-1/2 p-4 flex justify-center items-center">
            <img
              src="/api/placeholder/400/320"
              alt="Insulation tester in use"
              className="max-w-full h-auto rounded-lg shadow-sm"
            />
          </div>
        </motion.div>

        {/* Test Features Section */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="mb-8"
        >
          <div className="mb-6">
            <h2 className="text-2xl font-bold mb-4">Insulation Tests</h2>
            <p className="mb-3 text-muted-foreground">
              This new range is equipped with various insulation test voltages ranging from 10 V to 1 kV. The dynamic range for insulation measurement extends up to 200 GΩ,
              remaining compliant with the IEC 61557 standard up to 2 GΩ.
            </p>
            <p className="text-muted-foreground">
              In addition to the manual mode, there are also new insulation test modes such as Lock mode, Timer mode and calculation of the PI & DAR ratios.
            </p>
          </div>

          <div className="mb-6">
            <h2 className="text-2xl font-bold mb-4">Continuity Tests</h2>
            <p className="text-muted-foreground">
              The continuity test function, which complies with the IEC 61557 standard (200 mA current), is enhanced on certain models with a 20 mA test current, with the
              advantage of prolonging battery life in normal use. This function is equipped with active/fuseless protection if a voltage is present.
            </p>
          </div>

          <div className="mb-6">
            <h2 className="text-2xl font-bold mb-4">Measurement Adaptability</h2>
            <p className="mb-3 text-muted-foreground">
              On most of the models in the C.A 652X & C.A 653x range, you can activate an alarm threshold, choosing between 2 fixed thresholds and 1 customizable threshold for
              each measurement.
            </p>
            <p className="text-muted-foreground">
              Furthermore, the ΔREL function can be used to display the measurements which differ from a reference measurement. The display shows the value of the variation and
              the percentage in relation to the reference.
            </p>
          </div>

          <div className="mb-6">
            <h2 className="text-2xl font-bold mb-4">Other Functions</h2>
            <p className="mb-3 text-muted-foreground">
              DMM functions are available with measurement of voltage up to 700 V, frequency up to 800 Hz, resistance up to 999 kΩ and capacitance up to 10 μF.
            </p>
          </div>

          <div className="mb-6">
            <h2 className="text-2xl font-bold mb-4">Easy Operation</h2>
            <p className="mb-3 text-muted-foreground">
              Analysis of the measurements is facilitated by storage of 300 measurements on the instrument. The memory capacity is extended to 1,300 locations for the versions
              with Bluetooth® communication. The Megohmmeter Transfer (MEG) software module, delivered as standard with the communicating versions, allows remote test
              activation with real-time graphical display and transfer of the stored measurements onto a PC. The values are then displayed by type of measurement.
            </p>
            <p className="text-muted-foreground">
              The DataView® software suite (option) generates customized measurement reports which may include the location of the site and the contact details of the operator.
            </p>
          </div>
        </motion.div>

        {/* Specifications Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <h2 className="text-3xl font-bold mb-6">Specifications</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 bg-background border border-border rounded-lg p-6">
            <div>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>INSULATION: 250-500-1000 V test voltages</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Measurement range: 40 GΩ</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Test mode: Manual, Lock and Timer</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Continuity: 200 mA measurement current</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Voltage measurement: 700 V</span>
                </li>
              </ul>
            </div>
            <div>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Double display + logarithmic bargraph</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Powered by 6 x LR6 / AA batteries</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Dimensions: 211 x 108 x 60 mm</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Weight: 850 g</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>IP54, safety category: 600 V CAT IV</span>
                </li>
              </ul>
            </div>
          </div>
        </motion.div>
        
        {/* Related Products Section */}
        <motion.section 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="mt-16 mb-16 pt-8 border-t border-border"
        >
          <h2 className="text-2xl font-bold mb-6 text-center">Related Products</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[
              {
                id: "digital-insulation",
                name: "Digital Insulation Testers",
                description: "High precision testers for advanced insulation resistance measurement.",
                image: "/api/placeholder/300/200",
                link: "/measure/safety-instruments/digital-insulation-testers"
              },
              {
                id: "earth-testers",
                name: "Earth & Resistivity Testers",
                description: "Essential tools for ground safety testing in electrical installations.",
                image: "/api/placeholder/300/200",
                link: "/measure/safety-instruments/earth-resistivity-testers"
              },
              {
                id: "installation-testers",
                name: "Installation Testers",
                description: "Complete solution for electrical installation testing and compliance.",
                image: "/api/placeholder/300/200",
                link: "/measure/safety-instruments/installation-testers"
              }
            ].map((product) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="bg-background border border-border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow"
              >
                <div className="p-4 flex flex-col h-full">
                  <div className="mb-4">
                    <img
                      src={product.image}
                      alt={product.name}
                      className="w-full h-48 object-contain"
                    />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">{product.name}</h3>
                  <p className="text-muted-foreground text-sm mb-4 flex-grow">{product.description}</p>
                  <Link
                    to={product.link}
                    className="text-primary hover:text-primary/80 text-sm font-medium"
                  >
                    View Products
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Product Help Section */}
        <motion.section 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="mt-16 bg-muted/30 p-8 rounded-lg"
        >
          <h2 className="text-2xl font-bold mb-6 text-center">Need Help Selecting the Right Product?</h2>
          <p className="text-center text-muted-foreground mb-8">
            Our team of experts can help you choose the right solution for your specific needs.
          </p>
          <div className="flex justify-center">
            <Link 
              to="/contact" 
              className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-primary text-primary-foreground hover:bg-primary/90 h-10 py-2 px-4"
            >
              Contact Our Experts
            </Link>
          </div>
        </motion.section>
      </div>
    </PageLayout>
  );
};

export default InsulationContinuityTesters;