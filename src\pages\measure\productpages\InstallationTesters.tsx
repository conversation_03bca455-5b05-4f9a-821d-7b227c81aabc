import React, { useState } from 'react';
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import PageLayout from "@/components/layout/PageLayout";

const InstallationTesters = () => {
  const [activeTab, setActiveTab] = useState('installation');
  const [activeModel, setActiveModel] = useState('6133');

  const navItems = [
    { id: 'overview', label: 'Overview' },
    { id: 'installation', label: 'Installation Testers' },
    { id: 'insulation', label: 'Insulation Testers' },
    { id: 'insulation-continuity', label: 'Insulation & Continuity Testers' },
    { id: 'digital', label: 'Digital Insulation Testers' },
    { id: 'earth', label: 'Earth & Resistivity Testers' },
    { id: 'radiometer', label: 'Radiometer' }
  ];

  const models = [
    {
      id: '6133',
      name: 'C.A 6133 / C.A 6131',
      image: '/api/placeholder/400/320',
      features: [
        'Earth measurement by stake and loop methods',
        'Continuity measurement at 0.2A',
        'Insulation testing',
        'RCD testing: Current and trip time',
        'Automatic test sequences',
        'Storage of test results',
        'Android application for report generation',
        'Phase rotation: 45 to 550V; 45 to 65 Hz',
        'Power supply by mains-rechargeable batteries, USB socket or vehicle cigarette lighter',
        'Safety standard CAT III 600V'
      ],
      specifications: [
        { parameter: 'Models', ca6131: 'C.A 6131', ca6133: 'C.A 6133' },
        { parameter: 'Continuity', ca6131: '0.00 to 9.99 Ω', ca6133: '0.00 to 9.99 Ω' },
        { parameter: 'Resistance', ca6131: '1 to 9999 Ω', ca6133: '1 to 9999 Ω' },
        { parameter: 'Insulation', ca6131: '250 V / 500 V', ca6133: '250 V/500 V/ 1000 V' },
        { parameter: 'Earth res- 3P', ca6131: 'NA', ca6133: '0.50 to 2000 Ω' },
        { parameter: 'Frequency', ca6131: 'NA', ca6133: '30.0 to 999.9 Hz' },
        { parameter: 'Current', ca6131: 'Via clamp with Voltage output', ca6133: 'Via MN73A clamp' },
        { parameter: 'Voltage', ca6131: '0 - 800V', ca6133: '0 - 800V' },
        { parameter: 'RCD test (V)', ca6131: '90 to 450 V', ca6133: '90 to 450 V' },
        { parameter: 'Trip time', ca6131: '0.5 x I∆N ; 1 x I∆N ; 5 x I∆N / 5.0 to 300 ms', ca6133: '0.5 x I∆N ; 1 x I∆N ; 5 x I∆N / 5.0 to 300 ms' }
      ]
    },
    {
      id: '6165',
      name: 'C.A 6165',
      image: '/api/placeholder/400/320',
      features: [
        '200 mA, 4 A, 10 A and 25 A continuity',
        'TRMS leakage and load currents',
        '50 V / 100 V / 250 V / 500 V and 1,000 V insulation resistance',
        'Power values (active, apparent & reactive) & THD',
        'Touch leakage current',
        'Earth leakage current (PE)',
        'Storage of the tests on memory card up to 32 GB',
        'RS232, USB, Ethernet*, Bluetooth, inputs',
        'External and internal discharge time up to 10 s / 500 V peak',
        'Safety standard CAT II 300V'
      ],
      specifications: [
        { parameter: 'High voltage', value: 'AC: 0 V to 1,999 V\nDC: 0 V to 1,999 V' },
        { parameter: 'Continuity', value: '0 to 999 Ω' },
        { parameter: 'Resistance', value: '0 to 199.9 MΩ' },
        { parameter: 'Insulation', value: '50 V / 100 V 250 V / 500 V / 1,000 V' },
        { parameter: 'Leakage current', value: '0.00 to 19.99 mA' },
        { parameter: 'KW/KVA/KVAr', value: '0 to 3.70 kW / 0.01 W to 10 W\n0 to 3.70 kVA / 0.01 VA to 10 VA\n0 to 3.70 kVAr / 0.01 VAr to 10 VAr' }
      ]
    }
  ];

  const activeModelData = models.find(model => model.id === activeModel);

  return (
    <PageLayout
      title="Installation Testers"
      subtitle="Comprehensive testing devices for electrical installations to ensure compliance with safety standards."
      category="measure"
    >
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        {/* Navigation */}
        <nav className="border-b mb-6">
          <ul className="flex flex-wrap">
            {navItems.map((item) => (
              <li key={item.id}>
                <button
                  onClick={() => setActiveTab(item.id)}
                  className={`px-4 py-3 text-sm ${
                    activeTab === item.id
                      ? 'border-b-2 border-primary font-medium'
                      : 'text-muted-foreground'
                  }`}
                >
                  {item.label}
                </button>
              </li>
            ))}
          </ul>
        </nav>

        {/* Model Tabs */}
        <motion.div 
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="border-b mb-6"
        >
          <ul className="flex flex-wrap">
            {models.map((model) => (
              <li key={model.id}>
                <button
                  onClick={() => setActiveModel(model.id)}
                  className={`px-4 py-2 text-sm ${
                    activeModel === model.id
                      ? 'bg-primary/10 font-medium text-primary'
                      : 'text-muted-foreground'
                  }`}
                >
                  {model.name}
                </button>
              </li>
            ))}
          </ul>
        </motion.div>

        {/* Main Content */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="flex flex-col"
        >
          {/* Header */}
          <div className="flex flex-col md:flex-row mb-8 bg-background border border-border rounded-lg p-6 shadow-sm">
            <div className="w-full md:w-2/5 p-4 flex justify-center items-center">
              <img
                src={activeModelData.image}
                alt={`${activeModelData.name} Installation Tester`}
                className="max-w-full h-auto"
              />
            </div>
            <div className="w-full md:w-3/5 p-4">
              <h1 className="text-4xl font-bold text-foreground mb-4">
                INSTALLATION<br />
                <span className="text-primary">TESTERS</span>
              </h1>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 bg-primary rounded-full flex items-center justify-center mr-2 mt-1">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span>Models - {activeModelData.name}</span>
                </li>
                {activeModelData.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 bg-primary rounded-full flex items-center justify-center mr-2 mt-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
              <div className="mt-6 flex space-x-4">
                <button className="bg-white border border-primary hover:bg-primary/5 text-primary font-bold py-2 px-4 rounded">
                  ENQUIRE
                </button>
                <button className="bg-white border border-primary hover:bg-primary/5 text-primary font-bold py-2 px-4 rounded">
                  BROCHURE
                </button>
              </div>
            </div>
          </div>

          {/* Specifications Table */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="mb-8"
          >
            <h2 className="text-2xl font-bold mb-4">Specifications</h2>
            <div className="overflow-x-auto bg-background border border-border rounded-lg shadow-sm">
              {activeModel === '6133' ? (
                <table className="min-w-full bg-background">
                  <thead>
                    <tr className="bg-primary text-primary-foreground">
                      <th className="py-2 px-4 text-left border-b">PARAMETER</th>
                      <th className="py-2 px-4 text-left border-b">RANGE</th>
                      <th className="py-2 px-4 text-left border-b"></th>
                    </tr>
                  </thead>
                  <tbody>
                    {activeModelData.specifications.map((spec, index) => (
                      <tr key={index} className={index % 2 === 0 ? 'bg-muted/30' : 'bg-background'}>
                        <td className="py-2 px-4 border-b font-medium text-foreground">{spec.parameter}</td>
                        <td className="py-2 px-4 border-b">{spec.ca6131}</td>
                        <td className="py-2 px-4 border-b">{spec.ca6133}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              ) : (
                <table className="min-w-full bg-background">
                  <thead>
                    <tr className="bg-primary text-primary-foreground">
                      <th className="py-2 px-4 text-left border-b">PARAMETER</th>
                      <th className="py-2 px-4 text-left border-b">C.A 6165</th>
                    </tr>
                  </thead>
                  <tbody>
                    {activeModelData.specifications.map((spec, index) => (
                      <tr key={index} className={index % 2 === 0 ? 'bg-muted/30' : 'bg-background'}>
                        <td className="py-2 px-4 border-b font-medium text-foreground">{spec.parameter}</td>
                        <td className="py-2 px-4 border-b whitespace-pre-line">{spec.value}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          </motion.div>

          {/* Applications Section */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="mb-8"
          >
            <h2 className="text-2xl font-bold mb-4">Applications</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-muted/30 p-4 rounded-lg border border-border">
                <h3 className="font-semibold mb-2">Electrical Installations</h3>
                <p className="text-muted-foreground">Test and verify the safety and compliance of electrical installations in buildings, industrial facilities, and infrastructure.</p>
              </div>
              <div className="bg-muted/30 p-4 rounded-lg border border-border">
                <h3 className="font-semibold mb-2">Maintenance</h3>
                <p className="text-muted-foreground">Regular checking and maintenance of existing electrical systems to ensure they remain safe and up to standard.</p>
              </div>
              <div className="bg-muted/30 p-4 rounded-lg border border-border">
                <h3 className="font-semibold mb-2">Safety Verification</h3>
                <p className="text-muted-foreground">Confirm compliance with electrical safety standards and regulations before commissioning or after modifications.</p>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </motion.section>

      {/* Product Selection Help Section */}
      <section className="mt-16 bg-muted/30 p-8 rounded-lg border border-border">
        <h2 className="text-2xl font-bold mb-6 text-center">Need Help Selecting the Right Product?</h2>
        <p className="text-center text-muted-foreground mb-8">
          Our team of experts can help you choose the right solution for your specific needs.
        </p>
        <div className="flex justify-center">
          <Link 
            to="/contact" 
            className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-primary text-primary-foreground hover:bg-primary/90 h-10 py-2 px-4"
          >
            Contact Our Experts
          </Link>
        </div>
      </section>
    </PageLayout>
  );
};

export default InstallationTesters;