import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useNavigate, useLocation } from "react-router-dom";
import {
  ArrowRight,
  Check,
  ChevronRight,
  FileText,
  Gauge,
  Zap,
  BarChart,
  Shield,
  Download,
  ExternalLink
} from "lucide-react";
import PageLayout from "@/components/layout/PageLayout";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

// PDF URL for brochure
const PDF_URL = "/T&M April 2025.pdf";

// Modern Background Component
const ModernBackground = () => {
  return (
    <div className="fixed inset-0 pointer-events-none z-[-1] overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Abstract shapes */}
      <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-yellow-100 rounded-bl-full opacity-30"></div>
      <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-yellow-200 rounded-tr-full opacity-20"></div>

      {/* Subtle grid pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
    </div>
  );
};

// No PDF Viewer Component - Using direct link instead

// Enhanced Hero Section Component
const HeroSection = ({ onRequestDemo, onViewBrochure }) => {
  return (
    <div className="relative py-8 md:py-12 overflow-hidden font-['Open_Sans']">
      {/* Hero Background Elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 right-0 w-3/4 h-full bg-yellow-50 rounded-bl-[100px] transform -skew-x-12"></div>
        <div className="absolute bottom-20 left-0 w-64 h-64 bg-yellow-400 rounded-full opacity-10"></div>
      </div>

      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6 items-center">
          {/* Text Content */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-3 md:space-y-4 order-2 lg:order-1"
          >
            <div className="inline-block bg-yellow-400 py-1 px-3 rounded-full mb-2">
              <span className="text-sm font-semibold text-black font-['Open_Sans']">KRYKARD Precision Instruments</span>
            </div>
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold text-black leading-tight text-center lg:text-left font-['Open_Sans']">
              OSCILLO<span className="text-yellow-500">SCOPES</span>
            </h1>

            <p className="text-base md:text-lg text-black leading-relaxed font-semibold text-center lg:text-justify font-['Open_Sans']">
              Professional-grade instruments for precision measurement of electrical signals with multiple operating modes.
            </p>

            <div className="pt-3 flex flex-wrap gap-2 md:gap-3 justify-center lg:justify-start">
              <Button
                className="px-4 md:px-6 py-2 md:py-3 bg-yellow-400 hover:bg-yellow-500 text-black font-semibold rounded-lg shadow-md transition duration-300 flex items-center space-x-2 text-sm md:text-base font-['Open_Sans']"
                onClick={onRequestDemo}
              >
                <span>Request Demo</span>
                <ArrowRight className="ml-1 md:ml-2 h-4 w-4 md:h-5 md:w-5" />
              </Button>
              <Button
                className="px-4 md:px-6 py-2 md:py-3 bg-white border-2 border-yellow-400 text-black font-semibold rounded-lg shadow-sm transition duration-300 hover:bg-gray-50 flex items-center space-x-2 text-sm md:text-base font-['Open_Sans']"
                onClick={onViewBrochure}
              >
                <span>View Brochure</span>
                <FileText className="ml-1 md:ml-2 h-4 w-4 md:h-5 md:w-5" />
              </Button>
            </div>
          </motion.div>

          {/* Product Image */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative order-1 lg:order-2 mb-4 lg:mb-0"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-200 to-yellow-50 rounded-full opacity-30 blur-2xl transform scale-110"></div>
            <motion.div
              animate={{
                y: [0, -15, 0],
              }}
              transition={{
                repeat: Infinity,
                duration: 3,
                ease: "easeInOut"
              }}
              className="relative z-10 flex justify-center"
            >
              <img
                src="/oscillosacopes/images-removebg-preview (1).png"
                alt="Krykard Oscilloscope"
                className="max-h-[200px] md:max-h-[250px] lg:max-h-[300px] w-auto object-contain drop-shadow-2xl"
              />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

// Animated Product Card Component
const ProductCard = ({
  title,
  modelInfo,
  image,
  bandwidth,
  displayInfo,
  features,
  colors = {
    primary: 'yellow-400',
    secondary: 'yellow-50'
  },
  onViewDetailsClick
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="group h-full font-['Open_Sans']"
    >
      <div className={`h-full rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 bg-white border border-gray-100`}>
        {/* Product Image with Colored Background */}
        <div className={`relative p-3 md:p-4 flex justify-center items-center bg-${colors.secondary} h-40 md:h-48 overflow-hidden group-hover:bg-opacity-80 transition-all duration-700`}>
          <motion.img
            src={image}
            alt={title}
            className="h-32 md:h-40 object-contain z-10 drop-shadow-xl"
            whileHover={{ rotate: [-1, 1, -1], transition: { repeat: Infinity, duration: 2 } }}
          />
          <div className={`absolute top-2 left-2 bg-${colors.primary} text-white text-xs font-bold py-1 px-2 rounded-full font-['Open_Sans']`}>
            {modelInfo}
          </div>
        </div>

        {/* Product Content */}
        <div className="p-3 md:p-4 space-y-2 md:space-y-3">
          <h3 className="text-base md:text-lg font-bold text-black group-hover:text-yellow-600 transition-colors duration-300 text-center font-['Open_Sans']">
            {title}
          </h3>

          {/* Key Features */}
          <div className="space-y-1">
            {features.slice(0, 3).map((feature, idx) => (
              <div key={idx} className="flex items-start">
                <Check className="h-3 w-3 md:h-4 md:w-4 text-yellow-400 mt-1 mr-2 flex-shrink-0" />
                <span className="text-black text-xs md:text-sm font-semibold font-['Open_Sans']">{feature}</span>
              </div>
            ))}
          </div>

          {/* Specs Badge */}
          <div className="flex flex-wrap gap-1 md:gap-2 pt-1">
            <span className="inline-block bg-gray-100 rounded-full px-2 py-0.5 text-xs font-semibold text-gray-700 font-['Open_Sans']">
              {bandwidth}
            </span>
            <span className="inline-block bg-gray-100 rounded-full px-2 py-0.5 text-xs font-semibold text-gray-700 font-['Open_Sans']">
              {displayInfo}
            </span>
          </div>

          {/* View Details Button */}
          <Button
            onClick={onViewDetailsClick}
            className={`w-full mt-2 py-2 md:py-3 px-3 bg-${colors.primary} hover:bg-yellow-500 text-center font-semibold text-black rounded-lg transition-all duration-300 transform group-hover:-translate-y-1 flex items-center justify-center text-sm md:text-base font-['Open_Sans']`}
          >
            <span>View Details</span>
            <ChevronRight className="ml-1 h-3 w-3 md:h-4 md:w-4" />
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

// Feature Highlight Component
const FeatureHighlight = ({ icon, title, description }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
      whileHover={{ y: -8, boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)" }}
      className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 p-4 h-full border-b-4 border-yellow-400 text-center md:text-left font-['Open_Sans']"
    >
      <div className="flex flex-col h-full">
        <div className="bg-gradient-to-br from-yellow-400 to-yellow-300 w-12 h-12 md:w-16 md:h-16 rounded-lg flex items-center justify-center mb-3 shadow-md mx-auto md:mx-0">
          {icon}
        </div>
        <h3 className="text-base md:text-lg lg:text-xl font-bold text-black mb-2 font-['Open_Sans']">{title}</h3>
        <p className="text-black font-semibold text-sm md:text-base text-center md:text-justify flex-grow font-['Open_Sans']">{description}</p>
      </div>
    </motion.div>
  );
};

// SpecItem component for product details
const SpecItem = ({ icon, text }) => (
  <div className="flex items-center space-x-3 py-3 border-b border-yellow-100">
    <div className="bg-yellow-100 p-2 rounded-lg">
      {icon}
    </div>
    <span className="text-gray-800 font-medium">{text}</span>
  </div>
);

// Combined Tab Component for Features and Measurements
const ProductTabContent = ({ activeProductType, activeTab }) => {
  // Features data based on product type
  const features = {
    handheld: [
      "Bandwidth & Channels: 20 MHz & 2 isolated channels (OX 5022), 40 MHz & 2 isolated channels (OX 5042)",
      "Input impedance: 1 MΩ ±0.5%, approximately 17 pF",
      "Storage: 2 MB for file storage",
      "2,500 real acquisition points on screen",
      "Multimeter Mode: 2 channels, 8,000-count display + min/max bargraph",
      "Graphical recording: 2,700 measurements (5 min to 1 month)",
      "Resistance & capacitance: 80 Ω to 32 MΩ & 5 nF to 5 mF",
      "Harmonic Analyzer Mode: 2 channels, 31 orders, frequency of fundamental from 40 to 450 Hz",
      "Screen shot: Up to 100 files"
    ],
    portable: [
      "Bandwidth: 60 MHz (OX 9062), 100 MHz (OX 9102 & OX 9104), 300 MHz (OX 9304)",
      "Channels: 2 isolated channels (OX 9062 & OX 9102), 4 isolated channels (OX 9104 & OX 9304)",
      "Input impedance: 1 MΩ ± 0.5%, approximately 12 pF",
      "20 automatic measurements per channel",
      "Memory: 2GB",
      "Multimeter Mode: 2 or 4 channels – 8,000 counts min/max/frequency/relative – TRMS",
      "Resistance & capacitance: 80 Ω to 32 MΩ & 5 nF to 5 mF",
      "Harmonic Analyzer Mode: Up to 63 orders, fundamental frequency 40 to 450 Hz",
      "Logger Mode: Duration: 20,000s – Interval: 0.2s – Files: 100,000 measurements"
    ]
  };

  // Measurements data based on product type
  const measurements = {
    handheld: [
      { label: "Display Type", value: "3.5\" colour TFT - Resolution 320 x 240 with LED backlighting" },
      { label: "Bandwidth", value: "OX 5022: 20 MHz\nOX 5042: 40 MHz" },
      { label: "Channels", value: "2 isolated channels" },
      { label: "AC/DC Voltages", value: "600 mV to 600 VRMS, 800 mV to 800 VDC" },
      { label: "Resistance Range", value: "80 Ω to 32 MΩ" },
      { label: "Capacitance Range", value: "5 nF to 5 mF" }
    ],
    portable: [
      { label: "Display Type", value: "7\" WVGA colour TFT LCD touch screen, 800 x 480 with LED backlighting" },
      { label: "Bandwidth", value: "OX 9062: 60 MHz\nOX 9102/9104: 100 MHz\nOX 9304: 300 MHz" },
      { label: "Channels", value: "OX 9062/9102: 2 isolated channels\nOX 9104/9304: 4 isolated channels" },
      { label: "AC/DC Voltages", value: "600 mV to 600 VRMS, 800 mV to 800 VDC" },
      { label: "Resistance Range", value: "80 Ω to 32 MΩ" },
      { label: "Capacitance Range", value: "5 nF to 5 mF" }
    ]
  };

  if (activeTab === "features") {
    return (
      <div className="bg-white rounded-2xl shadow-lg overflow-hidden font-['Open_Sans']">
        <div className="bg-gradient-to-r from-yellow-400 to-yellow-400 p-3">
          <h3 className="text-xl md:text-2xl font-bold text-center text-white">Salient Features</h3>
        </div>
        <div className="p-2 sm:p-4">
          <div className="space-y-1">
            {features[activeProductType].map((feature, idx) => (
              <motion.div
                key={idx}
                initial={{ opacity: 0, x: -10 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: idx * 0.05 }}
                viewport={{ once: true }}
                className="flex items-start p-2 hover:bg-yellow-50 rounded-lg transition-colors duration-300"
              >
                <div className="flex-shrink-0 w-5 h-5 rounded-full bg-yellow-100 text-yellow-600 flex items-center justify-center mr-2 mt-0.5">
                  <Check className="h-3 w-3 md:h-4 md:w-4" />
                </div>
                <span className="text-gray-900 text-sm md:text-base font-bold font-['Open_Sans']">{feature}</span>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    );
  } else {
    return (
      <div className="bg-white rounded-2xl shadow-lg overflow-hidden font-['Open_Sans']">
        <div className="bg-gradient-to-r from-yellow-400 to-yellow-400 p-3">
          <h3 className="text-xl md:text-2xl font-bold text-center text-white">Measurements</h3>
        </div>
        <div className="p-2 sm:p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4">
            {measurements[activeProductType].map((item, idx) => (
              <motion.div
                key={idx}
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: idx * 0.05 }}
                viewport={{ once: true }}
                className="bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow duration-300 border border-yellow-100"
              >
                <h4 className="font-bold text-gray-900 mb-2 border-b border-yellow-200 pb-2 text-sm md:text-base font-['Open_Sans']">{item.label}</h4>
                <div className="text-gray-900 text-xs md:text-sm font-bold font-['Open_Sans']">
                  {item.value.split('\n').map((line, i) => (
                    <div key={i} className="mb-1 break-words">{line}</div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    );
  }
};

// Contact Section Component
const ContactSection = ({ onContactClick }) => {
  return (
    <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 py-6 px-4 rounded-2xl shadow-lg font-['Open_Sans']">
      <div className="max-w-full mx-auto text-center">
        <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-black mb-3 font-['Open_Sans']">Need More Information?</h2>
        <p className="text-black mb-6 max-w-4xl mx-auto font-semibold text-sm md:text-base lg:text-lg text-center font-['Open_Sans']">
          Our team of experts is ready to help you choose the best oscilloscope for your measurement needs.
          Get in touch for product specifications, custom solutions, and pricing details.
        </p>
        <Button
          className="inline-flex items-center px-6 md:px-8 py-3 md:py-4 bg-yellow-400 hover:bg-yellow-500 text-black font-semibold rounded-lg shadow-md transition duration-300 transform hover:-translate-y-1 text-sm md:text-base"
          onClick={onContactClick}
        >
          Contact Our Experts
          <ArrowRight className="ml-2 h-4 md:h-5 w-4 md:w-5" />
        </Button>
      </div>
    </div>
  );
};

// Comparison Table Component
const ComparisonTable = ({ productType }) => {
  const models = {
    handheld: ['OX 5022', 'OX 5042'],
    portable: ['OX 9062', 'OX 9102', 'OX 9104', 'OX 9304']
  };

  const features = {
    handheld: [
      { name: 'Bandwidth', values: ['20 MHz', '40 MHz'] },
      { name: 'Display', values: ['3.5" TFT', '3.5" TFT'] },
      { name: 'Channels', values: ['2 isolated', '2 isolated'] },
      { name: 'Acquisition Points', values: ['2,500', '2,500'] },
      { name: 'Storage', values: ['2 MB', '2 MB'] },
      { name: 'Harmonics', values: ['31 orders', '31 orders'] }
    ],
    portable: [
      { name: 'Bandwidth', values: ['60 MHz', '100 MHz', '100 MHz', '300 MHz'] },
      { name: 'Display', values: ['7" Touch', '7" Touch', '7" Touch', '7" Touch'] },
      { name: 'Channels', values: ['2 isolated', '2 isolated', '4 isolated', '4 isolated'] },
      { name: 'Acquisition Points', values: ['2,500', '2,500', '2,500', '2,500'] },
      { name: 'Memory', values: ['2 GB', '2 GB', '2 GB', '2 GB'] },
      { name: 'Harmonics', values: ['63 orders', '63 orders', '63 orders', '63 orders'] },
      { name: 'Logger Mode', values: ['Yes', 'Yes', 'Yes', 'Yes'] }
    ]
  };

  return (
    <div className="bg-white rounded-2xl shadow-lg overflow-hidden mt-6 font-['Open_Sans']">
      <div className="bg-gradient-to-r from-yellow-400 to-yellow-400 p-3">
        <h3 className="text-xl md:text-2xl font-bold text-center text-white">Model Comparison</h3>
      </div>
      <div className="p-2 sm:p-4 overflow-x-auto -mx-4 sm:mx-0">
        <div className="inline-block min-w-full align-middle">
          <table className="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th className="px-3 sm:px-6 py-2 sm:py-3 bg-yellow-50 text-left text-xs font-bold text-gray-500 uppercase tracking-wider rounded-tl-lg font-['Open_Sans']">Feature</th>
                {models[productType].map((model, idx) => (
                  <th key={idx} className={`px-3 sm:px-6 py-2 sm:py-3 bg-yellow-50 text-center text-xs font-bold text-gray-500 uppercase tracking-wider font-['Open_Sans'] ${idx === models[productType].length - 1 ? 'rounded-tr-lg' : ''}`}>
                    {model}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {features[productType].map((feature, idx) => (
                <motion.tr
                  key={idx}
                  initial={{ opacity: 0, y: 5 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: idx * 0.05 }}
                  viewport={{ once: true }}
                  className={idx % 2 === 0 ? 'bg-white hover:bg-yellow-50 transition-colors duration-200' : 'bg-gray-50 hover:bg-yellow-50 transition-colors duration-200'}
                >
                  <td className="px-3 sm:px-6 py-2 sm:py-4 text-xs sm:text-sm font-bold text-gray-900 font-['Open_Sans']">{feature.name}</td>
                  {feature.values.map((value, i) => (
                    <td key={i} className="px-3 sm:px-6 py-2 sm:py-4 text-xs sm:text-sm text-gray-900 text-center font-bold font-['Open_Sans']">
                      {value}
                    </td>
                  ))}
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

// Applications Section Component
const ApplicationsSection = () => {
  const applications = [
    {
      title: "Electronic Design",
      icon: <Zap className="h-8 w-8 text-yellow-600" />,
      description: "Ideal for engineers developing and testing new electronic circuits, providing detailed signal visualization."
    },
    {
      title: "Industrial Maintenance",
      icon: <Gauge className="h-8 w-8 text-yellow-600" />,
      description: "Essential for troubleshooting electrical equipment, identifying faults, and preventive maintenance."
    },
    {
      title: "Power Quality Analysis",
      icon: <BarChart className="h-8 w-8 text-yellow-600" />,
      description: "Analyze waveforms, harmonics, and power quality parameters to ensure efficient energy distribution."
    },
    {
      title: "Education & Research",
      icon: <Shield className="h-8 w-8 text-yellow-600" />,
      description: "Perfect for educational institutions teaching signal analysis and conducting advanced research."
    }
  ];

  return (
    <div className="py-6 md:py-8 bg-gradient-to-br from-yellow-50 to-white rounded-2xl my-6 shadow-lg font-['Open_Sans']">
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-6 md:mb-8">
          <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-gray-900 mb-3 font-['Open_Sans']">Application Areas</h2>
          <p className="text-sm md:text-base lg:text-lg text-gray-800 max-w-4xl mx-auto font-medium text-center font-['Open_Sans']">
            Our oscilloscopes are designed for a wide range of signal measurement applications
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {applications.map((app, idx) => (
            <motion.div
              key={idx}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: idx * 0.1 }}
              viewport={{ once: true }}
              className="bg-white p-4 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-2 border-b-4 border-yellow-400 text-center md:text-left"
            >
              <div className="bg-yellow-100 w-12 md:w-16 h-12 md:h-16 rounded-lg flex items-center justify-center mb-3 text-yellow-600 mx-auto md:mx-0">
                {app.icon}
              </div>
              <h3 className="text-base md:text-lg lg:text-xl font-semibold mb-2 text-gray-900 font-['Open_Sans']">{app.title}</h3>
              <p className="text-gray-900 font-semibold text-sm md:text-base text-center md:text-justify font-['Open_Sans']">{app.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Main Oscilloscopes Component (continued)
const Oscilloscopes = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const [activeProductType, setActiveProductType] = useState("handheld");
  const [activeDetailTab, setActiveDetailTab] = useState("features");
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // Effect to check URL parameters for initial tab and product type
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const tab = params.get('tab');
    const product = params.get('product');
    const detailTab = params.get('detailTab');

    if (tab) setActiveTab(tab);
    if (product && (product === "handheld" || product === "portable")) {
      setActiveProductType(product);
    }
    if (detailTab) setActiveDetailTab(detailTab);

    // If we're viewing product details, scroll to the product section
    if (tab === 'details' && product) {
      // Use requestAnimationFrame instead of setTimeout to prevent blinking
      requestAnimationFrame(() => {
        // Find the product detail section by ID and scroll directly to it
        const productDetailSection = document.getElementById('product-detail-view');
        if (productDetailSection) {
          productDetailSection.scrollIntoView({ behavior: 'auto', block: 'start' });
        }
      });
    }
  }, [location]);

  // Handler for Request Demo button
  const handleRequestDemo = () => {
    navigate("/contact/sales");
  };

  // Handler for View Brochure button
  const handleViewBrochure = () => {
    window.open(PDF_URL, '_blank');
  };

  // Handler for View Details button
  const handleViewDetails = (productType) => {
    // Update state
    setActiveProductType(productType);
    setActiveTab('details');
    setActiveDetailTab('features');

    // Update URL - this will directly show the details tab
    navigate(`?tab=details&product=${productType}&detailTab=features`, { replace: true });

    // Use requestAnimationFrame instead of setTimeout to prevent blinking
    // This ensures the DOM updates before scrolling
    requestAnimationFrame(() => {
      // Find the product details section and scroll to it
      const detailsSection = document.getElementById('product-detail-view');
      if (detailsSection) {
        // Use scrollIntoView with block: 'start' to position at the top of viewport
        detailsSection.scrollIntoView({ behavior: 'auto', block: 'start' });
      }
    });
  };

  // Handheld Oscilloscope data
  const handheldData = {
    title: "Handheld Oscilloscope",
    modelInfo: "OX 5022/OX 5042",
    image: "/oscillosacopes/Handheld-handscope.png",
    bandwidth: "20-40 MHz",
    displayInfo: "3.5\" Color TFT",
    features: [
      "2 isolated channels for safety and accuracy",
      "Multiple operating modes (oscilloscope, multimeter, analyzer)",
      "Input impedance: 1 MΩ ±0.5%, approximately 17 pF",
      "2,500 real acquisition points on screen",
      "Harmonic analyzer with 31 orders"
    ],
    colors: {
      primary: 'yellow-400',
      secondary: 'yellow-50'
    }
  };

  // Portable Oscilloscope data
  const portableData = {
    title: "Portable Oscilloscope",
    modelInfo: "OX 9062/OX 9102/OX 9104/OX 9304",
    image: "/oscillosacopes/Portable-scopics-4.png",
    bandwidth: "60-300 MHz",
    displayInfo: "7\" Touch Screen",
    features: [
      "2-4 isolated channels for comprehensive measurements",
      "7\" WVGA color touch screen with intuitive interface",
      "Up to 300 MHz bandwidth for high-frequency signals",
      "20 automatic measurements per channel",
      "Advanced harmonic analysis up to 63rd order"
    ],
    colors: {
      primary: 'yellow-400',
      secondary: 'yellow-50'
    }
  };

  // Product Type Options
  const productOptions = [
    { id: "handheld", label: "Handheld Series", models: "OX 5022/OX 5042" },
    { id: "portable", label: "Portable Series", models: "OX 9062/OX 9102/OX 9104/OX 9304" }
  ];

  // Get active product data
  const getActiveProductData = () => {
    if (activeProductType === "handheld") return handheldData;
    return portableData;
  };

  // Navigation tabs data
  const navTabs = [
    { id: "overview", label: "Overview", icon: <Gauge className="h-5 w-5" /> },
    { id: "details", label: "Product Details", icon: <Zap className="h-5 w-5" /> },
    { id: "applications", label: "Applications", icon: <Shield className="h-5 w-5" /> }
  ];

  return (
    <PageLayout
      title="Oscilloscopes"
      subtitle="Precision tools for electrical signal analysis"
      category="measure"
    >
      {/* Modern Background */}
      <ModernBackground />

      {/* Premium Modern Navigation Tabs - Responsive Design */}
      <div className="sticky top-0 z-30 bg-white shadow-lg border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4">
          {/* Desktop Navigation */}
          <div className="hidden md:flex justify-center py-2">
            <div className="bg-gray-50 p-1.5 rounded-full flex shadow-sm">
              {navTabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => {
                    setActiveTab(tab.id);
                    if (tab.id === "details") {
                      navigate(`?tab=${tab.id}&product=${activeProductType}&detailTab=${activeDetailTab}`, { replace: true });
                    } else {
                      navigate(`?tab=${tab.id}`, { replace: true });
                    }
                    setIsMobileMenuOpen(false);
                  }}
                  className={cn(
                    "relative px-6 py-2.5 font-medium rounded-full transition-all duration-300 flex items-center mx-1 overflow-hidden",
                    activeTab === tab.id
                      ? "bg-gradient-to-r from-yellow-500 to-yellow-400 text-white shadow-md transform -translate-y-0.5"
                      : "text-gray-700 hover:bg-yellow-50 hover:text-yellow-500"
                  )}
                >
                  <div className="flex items-center relative z-10">
                    <span className="mr-2">{tab.icon}</span>
                    <span>{tab.label}</span>
                  </div>
                  {activeTab === tab.id && (
                    <motion.div
                      layoutId="navIndicator"
                      className="absolute inset-0 bg-gradient-to-r from-yellow-500 to-yellow-400 -z-0"
                      initial={false}
                      transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                    />
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Mobile Navigation */}
          <div className="md:hidden py-2 flex justify-between items-center">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="text-gray-700 hover:text-yellow-500 focus:outline-none"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            <span className="font-semibold text-gray-900 text-lg">
              {navTabs.find(tab => tab.id === activeTab)?.label}
            </span>

            <div className="w-6"></div> {/* Spacer for balanced layout */}
          </div>

          {/* Mobile Menu Dropdown */}
          <div className={`md:hidden overflow-hidden transition-all duration-300 ease-in-out ${isMobileMenuOpen ? 'max-h-60' : 'max-h-0'}`}>
            <div className="bg-white rounded-lg shadow-lg p-2 mb-2 z-50 relative">
              {navTabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => {
                    setActiveTab(tab.id);
                    if (tab.id === "details") {
                      navigate(`?tab=${tab.id}&product=${activeProductType}&detailTab=${activeDetailTab}`, { replace: true });
                    } else {
                      navigate(`?tab=${tab.id}`, { replace: true });
                    }
                    setIsMobileMenuOpen(false);
                  }}
                  className={`w-full text-left px-4 py-3 rounded-lg my-1 flex items-center ${
                    activeTab === tab.id
                      ? "bg-yellow-50 text-yellow-600 font-medium"
                      : "text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  <span className="mr-3">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {activeTab === "overview" && (
        <>
          {/* Hero Section */}
          <HeroSection
            onRequestDemo={handleRequestDemo}
            onViewBrochure={handleViewBrochure}
          />

          {/* Key Features Overview */}
          <div className="py-8 md:py-12 bg-gradient-to-br from-yellow-50 to-white">
            <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-8 md:mb-12">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                  viewport={{ once: true }}
                >
                  <h2 className="text-2xl md:text-3xl font-bold text-yellow-600 mb-4 font-['Open_Sans']">Why Choose Our Oscilloscopes?</h2>
                  <p className="mt-4 text-base md:text-lg text-gray-800 max-w-4xl mx-auto font-medium text-center font-['Open_Sans']">
                    Precision-engineered instruments that combine multiple measurement capabilities in a single device
                  </p>
                </motion.div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
                <FeatureHighlight
                  icon={<Gauge className="h-6 w-6 text-white" />}
                  title="Multi-Mode Capability"
                  description="Each device functions as an oscilloscope, multimeter, and harmonic analyzer, providing versatile measurement options in a single instrument."
                />

                <FeatureHighlight
                  icon={<Zap className="h-6 w-6 text-white" />}
                  title="Isolated Channels"
                  description="Fully isolated input channels ensure accurate measurements and user safety when working with different circuit potentials."
                />

                <FeatureHighlight
                  icon={<Shield className="h-6 w-6 text-white" />}
                  title="High-Resolution Display"
                  description="Crisp, vibrant displays with intuitive interfaces make complex measurements easier to visualize and interpret."
                />
              </div>
            </div>
          </div>

          {/* Products Section */}
          <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-8 md:mb-12"
            >
              <span className="inline-block bg-yellow-100 text-yellow-800 px-4 py-1 rounded-full text-sm font-semibold mb-4 font-['Open_Sans']">
                PROFESSIONAL SERIES
              </span>
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-6 text-gray-900 font-['Open_Sans']">
                Our Oscilloscope Series
              </h2>
              <p className="max-w-4xl mx-auto text-gray-800 text-base md:text-lg font-medium text-center font-['Open_Sans']">
                Choose the perfect oscilloscope for your signal measurement needs.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 px-2 sm:px-0">
              <ProductCard
                {...handheldData}
                onViewDetailsClick={() => handleViewDetails("handheld")}
              />
              <ProductCard
                {...portableData}
                onViewDetailsClick={() => handleViewDetails("portable")}
              />
            </div>
          </div>

          {/* Quick Comparison Table */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="bg-white rounded-2xl shadow-lg p-4 sm:p-8 mb-16">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Quick Comparison</h2>
              <div className="overflow-x-auto -mx-4 sm:mx-0">
                <div className="inline-block min-w-full align-middle">
                  <table className="min-w-full">
                    <thead>
                      <tr className="bg-yellow-100">
                        <th className="px-2 sm:px-4 py-3 text-left text-gray-800 font-semibold text-sm sm:text-base">Feature</th>
                        <th className="px-2 sm:px-4 py-3 text-left text-gray-800 font-semibold text-sm sm:text-base">Handheld Series</th>
                        <th className="px-2 sm:px-4 py-3 text-left text-gray-800 font-semibold text-sm sm:text-base">Portable Series</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      <tr className="hover:bg-yellow-50">
                        <td className="px-2 sm:px-4 py-3 font-medium text-gray-800 text-sm sm:text-base">Bandwidth</td>
                        <td className="px-2 sm:px-4 py-3 text-gray-700 text-sm sm:text-base">20-40 MHz</td>
                        <td className="px-2 sm:px-4 py-3 text-gray-700 text-sm sm:text-base">60-300 MHz</td>
                      </tr>
                      <tr className="hover:bg-yellow-50">
                        <td className="px-2 sm:px-4 py-3 font-medium text-gray-800 text-sm sm:text-base">Channels</td>
                        <td className="px-2 sm:px-4 py-3 text-gray-700 text-sm sm:text-base">2 isolated</td>
                        <td className="px-2 sm:px-4 py-3 text-gray-700 text-sm sm:text-base">2-4 isolated</td>
                      </tr>
                      <tr className="hover:bg-yellow-50">
                        <td className="px-2 sm:px-4 py-3 font-medium text-gray-800 text-sm sm:text-base">Display</td>
                        <td className="px-2 sm:px-4 py-3 text-gray-700 text-sm sm:text-base">3.5" color TFT</td>
                        <td className="px-2 sm:px-4 py-3 text-gray-700 text-sm sm:text-base">7" touch screen</td>
                      </tr>
                      <tr className="hover:bg-yellow-50">
                        <td className="px-2 sm:px-4 py-3 font-medium text-gray-800 text-sm sm:text-base">Memory</td>
                        <td className="px-2 sm:px-4 py-3 text-gray-700 text-sm sm:text-base">2 MB</td>
                        <td className="px-2 sm:px-4 py-3 text-gray-700 text-sm sm:text-base">2 GB</td>
                      </tr>
                      <tr className="hover:bg-yellow-50">
                        <td className="px-2 sm:px-4 py-3 font-medium text-gray-800 text-sm sm:text-base">Harmonics</td>
                        <td className="px-2 sm:px-4 py-3 text-gray-700 text-sm sm:text-base">Up to 31 orders</td>
                        <td className="px-2 sm:px-4 py-3 text-gray-700 text-sm sm:text-base">Up to 63 orders</td>
                      </tr>
                      <tr className="hover:bg-yellow-50">
                        <td className="px-2 sm:px-4 py-3 font-medium text-gray-800 text-sm sm:text-base">Logger Mode</td>
                        <td className="px-2 sm:px-4 py-3 text-gray-700 text-sm sm:text-base">No</td>
                        <td className="px-2 sm:px-4 py-3 text-gray-700 text-sm sm:text-base">Yes</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Information Section */}
          <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <ContactSection onContactClick={handleRequestDemo} />
          </div>
        </>
      )}

      {activeTab === "details" && (
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6 md:py-8">
          {/* Enhanced Product Type Selector - Compact Version */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-gradient-to-r from-yellow-50 to-white rounded-xl shadow-md p-4 md:p-6 mb-6 relative overflow-hidden font-['Open_Sans']"
          >
            <div className="absolute top-0 right-0 w-64 h-64 bg-yellow-200 rounded-full opacity-20 transform translate-x-1/2 -translate-y-1/2 blur-3xl"></div>

            <h2 className="text-lg md:text-xl font-bold text-black mb-4 relative z-10 text-center font-['Open_Sans']">
              Select <span className="text-yellow-500">Model Series</span>
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 md:gap-3 relative z-10">
              {productOptions.map((option) => (
                <motion.button
                  key={option.id}
                  whileHover={{ y: -3, boxShadow: "0 8px 20px -5px rgba(0, 0, 0, 0.1)" }}
                  whileTap={{ y: 0 }}
                  onClick={() => {
                    setActiveProductType(option.id);
                    navigate(`?tab=details&product=${option.id}&detailTab=${activeDetailTab}`, { replace: true });
                  }}
                  className={`relative rounded-lg transition-all duration-300 overflow-hidden ${
                    activeProductType === option.id
                      ? "ring-1 ring-yellow-400"
                      : "hover:ring-1 hover:ring-yellow-300"
                  }`}
                >
                  <div className={`h-full py-3 md:py-4 px-2 md:px-3 flex flex-col items-center text-center ${
                    activeProductType === option.id
                      ? "bg-yellow-400 text-white"
                      : "bg-white hover:bg-yellow-50"
                  }`}>
                    <div className="mb-2">
                      {option.id === "handheld" && (
                        <Gauge className={`h-6 w-6 md:h-8 md:w-8 ${activeProductType === option.id ? "text-white" : "text-yellow-400"}`} />
                      )}
                      {option.id === "portable" && (
                        <Zap className={`h-6 w-6 md:h-8 md:w-8 ${activeProductType === option.id ? "text-white" : "text-yellow-400"}`} />
                      )}
                    </div>

                    <h3 className={`text-sm md:text-lg font-bold mb-1 font-['Open_Sans'] ${activeProductType === option.id ? "text-white" : "text-black"}`}>
                      {option.label}
                    </h3>

                    <div className={`text-xs font-['Open_Sans'] ${activeProductType === option.id ? "text-white opacity-80" : "text-gray-500"}`}>
                      {option.models}
                    </div>

                    {activeProductType === option.id && (
                      <div className="mt-2 bg-white bg-opacity-20 rounded-full px-2 md:px-3 py-0.5 text-xs font-semibold font-['Open_Sans']">
                        Selected
                      </div>
                    )}
                  </div>
                </motion.button>
              ))}
            </div>
          </motion.div>

          {/* Enhanced Product Detail Section */}
          <div id="product-detail-view" className="grid grid-cols-1 md:grid-cols-12 gap-4 md:gap-6 product-details-section">
            <div className="md:col-span-5">
              <div className="md:sticky md:top-24">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                  className="bg-gradient-to-br from-white to-yellow-50 rounded-2xl shadow-lg p-4 md:p-6 mb-6 relative overflow-hidden font-['Open_Sans']"
                >
                  {/* Background decoration */}
                  <div className="absolute top-0 right-0 w-1/2 h-1/2 bg-yellow-200 rounded-full opacity-10 blur-3xl"></div>
                  <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-yellow-100 rounded-full opacity-10 blur-3xl"></div>

                  {/* Product badge */}
                  <div className="absolute top-4 left-4 bg-yellow-400 text-white px-3 py-1 rounded-full text-xs font-semibold z-10 font-['Open_Sans']">
                    {getActiveProductData().modelInfo}
                  </div>

                  {/* Image container with glow effect */}
                  <div className="relative mb-6 mt-4">
                    <div className="absolute inset-0 bg-gradient-to-br from-yellow-200 to-yellow-50 rounded-full opacity-30 blur-xl transform scale-90"></div>
                    <motion.div
                      animate={{ y: [0, -10, 0] }}
                      transition={{ repeat: Infinity, duration: 3, ease: "easeInOut" }}
                      className="relative z-10 flex justify-center items-center py-6"
                    >
                      <img
                        src={getActiveProductData().image}
                        alt={getActiveProductData().title}
                        className="max-h-48 md:max-h-56 w-auto object-contain drop-shadow-2xl transform transition-transform duration-500 hover:scale-110"
                        id="product-detail-image"
                      />
                    </motion.div>
                  </div>

                  {/* Product details */}
                  <div className="text-center mb-6">
                    <h3 className="text-xl md:text-2xl font-bold text-black mb-3 font-['Open_Sans']">{getActiveProductData().title}</h3>
                    <div className="flex justify-center space-x-3 mb-4">
                      <span className="inline-block bg-yellow-100 text-yellow-700 px-3 py-1 rounded-full text-sm font-medium font-['Open_Sans']">
                        {getActiveProductData().bandwidth} Bandwidth
                      </span>
                    </div>
                  </div>

                  {/* Specs cards */}
                  <div className="grid grid-cols-2 gap-3 mb-6">
                    <div className="bg-white p-3 rounded-xl shadow-sm border border-yellow-100 transform transition-transform duration-300 hover:-translate-y-1 hover:shadow-md">
                      <div className="flex items-center mb-2">
                        <Gauge className="h-4 w-4 md:h-5 md:w-5 text-yellow-400 mr-2" />
                        <span className="text-xs md:text-sm font-medium text-gray-500 font-['Open_Sans']">Display</span>
                      </div>
                      <span className="font-semibold text-black text-sm md:text-base font-['Open_Sans']">{getActiveProductData().displayInfo}</span>
                    </div>
                    <div className="bg-white p-3 rounded-xl shadow-sm border border-yellow-100 transform transition-transform duration-300 hover:-translate-y-1 hover:shadow-md">
                      <div className="flex items-center mb-2">
                        <Zap className="h-4 w-4 md:h-5 md:w-5 text-yellow-400 mr-2" />
                        <span className="text-xs md:text-sm font-medium text-gray-500 font-['Open_Sans']">Channels</span>
                      </div>
                      <span className="font-semibold text-black text-sm md:text-base font-['Open_Sans']">
                        {activeProductType === "handheld" ? "2 Isolated" : "2-4 Isolated"}
                      </span>
                    </div>
                  </div>

                  {/* View Brochure button */}
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      className="w-full bg-gradient-to-r from-yellow-400 to-yellow-400 hover:from-yellow-500 hover:to-yellow-500 text-black font-semibold rounded-lg shadow-md transition-all duration-300 flex items-center justify-center py-2 md:py-3 text-sm md:text-base font-['Open_Sans']"
                      onClick={handleViewBrochure}
                    >
                      <span>View Product Brochure</span>
                      <FileText className="ml-2 h-4 w-4 md:h-5 md:w-5" />
                    </Button>
                  </motion.div>
                </motion.div>
              </div>
            </div>

            <div className="md:col-span-7">
              {/* Enhanced Detail Tabs Navigation */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="bg-white rounded-xl shadow-lg mb-6 overflow-hidden font-['Open_Sans']"
              >
                <div className="flex border-b overflow-x-auto scrollbar-hide">
                  {[
                    { id: "features", label: "Features", icon: <Shield className="h-4 sm:h-5 w-4 sm:w-5" /> },
                    { id: "measurements", label: "Measurements", icon: <Gauge className="h-4 sm:h-5 w-4 sm:w-5" /> },
                    { id: "comparison", label: "Comparison", icon: <BarChart className="h-4 sm:h-5 w-4 sm:w-5" /> }
                  ].map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => {
                        setActiveDetailTab(tab.id);
                        navigate(`?tab=details&product=${activeProductType}&detailTab=${tab.id}`, { replace: true });
                      }}
                      className={`px-4 md:px-6 py-3 md:py-4 font-medium whitespace-nowrap flex items-center transition-all duration-300 text-sm md:text-base font-['Open_Sans'] ${
                        activeDetailTab === tab.id
                          ? "bg-yellow-50 border-b-2 border-yellow-400 text-yellow-700"
                          : "text-gray-700 hover:text-yellow-600 hover:bg-yellow-50/50"
                      }`}
                    >
                      <span className="mr-2">{tab.icon}</span>
                      {tab.label}
                    </button>
                  ))}
                </div>
              </motion.div>

              {/* Tab Content with enhanced styling */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="transform origin-top"
              >
                {activeDetailTab === "comparison" ? (
                  <ComparisonTable productType={activeProductType} />
                ) : (
                  <ProductTabContent activeProductType={activeProductType} activeTab={activeDetailTab} />
                )}
              </motion.div>
            </div>
          </div>

          {/* Enhanced Contact Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
            className="mt-12"
          >
            <ContactSection onContactClick={handleRequestDemo} />
          </motion.div>
        </div>
      )}

      {activeTab === "applications" && (
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6 md:py-8">
          <div className="text-center mb-8 md:mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4 font-['Open_Sans']">Applications</h1>
              <p className="text-base md:text-lg text-gray-800 max-w-4xl mx-auto text-center font-['Open_Sans']">
                KRYKARD oscilloscopes are versatile instruments designed for a wide range of professional applications
              </p>
            </motion.div>
          </div>

          <ApplicationsSection />

          <div className="mt-8 md:mt-12 bg-white rounded-2xl shadow-lg p-4 md:p-6">
            <h2 className="text-xl md:text-2xl font-bold text-gray-900 mb-4 md:mb-6 text-center font-['Open_Sans']">Industry-Specific Solutions</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Electronics Manufacturing</h3>
                  <p className="text-gray-800 font-medium">
                    Quality control testing, troubleshooting production issues, and verifying circuit performance in manufacturing environments.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Research & Development</h3>
                  <p className="text-gray-800 font-medium">
                    Prototype testing, signal characterization, and performance validation during the development of new electronic products.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Electrical Utility Services</h3>
                  <p className="text-gray-800 font-medium">
                    Power quality analysis, troubleshooting distribution networks, and monitoring harmonic distortion in electrical systems.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Educational Institutions</h3>
                  <p className="text-gray-800 font-medium">
                    Practical laboratory demonstrations, student experiments, and advanced signal analysis for educational purposes.
                  </p>
                </div>
              </motion.div>
            </div>

            {/* View Brochure button */}
            <div className="flex justify-center mt-8">
              <Button
                className="px-6 py-3 bg-gradient-to-r from-yellow-500 to-yellow-400 hover:from-yellow-600 hover:to-yellow-500 text-gray-900 font-semibold rounded-lg shadow-md transition duration-300 flex items-center space-x-2"
                onClick={handleViewBrochure}
              >
                <span>View Brochure</span>
                <FileText className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </div>

          <div className="mt-16">
            <ContactSection onContactClick={handleRequestDemo} />
          </div>
        </div>
      )}

      {/* No PDF Viewer Modal - Using direct link instead */}
    </PageLayout>
  );
};

export default Oscilloscopes;