import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import Layout from "@/components/layout/Layout";
import { Button } from "@/components/ui/button";

interface ProductSpec {
  label: string;
  description: string;
}

interface TableRow {
  parameter: string;
  range: string;
  accuracy: string;
}

interface Product {
  id: string;
  name: string;
  title: string;
  subtitle?: string;
  model: string;
  image: string;
  description?: string;
  features: string[];
  specs: ProductSpec[];
  hasRangeTable?: boolean;
  rangeData?: TableRow[];
}

const LightMeterProductPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('lux-meter');
  
  const products: Product[] = [
    {
      id: 'lux-meter',
      name: 'Lux Meter',
      title: 'LUX',
      subtitle: 'METER',
      model: 'C.A 1110',
      image: '/images/lux-meter.png',
      description: 'The CA 1110 luxmeter (light meter) measures the illuminance of all light sources (LED, fluo, etc.) up to 200,000 lux in compliance with Class C of the NF C 42-710 standard. With the MAP function, you can produce a map of a surface to check in order to verify that the illumination is uniform. Because it is magnetic, the sensor can be fixed to the product for centralized use or for easy transport, but it is also possible to use it remotely by hand thanks to the 1.5m cable. The CA 1110 also offers Min, Max, Average and Hold functions, as well as allowing spot or programmable recording. The Data Logger Transfer software is available for free download to view the recorded data, configure the CA 1110, benefit from remote display and generate automatic reports.',
      features: [
        'Models: C.A 1110',
        'Spectral error compensation for LED or fluorescent sources',
        'MAP function: determine the distribution of surface lux in a room',
        '1.5m cable between unit and remote sensor',
        'Wide backlit display',
        'Remote probe allowing the risk of the operator shading the sensor or built-in probe for one-handed use',
        'Recording up to 1 million points',
        'Communicating via USB or Bluetooth',
        'Data Logger Transfer software with automatic report generation'
      ],
      specs: [
        { label: 'Measurement range', description: '0.1 to 200,000 lux' },
        { label: 'Intrinsic uncertainty without compensation', description: '±3% of reading' },
        { label: '3% of the reading on incandescent source', description: '' },
        { label: '6% of the reading on LED source (white)', description: '' },
        { label: '9% of the reading on fluorescent source', description: '' },
        { label: 'Min, Max, Average, MAP and Hold functions', description: 'Yes & Backlighting' },
        { label: 'Recording', description: 'up to 1 million points' },
        { label: 'USB or Bluetooth interfaces', description: '' },
        { label: 'Casing dimensions', description: '150 x 72 x 32 mm / Sensor: 67 x 64 x 35 mm' },
        { label: 'Weight', description: '345 g with batteries' },
        { label: 'Compatible with the MultiFix accessory', description: '' },
        { label: 'Shockproof protective sheath', description: 'available as an accessory' }
      ]
    },
    {
      id: 'light-meter-220',
      name: 'Light Meter 220',
      title: 'LIGHT',
      subtitle: 'METER',
      model: 'ATEST EL 220',
      image: '/images/light-meter-220.png',
      features: [
        'Models - ATEST EL 220',
        'Lx/Fc function Selectable',
        'Auto Ranging',
        'Automatic Power off',
        'Hold Function',
        'Range Display',
        'Automatic Zero When Power On'
      ],
      specs: [
        { label: 'Measurement unit selection', description: 'Lux/Footcandle selectable' },
        { label: 'Ranging', description: 'Automatic ranging for easy operation' },
        { label: 'Power management', description: 'Automatic power off to conserve battery' },
        { label: 'Data functions', description: 'Hold function to freeze readings' },
        { label: 'Display features', description: 'Range display for clear measurement information' },
        { label: 'Calibration', description: 'Automatic zero when powered on for accuracy' }
      ],
      hasRangeTable: true,
      rangeData: [
        { parameter: 'Light Range (fc)', range: '0.000-4000 fc', accuracy: '5' },
        { parameter: 'Light Range (lx)', range: '0.000-40000 lx', accuracy: '5' },
        { parameter: 'Sample rate', range: '2 times/sec', accuracy: '-' }
      ]
    },
    {
      id: 'light-meter-722',
      name: 'Light Meter 722',
      title: 'LIGHT',
      subtitle: 'METER',
      model: 'ATEST EL 722',
      image: '/images/light-meter-722.png',
      features: [
        'Models - ATEST EL 722',
        'Lx/Fc function Selectable',
        'Data Hold Function',
        'Range Display',
        'Big LCD Display',
        'Automatic Zero When Power On',
        'Detachable & Rotatable Light Sensor'
      ],
      specs: [
        { label: 'Measurement unit selection', description: 'Lux/Footcandle selectable for flexibility' },
        { label: 'Data functions', description: 'Data hold to freeze measurements for recording' },
        { label: 'Display features', description: 'Range display and large LCD for clear reading' },
        { label: 'Calibration', description: 'Automatic zero when turned on for accuracy' },
        { label: 'Sensor design', description: 'Detachable and rotatable light sensor for versatile positioning' }
      ],
      hasRangeTable: true,
      rangeData: [
        { parameter: 'Light Range (fc)', range: '0-10,000 fc', accuracy: '5' },
        { parameter: 'Light Range (lx)', range: '0-100,000 lx', accuracy: '5' },
        { parameter: 'Sample rate', range: '2 times/sec', accuracy: '-' }
      ]
    },
    {
      id: 'light-meter-724',
      name: 'Light Meter 724',
      title: 'LIGHT',
      subtitle: 'METER',
      model: 'ATEST EL 724',
      image: '/images/light-meter-724.png',
      features: [
        'Models - ATEST EL 724',
        'Lx/Fc function Selectable',
        'Four Adjustable Parameters For Special Light Source',
        'Data Hold Function',
        'Range Display',
        'Big LCD Display',
        'Automatic Zero When Power On',
        'Detachable & Rotatable Light Sensor'
      ],
      specs: [
        { label: 'Measurement unit selection', description: 'Lux/Footcandle selectable' },
        { label: 'Light source parameters', description: 'Four adjustable parameters for different light sources' },
        { label: 'Data functions', description: 'Data hold for measurement recording' },
        { label: 'Display features', description: 'Range display and large LCD for easy reading' },
        { label: 'Calibration', description: 'Automatic zero when powered on' },
        { label: 'Sensor flexibility', description: 'Detachable and rotatable light sensor for optimal positioning' }
      ],
      hasRangeTable: true,
      rangeData: [
        { parameter: 'Light Range (fc)', range: '0-20,000 fc / 0-200,000 fc', accuracy: '5' },
        { parameter: 'Light Range (lx)', range: '0-2,000 lx / 0-20,000 lx / 0-200,000 lx', accuracy: '4' },
        { parameter: 'Response Time', range: '0.5 sec', accuracy: '-' }
      ]
    }
  ];
  
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };
  
  const activeProduct = products.find(product => product.id === activeTab);
  
  return (
    <Layout>
      <div className="pt-24 md:pt-32">
        {/* Product Tabs */}
        <div className="bg-white border-b">
          <div className="container mx-auto px-4">
            <div className="flex overflow-x-auto py-4 space-x-6">
              {products.map(product => (
                <button
                  key={product.id}
                  className={`whitespace-nowrap px-4 py-2 font-medium rounded-md transition-colors ${
                    activeTab === product.id 
                      ? 'bg-yellow-500 text-white' 
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => handleTabChange(product.id)}
                >
                  {product.name}
                </button>
              ))}
            </div>
          </div>
        </div>
        
        {/* Main Content */}
        {activeProduct && (
          <main className="flex-grow container mx-auto px-4 py-8">
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              {/* Product Hero Section */}
              <div className="relative">
                <div className={`absolute inset-0 ${activeTab === 'lux-meter' ? 'bg-gray-100' : 'bg-yellow-50'}`}></div>
                <div className="relative flex flex-col md:flex-row p-6 md:p-12 items-center">
                  <div className="w-full md:w-1/3 flex justify-center mb-8 md:mb-0">
                    <div className="bg-white p-4 rounded-lg shadow-md transform hover:scale-105 transition-transform duration-300">
                      <img 
                        src={activeProduct.image} 
                        alt={activeProduct.name} 
                        className="max-h-80 object-contain"
                      />
                    </div>
                  </div>
                  <div className="w-full md:w-2/3 md:pl-12">
                    <div className="flex flex-col">
                      <h1 className="text-4xl font-normal text-gray-700 mb-2">
                        {activeProduct.title}
                      </h1>
                      {activeProduct.subtitle && (
                        <h2 className="text-4xl font-bold text-yellow-500 mb-6">
                          {activeProduct.subtitle}
                        </h2>
                      )}
                    </div>
                    <p className="text-lg font-medium mb-4">Model: {activeProduct.model}</p>
                    
                    {/* Features List */}
                    <div className="mb-6">
                      <ul className="space-y-3">
                        {activeProduct.features.map((feature, index) => (
                          <li key={index} className="flex items-start">
                            <div className="flex-shrink-0 h-5 w-5 rounded-full bg-yellow-500 mt-1 mr-3"></div>
                            <span className="text-gray-700">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div className="flex space-x-4 mt-6">
                      <Button variant="outline" className="border-yellow-500 text-yellow-500 hover:bg-yellow-50 rounded-full">
                        ENQUIRE
                      </Button>
                      <Button variant="outline" className="border-yellow-500 text-yellow-500 hover:bg-yellow-50 rounded-full">
                        BROCHURE
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Product Description */}
              {activeProduct.description && (
                <div className="p-6 md:p-12 bg-yellow-50 border-t border-yellow-100">
                  <div className="flex flex-col md:flex-row">
                    <div className="w-full md:w-2/3 md:pr-8">
                      <p className="text-gray-700 leading-relaxed">
                        {activeProduct.description}
                      </p>
                    </div>
                    <div className="w-full md:w-1/3 mt-6 md:mt-0">
                      <img 
                        src="/images/product-in-use.jpg" 
                        alt="Product in use" 
                        className="rounded-lg shadow-md w-full h-auto" 
                      />
                    </div>
                  </div>
                </div>
              )}
              
              {/* Product Specifications */}
              <div className="p-6 md:p-12 border-t border-gray-200">
                <h3 className="text-2xl font-bold text-gray-700 mb-8">Specifications</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {activeProduct.specs.map((spec, index) => (
                    <div key={index} className="flex items-start">
                      <div className="flex-shrink-0 flex items-start">
                        <div className="h-2 w-2 rounded-full bg-gray-500 mt-2 mr-2"></div>
                      </div>
                      <div>
                        <span className="font-bold text-gray-700">{spec.label}: </span>
                        <span className="text-gray-600">{spec.description}</span>
                      </div>
                    </div>
                  ))}
                </div>
                
                {/* Range Table */}
                {activeProduct.hasRangeTable && activeProduct.rangeData && (
                  <div className="mt-12 overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr>
                          <th className="px-4 py-3 bg-yellow-500 text-white font-bold text-left">PARAMETER</th>
                          <th className="px-4 py-3 bg-yellow-500 text-white font-bold text-left">RANGE</th>
                          <th className="px-4 py-3 bg-yellow-500 text-white font-bold text-left">ACCURACY %</th>
                        </tr>
                      </thead>
                      <tbody>
                        {activeProduct.rangeData.map((row, index) => (
                          <tr key={index} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                            <td className="px-4 py-3 border border-gray-200">{row.parameter}</td>
                            <td className="px-4 py-3 border border-gray-200">{row.range}</td>
                            <td className="px-4 py-3 border border-gray-200">{row.accuracy}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
              
              {/* Features Section */}
              <div className="p-6 md:p-12 bg-gray-50 border-t border-gray-200">
                <h3 className="text-2xl font-bold text-gray-700 mb-8">Key Benefits</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                    </div>
                    <h4 className="text-lg font-bold text-gray-700 mb-2">High Accuracy</h4>
                    <p className="text-gray-600">Precise measurements for professional light intensity monitoring with minimal uncertainty.</p>
                  </div>
                  
                  <div className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                      </svg>
                    </div>
                    <h4 className="text-lg font-bold text-gray-700 mb-2">User-Friendly Design</h4>
                    <p className="text-gray-600">Wide backlit display and intuitive controls for easy operation in all lighting conditions.</p>
                  </div>
                  
                  <div className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      </svg>
                    </div>
                    <h4 className="text-lg font-bold text-gray-700 mb-2">Versatile Applications</h4>
                    <p className="text-gray-600">Suitable for a wide range of light sources including LED, fluorescent, and incandescent lighting.</p>
                  </div>
                </div>
              </div>
              
              {/* Related Products */}
              <div className="p-6 md:p-12 border-t border-gray-200">
                <h3 className="text-2xl font-bold text-gray-700 mb-8">Related Products</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {products.filter(product => product.id !== activeTab).slice(0, 3).map(product => (
                    <div key={product.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                      <div className="p-4">
                        <img 
                          src={product.image} 
                          alt={product.name} 
                          className="w-full h-40 object-contain mb-4" 
                        />
                        <h4 className="text-lg font-bold text-gray-700 mb-2">{product.name}</h4>
                        <p className="text-sm text-gray-600 mb-4">Model: {product.model}</p>
                        <Button 
                          className="w-full bg-yellow-500 text-white hover:bg-yellow-600"
                          onClick={() => handleTabChange(product.id)}
                        >
                          View Details
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              {/* Call to Action */}
              <div className="p-6 md:p-12 bg-gray-800 text-white text-center">
                <h3 className="text-2xl font-bold mb-4">Need Professional Light Measurement Solutions?</h3>
                <p className="text-gray-300 mb-6 max-w-2xl mx-auto">Contact our team of experts to find the right equipment for your specific illumination measurement and monitoring needs.</p>
                <Button asChild className="px-8 py-3 bg-yellow-500 text-white hover:bg-yellow-600">
                  <Link to="/contact">REQUEST A QUOTE</Link>
                </Button>
              </div>
            </div>
          </main>
        )}
      </div>
    </Layout>
  );
};

export default LightMeterProductPage;