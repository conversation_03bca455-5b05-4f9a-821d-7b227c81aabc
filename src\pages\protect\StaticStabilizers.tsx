import React, { useEffect } from "react";
import { motion, useAnimation, useInView, AnimatePresence } from "framer-motion";
import { Link } from "react-router-dom";
import PageLayout from "@/components/layout/PageLayout";
import { Button } from "@/components/ui/button";
import { useRef } from "react";
import { Mail, ArrowRight } from "lucide-react";

const StaticStabilizers = () => {
  const keyFeatures = [
    "20kHz DSP based PWM control for fast cycle-by-cycle output correction",
    "Direct AC-AC conversion improves efficiency & reliability",
    "Instantaneous Correction @ 20,000 V/Sec speed",
    "No moving parts – no wear & tear and therefore low maintenance",
    "Output regulation of +/- 1%",
    "No distortion in output waveform",
    "Rugged and reliable IGBT power module",
    "Complete Protection from Over Voltage, Under Voltage, Overload, Single Phasing, Phase Reversal & Short Circuit",
    "Automatic bypass in case of failure",
    "LCD display for displaying all performance parameters",
    "Silent operation"
  ];

  const svrTypes = [
    {
      title: "Three Phase SVR",
      description: "High-performance three-phase static voltage regulator for industrial applications requiring reliable power conditioning.",
      features: [
        "Input range: 340-460V AC three phase",
        "Output: 400V ±1%",
        "DSP controlled IGBT technology",
        "20kHz switching frequency",
        "Available capacity: 10kVA to 1000kVA",
        "Independent phase control"
      ],
      accentColor: "from-blue-500 to-cyan-600",
      textColor: "text-blue-600",
      bgColor: "bg-gradient-to-br from-blue-50 to-cyan-50",
      image: "/Static_stabilizers/W_5-removebg-preview.png"
    },
    {
      title: "Industrial SVR",
      description: "Rugged industrial-grade static voltage regulator designed for harsh environments and continuous operation with zero maintenance.",
      features: [
        "High overload capacity: 150% for 30 sec",
        "Enhanced surge protection: 6kV",
        "IP54 protection available",
        "Operating temperature: 0-55°C",
        "Remote monitoring capability",
        "Parallel operation capable"
      ],
      accentColor: "from-indigo-500 to-blue-600",
      textColor: "text-indigo-700",
      bgColor: "bg-gradient-to-br from-indigo-50 to-blue-50",
      image: "/Static_stabilizers/B_3-removebg-preview.png"
    }
  ];

  const techRef = useRef(null);
  const techInView = useInView(techRef, { once: true, margin: "-100px 0px" });
  const techControls = useAnimation();

  const featureRef = useRef(null);
  const featureInView = useInView(featureRef, { once: true, margin: "-100px 0px" });
  const featureControls = useAnimation();

  const ctaRef = useRef(null);
  const ctaInView = useInView(ctaRef, { once: true, margin: "-100px 0px" });
  const ctaControls = useAnimation();
  const specRef = useRef(null);
  const specInView = useInView(specRef, { once: true, margin: "-100px 0px" });
  const specControls = useAnimation();

  useEffect(() => {
    if (techInView) {
      techControls.start("visible");
    }
    if (featureInView) {
      featureControls.start("visible");
    }
    if (ctaInView) {
      ctaControls.start("visible");
    }
    if (specInView) {
      specControls.start("visible");
    }
  }, [techInView, techControls, featureInView, featureControls, ctaInView, ctaControls, specInView, specControls]);

  return (
    <PageLayout
      title="Static Voltage Regulator"
      subtitle="Instantaneous correction for extra sensitive equipment"
      category="protect"
    >
      {/* Advanced animations */}
      <style>
        {`
          @keyframes float-slow {
            0% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(2deg); }
            100% { transform: translateY(0px) rotate(0deg); }
          }

          @keyframes float-slow-reverse {
            0% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(-2deg); }
            100% { transform: translateY(0px) rotate(0deg); }
          }

          @keyframes pulse-glow {
            0% { opacity: 0.3; filter: blur(8px); }
            50% { opacity: 0.8; filter: blur(12px); }
            100% { opacity: 0.3; filter: blur(8px); }
          }

          @keyframes pulse-glow-reverse {
            0% { opacity: 0.6; filter: blur(10px); }
            50% { opacity: 0.2; filter: blur(15px); }
            100% { opacity: 0.6; filter: blur(10px); }
          }

          @keyframes shimmer {
            0% { background-position: -100% 0; }
            100% { background-position: 200% 0; }
          }

          @keyframes gradient-shift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
          }

          @keyframes text-clip {
            from { clip-path: polygon(0 0, 0 0, 0 100%, 0 100%); }
            to { clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%); }
          }

          @keyframes text-fade-in {
            from { opacity: 0; filter: blur(4px); }
            to { opacity: 1; filter: blur(0); }
          }

          @keyframes text-fade-in-up {
            from { opacity: 0; transform: translateY(20px); filter: blur(4px); }
            to { opacity: 1; transform: translateY(0); filter: blur(0); }
          }

          @keyframes rotate-slow {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }

          @keyframes scale-pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
          }

          @keyframes border-glow {
            0% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.3); }
            50% { box-shadow: 0 0 15px rgba(59, 130, 246, 0.6); }
            100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.3); }
          }

          @keyframes background-pan {
            0% { background-position: 0% center; }
            100% { background-position: -200% center; }
          }

          @keyframes ripple {
            0% { transform: scale(0.8); opacity: 1; }
            100% { transform: scale(2); opacity: 0; }
          }

          @keyframes bounce-subtle {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
          }

          @keyframes slide-in-left {
            0% { transform: translateX(-100%); opacity: 0; }
            100% { transform: translateX(0); opacity: 1; }
          }

          @keyframes slide-in-right {
            0% { transform: translateX(100%); opacity: 0; }
            100% { transform: translateX(0); opacity: 1; }
          }

          @keyframes slide-in-bottom {
            0% { transform: translateY(100%); opacity: 0; }
            100% { transform: translateY(0); opacity: 1; }
          }

          @keyframes color-cycle {
            0% { color: #3b82f6; } /* blue-500 */
            25% { color: #2563eb; } /* blue-600 */
            50% { color: #1d4ed8; } /* blue-700 */
            75% { color: #2563eb; } /* blue-600 */
            100% { color: #3b82f6; } /* blue-500 */
          }

          .animate-float-slow {
            animation: float-slow 6s ease-in-out infinite;
          }

          .animate-float-slow-reverse {
            animation: float-slow-reverse 7s ease-in-out infinite;
          }

          .animate-pulse-glow {
            animation: pulse-glow 8s ease-in-out infinite;
          }

          .animate-pulse-glow-reverse {
            animation: pulse-glow-reverse 9s ease-in-out infinite;
          }

          .animate-shimmer {
            animation: shimmer 8s linear infinite;
            background: linear-gradient(90deg, transparent, rgba(0, 123, 255, 0.15), transparent);
            background-size: 200% 100%;
          }

          .animate-gradient-shift {
            animation: gradient-shift 15s ease infinite;
            background-size: 200% 200%;
          }

          .animate-text-clip {
            animation: text-clip 1s ease forwards 0.2s;
          }

          .animate-text-fade-in {
            animation: text-fade-in 1s ease forwards;
          }

          .animate-text-fade-in-up {
            animation: text-fade-in-up 1s ease forwards;
          }

          .animate-rotate-slow {
            animation: rotate-slow 30s linear infinite;
          }

          .animate-scale-pulse {
            animation: scale-pulse 3s ease-in-out infinite;
          }

          .animate-border-glow {
            animation: border-glow 3s ease-in-out infinite;
          }

          .animate-background-pan {
            animation: background-pan 15s linear infinite;
            background-size: 200% 100%;
          }

          .animate-ripple {
            animation: ripple 2s linear infinite;
          }

          .animate-bounce-subtle {
            animation: bounce-subtle 2s ease-in-out infinite;
          }

          .animate-slide-in-left {
            animation: slide-in-left 0.5s ease-out forwards;
          }

          .animate-slide-in-right {
            animation: slide-in-right 0.5s ease-out forwards;
          }

          .animate-slide-in-bottom {
            animation: slide-in-bottom 0.5s ease-out forwards;
          }

          .animate-color-cycle {
            animation: color-cycle 8s ease-in-out infinite;
          }

          .perspective-1000 {
            perspective: 1000px;
          }

          .perspective-3000 {
            perspective: 3000px;
          }

          .glass-effect {
            background: rgba(0, 123, 255, 0.05);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 123, 255, 0.1);
          }

          .glass-effect-strong {
            background: rgba(0, 123, 255, 0.1);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 123, 255, 0.2);
          }

          .text-shadow-blue {
            text-shadow: 0 0 10px rgba(0, 123, 255, 0.5);
          }

          .text-shadow-blue-strong {
            text-shadow: 0 0 15px rgba(0, 123, 255, 0.7);
          }

          .shadow-blue {
            box-shadow: 0 0 30px rgba(0, 123, 255, 0.3);
          }

          .shadow-blue-strong {
            box-shadow: 0 0 40px rgba(0, 123, 255, 0.5);
          }

          .shadow-blue-inner {
            box-shadow: inset 0 0 20px rgba(0, 123, 255, 0.2);
          }

          .watermark {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url("data:image/svg+xml,%3Csvg width='200' height='200' viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Ctext x='50%' y='50%' dominant-baseline='middle' text-anchor='middle' font-family='Arial' font-weight='bold' font-size='24' fill='rgba(0, 123, 255, 0.08)' transform='rotate(-45, 100, 100)'%3EKRYKARD%3C/text%3E%3C/svg%3E");
            background-repeat: repeat;
            background-size: 200px 200px;
            pointer-events: none;
            z-index: -1;
          }

          .hover-lift {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
          }

          .hover-lift:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.4);
          }

          .hover-glow:hover {
            box-shadow: 0 0 15px rgba(59, 130, 246, 0.6);
          }

          .hover-scale:hover {
            transform: scale(1.05);
          }

          .hover-rotate:hover {
            transform: rotate(2deg);
          }

          .gradient-text-blue {
            background: linear-gradient(90deg, #3b82f6, #1d4ed8, #3b82f6);
            background-size: 200% auto;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: background-pan 8s linear infinite;
          }
        `}
      </style>

      {/* Global watermark - Enhanced visibility */}
      <div className="watermark"></div>

      <div className="relative overflow-hidden">
        {/* Modern Hero Section with Blue Background Design - following clampmeters pattern */}
        <div className="relative py-6 md:py-12 mb-16 sm:mb-24 md:mb-32 overflow-hidden font-['Open_Sans']">
          {/* Hero Background Elements - Mobile optimized with blue theme */}
          <div className="absolute inset-0 z-0">
            <div className="absolute top-0 right-0 w-1/2 md:w-3/4 h-full bg-blue-50 rounded-bl-[50px] md:rounded-bl-[100px] transform -skew-x-6 md:-skew-x-12"></div>
            <div className="absolute bottom-20 left-0 w-32 h-32 md:w-64 md:h-64 bg-blue-400 rounded-full opacity-10"></div>
            <div className="absolute top-20 right-20 w-48 h-48 md:w-72 md:h-72 bg-blue-300/20 rounded-full blur-3xl animate-pulse-glow"></div>
            <div className="absolute bottom-10 left-10 w-48 h-48 md:w-72 md:h-72 bg-blue-400/20 rounded-full blur-3xl animate-pulse-glow-reverse"></div>
          </div>

          {/* Decorative elements with blue theme */}
          <div className="absolute -z-10 top-40 right-40 w-96 h-96 rounded-full border border-blue-300/30 animate-rotate-slow"></div>
          <div className="absolute -z-10 bottom-40 left-40 w-80 h-80 rounded-full border border-blue-300/40 animate-rotate-slow" style={{ animationDirection: 'reverse', animationDuration: '25s' }}></div>

          {/* Particle animation */}
          <div className="absolute inset-0 overflow-hidden">
            {Array.from({ length: 20 }).map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 rounded-full bg-blue-400"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  opacity: 0.4,
                }}
                animate={{
                  y: [0, -100],
                  opacity: [0, 0.8, 0],
                }}
                transition={{
                  duration: 4 + Math.random() * 6,
                  repeat: Infinity,
                  delay: i * 0.2,
                }}
              />
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8 }}
            className="container mx-auto px-4"
          >
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-10 lg:gap-20 items-center">
              {/* Content Section - Now on the left with blue theme */}
              <motion.div
                initial={{ opacity: 0, x: -40 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="relative z-10 order-2 lg:order-1 space-y-4 text-center lg:text-left"
              >
                {/* KRYKARD Precision Instruments Badge */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="inline-block bg-blue-400 py-1 px-3 rounded-full mb-2"
                >
                  <span className="text-sm md:text-base font-semibold text-white font-['Open_Sans']">KRYKARD Precision Instruments</span>
                </motion.div>

                {/* Enhanced product information with larger text - more responsive now */}
                <div className="mb-6 sm:mb-8">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                  >
                    <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight font-['Open_Sans'] mb-2 tracking-tight overflow-hidden">
                      STATIC <span className="text-blue-400">VOLTAGE REGULATOR</span>
                    </h1>
                  </motion.div>

                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: "150px" }}
                    transition={{ duration: 0.8, delay: 0.6 }}
                    className="h-1.5 sm:h-2 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full mb-4 sm:mb-6 mx-auto lg:mx-0"
                  />

                  <motion.p
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.8, delay: 0.7 }}
                    className="text-base sm:text-xl md:text-2xl font-medium text-gray-700 dark:text-gray-300 animate-text-fade-in font-['Open_Sans']"
                  >
                    INSTANTANEOUS CORRECTION FOR EXTRA SENSITIVE EQUIPMENT
                  </motion.p>
                </div>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.8 }}
                className="text-sm sm:text-base md:text-lg lg:text-xl text-gray-900 dark:text-gray-200 mb-6 sm:mb-10 leading-relaxed font-medium font-['Open_Sans']"
              >
                KRYKARD Static Voltage Regulator provides instantaneous voltage correction and power to a new generation of machines that are sensitive to even small voltage fluctuations. KRYKARD SVR is a DSP controlled IGBT based Voltage Regulator that uses Pulse Width Modulation technique (PWM) to provide accurate and high-speed voltage correction.
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1 }}
                className="flex flex-wrap gap-3 sm:gap-4"
              >
                {/* Enhanced Enquiry Button with blue theme */}
                <Button
                  className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-4 sm:px-6 md:px-8 py-2.5 sm:py-3 rounded-xl shadow-xl hover:shadow-blue-500/30 transition-all duration-300 flex items-center gap-2 text-sm sm:text-base md:text-lg font-semibold"
                  onClick={() => window.location.href = '/contact/Sales'}
                >
                  <Mail className="h-4 w-4 sm:h-5 sm:w-5" />
                  <span>Get Quote</span>
                </Button>
              </motion.div>

                {/* Enhanced modern feature highlights */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 1.2 }}
                  className="mt-8"
                >
                  <div className="grid grid-cols-2 gap-5">
                    {[
                      { label: "Correction Speed", value: "20,000 V/Sec", icon: "⚡", color: "from-blue-500 to-indigo-600" },
                      { label: "Regulation", value: "±1%", icon: "📊", color: "from-cyan-500 to-blue-600" },
                      { label: "Technology", value: "IGBT Based", icon: "🔌", color: "from-indigo-500 to-purple-600" },
                      { label: "Control", value: "DSP PWM", icon: "🎛️", color: "from-blue-500 to-sky-600" }
                    ].map((feature, idx) => (
                      <motion.div
                        key={idx}
                        whileHover={{ y: -5, scale: 1.03 }}
                        transition={{ type: "spring", stiffness: 400, damping: 10 }}
                        className="relative overflow-hidden rounded-xl bg-gradient-to-br from-white to-blue-50 dark:from-gray-800 dark:to-gray-900 shadow-lg border border-blue-100/50 dark:border-blue-800/30"
                      >
                        {/* Background gradient accent */}
                        <div className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${feature.color}`}></div>

                        {/* Content */}
                        <div className="p-4 flex flex-col">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-lg">{feature.icon}</span>
                            <span className="text-xs uppercase tracking-wider text-blue-600 dark:text-blue-400 font-semibold">{feature.label}</span>
                          </div>
                          <div className="ml-7">
                            <span className="text-xl font-bold text-gray-800 dark:text-white">{feature.value}</span>
                          </div>

                          {/* Subtle animated glow effect */}
                          <div className={`absolute -bottom-4 -right-4 w-16 h-16 rounded-full bg-gradient-to-tr ${feature.color} opacity-20 blur-xl`}
                            style={{
                              animation: `pulse-glow ${3 + idx * 0.5}s ease-in-out infinite alternate`
                            }}
                          ></div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              </motion.div>

            {/* Image Section - Now on the right with blue background theme */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative order-1 lg:order-2 mt-6 sm:mt-0"
            >
              {/* Blue gradient background similar to clampmeters */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-200 to-blue-50 rounded-full opacity-20 blur-xl transform scale-90"></div>

              <motion.div
                animate={{
                  y: [0, -10, 0],
                }}
                transition={{
                  repeat: Infinity,
                  duration: 3,
                  ease: "easeInOut"
                }}
                className="relative z-10 flex justify-center"
              >
                <div className="relative">
                  <img
                    src="/Static_stabilizers/G_2-removebg-preview.png"
                    alt="KRYKARD Static Voltage Regulator"
                    className="max-h-[300px] sm:max-h-[400px] md:max-h-[500px] lg:max-h-[600px] w-auto object-contain drop-shadow-2xl"
                  />
                </div>
              </motion.div>
            </motion.div>
            </div>
          </motion.div>
        </div>

        {/* Modern Product Types Section with clean card layout based on example image */}
        <section id="products" className="py-32 relative overflow-hidden bg-white dark:bg-slate-900">
          {/* Light background with subtle patterns - blue only */}
          <div className="absolute inset-0 -z-10 overflow-hidden">
            {/* Main background color */}
            <div className="absolute inset-0 bg-blue-50 dark:bg-blue-900"></div>

            {/* Subtle pattern overlay */}
            <div className="absolute inset-0 opacity-5"
              style={{
                backgroundImage: "radial-gradient(circle at 25px 25px, rgba(59, 130, 246, 0.2) 2px, transparent 0)",
                backgroundSize: "30px 30px"
              }}
            ></div>
          </div>

          <div className="container mx-auto px-4 relative">
            <motion.div
              className="text-center mb-16"
              initial={{ opacity: 0, y: -20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
            >
              <motion.h2
                className="relative inline-block mb-8"
                initial={{ y: 30, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <span className="text-5xl font-black tracking-tight bg-gradient-to-r from-blue-900 via-blue-700 to-blue-800 bg-clip-text text-transparent drop-shadow-md">
                  Static Voltage Regulator Types
                </span>
                <motion.div
                  className="absolute -z-10 inset-0 bg-blue-400/10 blur-xl rounded-full"
                  animate={{
                    scale: [0.95, 1.05, 0.95],
                    opacity: [0.3, 0.5, 0.3]
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                />
                <motion.div
                  className="absolute -bottom-2 left-0 h-1.5 bg-gradient-to-r from-blue-600 to-blue-500 rounded-full w-3/4 mx-auto"
                  initial={{ width: "0%" }}
                  whileInView={{ width: "60%" }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.8, delay: 0.7, ease: "easeOut" }}
                />
              </motion.h2>
              <motion.p
                className="text-center text-gray-900 dark:text-gray-100 max-w-3xl mx-auto mt-4 text-lg font-medium"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                Explore our range of high-performance static voltage regulators tailored for different applications
              </motion.p>
            </motion.div>

            {/* Enhanced Product Cards - Unique 3D-style layout with interactive elements */}
            <div className="max-w-6xl mx-auto space-y-16">
              {svrTypes.map((svr, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.7, delay: index * 0.1 }}
                  className="relative"
                >
                  {/* Floating orbs in background for decoration */}
                  <div className="absolute -z-10 inset-0">
                    <motion.div
                      className="absolute w-24 h-24 rounded-full bg-blue-400/10 blur-xl"
                      animate={{
                        x: [0, 10, 0],
                        y: [0, -10, 0],
                        scale: [1, 1.1, 1],
                        opacity: [0.3, 0.5, 0.3],
                      }}
                      transition={{
                        duration: 5,
                        repeat: Infinity,
                        repeatType: "reverse"
                      }}
                      style={{
                        top: "20%",
                        left: "5%",
                      }}
                    />
                    <motion.div
                      className="absolute w-20 h-20 rounded-full bg-blue-400/10 blur-xl"
                      animate={{
                        x: [0, -10, 0],
                        y: [0, 10, 0],
                        scale: [1, 1.2, 1],
                        opacity: [0.2, 0.4, 0.2],
                      }}
                      transition={{
                        duration: 6,
                        repeat: Infinity,
                        repeatType: "reverse"
                      }}
                      style={{
                        bottom: "10%",
                        right: "10%",
                      }}
                    />
                  </div>                  {/* Modern clean card design with enhanced 3D and glassmorphism effects */}
                  {/* Card with hover glow effect */}
                  <div className="relative group bg-white dark:bg-slate-800/95 backdrop-blur-sm rounded-2xl shadow-lg overflow-hidden border border-blue-100/50 dark:border-blue-800/50 transition-all duration-500 hover:shadow-xl">
                    {/* Hover glow animation */}
                    <motion.div
                      className="absolute -inset-0.5 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl opacity-0 group-hover:opacity-20 blur-xl transition-all duration-500 -z-10"
                      animate={{
                        opacity: [0, 0.2, 0],
                      }}
                      transition={{
                        duration: 5,
                        repeat: Infinity,
                        repeatType: "reverse"
                      }}
                    />                      {/* Premium badge removed */}
                        <div className={`flex flex-col md:flex-row md:min-h-[780px] ${index === 0 || index === 2 ? 'md:flex-row-reverse' : ''}`}>
                        {/* Content section - positioned based on card index */}
                        <div className="md:w-1/2 p-6 md:p-8">
                          {/* Enhanced modern title with animated underline effect */}
                          <div className="mb-8">
                            <div className="mb-6">
                              <motion.div
                                initial={{ x: -20, opacity: 0 }}
                                whileInView={{ x: 0, opacity: 1 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.5 }}
                                className="flex items-center gap-2 mb-1"
                              >
                                <div className="h-0.5 w-5 bg-blue-500 rounded-full"></div>
                                <span className="text-xs uppercase tracking-wider text-blue-600 dark:text-blue-400 font-bold">
                                  High Performance
                                </span>
                              </motion.div>

                              <motion.h3
                                className="text-3xl font-extrabold bg-gradient-to-r from-blue-900 via-blue-700 to-blue-800 dark:from-blue-500 dark:via-blue-300 to-blue-400 bg-clip-text text-transparent relative inline-block"
                                initial={{ opacity: 0, y: 10 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.5, delay: 0.1 }}
                              >
                                {svr.title}
                              </motion.h3>

                              <motion.div
                                className="h-1 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full mt-2 block"
                                initial={{ width: 0 }}
                                whileInView={{ width: "40%" }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.8, delay: 0.3 }}
                              />
                            </div>

                            {/* Feature icon badges */}
                            <div className="flex gap-2 mb-6">
                              {["⚡", "🔌", "🛡️"].map((icon, i) => (
                                <motion.div
                                  key={i}
                                  initial={{ opacity: 0, y: 10 }}
                                  whileInView={{ opacity: 1, y: 0 }}
                                  viewport={{ once: true }}
                                  transition={{ delay: 0.4 + (i * 0.1) }}
                                  className="w-8 h-8 rounded-lg bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center text-sm shadow-sm"
                                >
                                  {icon}
                                </motion.div>
                              ))}
                            </div>
                          </div>
                          {/* Modern description with stylish badge */}
                        <div className="relative mb-8">
                          <div className="absolute -left-4 top-0 w-1 h-full bg-gradient-to-b from-blue-500 via-blue-400 to-blue-500 rounded-full"></div>
                          <p className="text-gray-800 dark:text-gray-100 text-base leading-relaxed pl-3">
                            {svr.description}
                          </p>
                          <div className="absolute -left-4 bottom-0 w-8 h-8 rounded-full bg-gradient-to-br from-blue-400/10 to-blue-600/20 blur-xl"></div>
                        </div>

                        {/* Key Features with enhanced modern style */}
                        <div className="mb-6">
                          <h4 className="text-lg font-semibold text-gradient-blue mb-5 flex items-center gap-3">
                            <motion.div
                              className="p-1.5 rounded-lg bg-gradient-to-r from-blue-500/20 to-blue-600/20"
                              animate={{
                                boxShadow: ["0 0 0px rgba(59, 130, 246, 0.3)", "0 0 8px rgba(59, 130, 246, 0.6)", "0 0 0px rgba(59, 130, 246, 0.3)"]
                              }}
                              transition={{ duration: 2, repeat: Infinity }}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                              </svg>
                            </motion.div>
                            <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-blue-500 dark:from-blue-500 dark:to-blue-300">
                              KEY FEATURES
                            </span>
                          </h4>

                          <div className="grid grid-cols-1 gap-3">
                            {svr.features.map((feature, fidx) => (
                              <motion.div
                                key={fidx}
                                className="relative overflow-hidden bg-gradient-to-br from-white to-blue-50/50 dark:from-slate-800/60 dark:to-slate-800/30 rounded-xl p-3 border border-blue-100/30 dark:border-blue-800/30 shadow-sm hover:shadow-md transition-all duration-300 group"
                                whileHover={{ y: -3, boxShadow: "0 10px 25px -5px rgba(59, 130, 246, 0.15)" }}
                                initial={{ opacity: 0, y: 10 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true }}
                                transition={{ delay: fidx * 0.1, duration: 0.3 }}
                              >
                                <div className="flex items-start gap-3">
                                  <div className="shrink-0 w-7 h-7 rounded-lg bg-gradient-to-br from-blue-400/10 to-blue-600/20 flex items-center justify-center">
                                    <motion.div
                                      className="text-blue-600 dark:text-blue-400 text-sm"
                                      animate={{ scale: [1, 1.2, 1] }}
                                      transition={{ duration: 2, repeat: Infinity, delay: fidx * 0.3 }}
                                    >
                                      {fidx === 0 ? "⚡" : fidx === 1 ? "📊" : fidx === 2 ? "🔌" : fidx === 3 ? "🛡️" : fidx === 4 ? "⚙️" : "🔍"}
                                    </motion.div>
                                  </div>
                                  <p className="text-gray-800 dark:text-gray-100 font-medium">
                                    {feature}
                                  </p>
                                </div>

                                {/* Subtle animated accent */}
                                <div className="absolute -right-10 -bottom-10 w-20 h-20 rounded-full bg-gradient-to-r from-blue-400/5 to-blue-500/10 group-hover:from-blue-400/15 group-hover:to-blue-500/20 transition-all duration-300"></div>
                              </motion.div>
                            ))}
                          </div>
                        </div>
                          {/* View Details Button - Only show for Three Phase SVR */}
                        {svr.title === "Three Phase SVR" && (
                          <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true }}
                            transition={{ duration: 0.5, delay: 0.5 }}
                            className="mt-8"
                          >
                            <Link
                              to="/protect/static-stabilizers/product"
                              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg shadow-lg hover:shadow-blue-500/40 transition-all duration-300 group"
                            >
                              <span className="mr-2">View Details</span>
                              <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                            </Link>
                          </motion.div>
                        )}

                        <div className="mt-10">
                          <div className="border-t border-gray-100 dark:border-gray-700 pt-4">
                          </div>
                        </div>
                      </div>
                      {/* Product image on the right - enhanced with motion and modern style - larger container */}
                      <div className="md:w-1/2 bg-gradient-to-br from-blue-50 via-blue-50 to-blue-50 dark:from-blue-900/10 dark:via-blue-900 dark:to-blue-900/10 flex items-center justify-center p-0 rounded-r-2xl relative overflow-hidden">
                        {/* Blue orb decorations */}
                        <motion.div
                          className="absolute w-48 h-48 rounded-full bg-blue-400/10 blur-3xl"
                          animate={{
                            x: [0, 20, 0],
                            y: [0, -20, 0],
                          }}
                          transition={{
                            duration: 8,
                            repeat: Infinity,
                            repeatType: "reverse"
                          }}
                          style={{ top: '10%', right: '15%' }}
                        />
                        <motion.div
                          className="absolute w-32 h-32 rounded-full bg-blue-400/10 blur-3xl"
                          animate={{
                            x: [0, -15, 0],
                            y: [0, 15, 0],
                          }}
                          transition={{
                            duration: 7,
                            repeat: Infinity,
                            repeatType: "reverse"
                          }}
                          style={{ bottom: '15%', left: '20%' }}
                        />                        {/* Animated product image - increased size */}
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.7 }}
                          className="relative z-10 flex items-center justify-center w-full h-full py-8"
                        >                          <motion.img
                            src={svr.image}
                            alt={svr.title}
                            className="w-auto h-auto max-h-[800px] min-h-[650px] object-contain drop-shadow-2xl transform scale-125"
                            animate={{
                              y: [0, -12, 0],
                              scale: [1.0, 1.05, 1.0]
                            }}
                            transition={{
                              duration: 6,
                              repeat: Infinity,
                              repeatType: "reverse",
                              ease: "easeInOut"
                            }}
                            style={{
                              filter: 'drop-shadow(0 20px 30px rgba(59, 130, 246, 0.4))'
                            }}
                          />                          {/* Enhanced highlight glow effect for larger image */}
                          <motion.div
                            className="absolute inset-0 bg-blue-400/20 blur-3xl rounded-full"
                            animate={{
                              opacity: [0.3, 0.7, 0.3],
                              scale: [0.9, 1.2, 0.9],
                            }}
                            transition={{
                              duration: 5,
                              repeat: Infinity,
                              repeatType: "reverse"
                            }}
                            style={{ filter: 'blur(40px)' }}
                          />

                          {/* Additional spotlight effect */}
                          <div className="absolute -bottom-10 inset-x-0 h-40 bg-gradient-to-b from-blue-100/40 to-transparent dark:from-blue-500/20 rounded-full blur-2xl"></div>
                        </motion.div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Technical Specifications Section - Modern Design Update */}
        <motion.div
          ref={specRef}
          initial="hidden"
          animate={specControls}
          variants={{
            hidden: { opacity: 0 },
            visible: { opacity: 1, transition: { duration: 0.8 } }
          }}
          className="relative py-20 mb-32"
        >
          {/* Enhanced background with subtle gradients */}
          <div className="absolute inset-0 -z-10 bg-gradient-to-br from-blue-50 via-white to-blue-50"></div>

          {/* Subtle decorative elements */}
          <div className="absolute top-0 right-0 -z-10 w-full h-full overflow-hidden">
            <svg className="absolute top-0 right-0 w-1/2 h-auto text-blue-100 opacity-30 rotate-45" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
              <path fill="currentColor" d="M37.5,-48.1C52.1,-40.3,69.7,-33.7,75.2,-21.1C80.8,-8.6,74.2,9.9,65.4,25.4C56.5,40.9,45.3,53.4,31.6,58.8C17.9,64.2,1.7,62.5,-12.8,57.8C-27.4,53.1,-40.2,45.3,-51.6,33.9C-63,22.5,-72.9,7.4,-72.1,-7.5C-71.3,-22.4,-59.8,-37.1,-46.1,-45.2C-32.4,-53.3,-16.2,-54.8,-1.9,-52.5C12.5,-50.2,24.9,-44.1,37.5,-48.1Z" transform="translate(100 100)" />
            </svg>
          </div>

          <div className="container mx-auto px-4">
            {/* Modern section header */}
            <motion.div
              variants={{
                hidden: { opacity: 0, y: 20 },
                visible: {
                  opacity: 1,
                  y: 0,
                  transition: { duration: 0.6 }
                }
              }}
              className="text-center mb-16"
            >
              <div className="inline-block mb-3 relative">
                <span className="text-blue-600 font-semibold text-sm uppercase tracking-wider relative z-10">Technical Details</span>
                <div className="absolute -inset-1 bg-blue-100/50 rounded-full blur-sm -z-10 animate-pulse-glow"></div>
              </div>
              <h2 className="text-4xl md:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-blue-800 mb-4 animate-background-pan">
                Specifications
                <span className="relative ml-2 inline-block w-12">
                  <span className="absolute inset-x-0 bottom-2 h-3 bg-blue-200/60 animate-scale-pulse"></span>
                  <span className="relative animate-bounce-subtle">📋</span>
                </span>
              </h2>
              <div className="w-32 h-1 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full mx-auto mb-4 animate-scale-pulse"></div>
              <p className="text-gray-600 max-w-2xl mx-auto text-lg relative">
                <span className="relative">Detailed technical specifications for the <span className="text-blue-700 font-medium animate-color-cycle">KRYKARD Static Voltage Regulator</span></span>
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-10">
              {/* Left column - Input & Output */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="bg-white rounded-xl shadow-lg border border-blue-100 overflow-hidden"
              >
                {/* Input Section */}
                <div className="p-6 border-b border-blue-100">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center text-xl">
                      <span>⚡</span>
                    </div>
                    <h3 className="text-xl font-bold text-gray-900">Input</h3>
                  </div>

                  <ul className="space-y-4">
                    {[
                      "Input Voltage: 340V - 460V AC, 3 Phase",
                      "Frequency: 47-53 Hz",
                      "Power Factor: 0.8 Lag to Unity",
                      "Full Load Current: As per rating"
                    ].map((item, idx) => (
                      <li key={idx} className="flex items-start gap-3">
                        <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center mt-0.5">
                          <span className="text-blue-600 text-xs font-bold">{idx + 1}</span>
                        </div>
                        <span className="text-gray-800 font-medium">
                          {item}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Output Section */}
                <div className="p-6">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center text-xl">
                      <span>🔌</span>
                    </div>
                    <h3 className="text-xl font-bold text-gray-900">Output</h3>
                  </div>

                  <ul className="space-y-4">
                    {[
                      "Output Voltage: 400V AC ±1%, 3 Phase",
                      "Correction Speed: 20,000 V/Sec",
                      "Crest Factor: Up to 3:1",
                      "Load Power Factor: 0.5 Lag to 0.9 Lead"
                    ].map((item, idx) => (
                      <li key={idx} className="flex items-start gap-3">
                        <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center mt-0.5">
                          <span className="text-blue-600 text-xs font-bold">{idx + 1}</span>
                        </div>
                        <span className="text-gray-800 font-medium">
                          {item}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              </motion.div>

              {/* Right column - Physical & Standards */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                className="bg-white rounded-xl shadow-lg border border-blue-100 overflow-hidden"
              >
                {/* Physical Section */}
                <div className="p-6 border-b border-blue-100">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center text-xl">
                      <span>🔧</span>
                    </div>
                    <h3 className="text-xl font-bold text-gray-900">Physical</h3>
                  </div>

                  <ul className="space-y-4">
                    {[
                      "Cooling: Forced Air Cooling",
                      "Noise Level: < 55 dB",
                      "Protection Class: IP21",
                      "Operating Temperature: 0 to 40°C",
                      "Relative Humidity: 95% non-condensing"
                    ].map((item, idx) => (
                      <li key={idx} className="flex items-start gap-3">
                        <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center mt-0.5">
                          <span className="text-blue-600 text-xs font-bold">{idx + 1}</span>
                        </div>
                        <span className="text-gray-800 font-medium">
                          {item}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Standards Section */}
                <div className="p-6">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center text-xl">
                      <span>🏆</span>
                    </div>
                    <h3 className="text-xl font-bold text-gray-900">Standards</h3>
                  </div>

                  <ul className="space-y-4">
                    {[
                      "Safety: IEC 62040-1",
                      "EMC: IEC 62040-2",
                      "Performance: IEC 62040-3"
                    ].map((item, idx) => (
                      <li key={idx} className="flex items-start gap-3">
                        <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center mt-0.5">
                          <span className="text-blue-600 text-xs font-bold">{idx + 1}</span>
                        </div>
                        <span className="text-gray-800 font-medium">
                          {item}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              </motion.div>
            </div>

            {/* Additional note */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              className="mt-8 text-center"
            >
              <p className="text-gray-600 text-sm">
                * Specifications may vary based on model and configuration. Contact our team for detailed information.
              </p>
            </motion.div>
          </div>
        </motion.div>

        {/* Key Features Section - Modern Design Update */}
        <motion.div
          ref={featureRef}
          initial="hidden"
          animate={featureControls}
          variants={{
            hidden: { opacity: 0 },
            visible: { opacity: 1, transition: { duration: 0.8 } }
          }}
          className="relative py-20 mb-32"
        >
          {/* Enhanced background with subtle gradients */}
          <div className="absolute inset-0 -z-10 bg-gradient-to-br from-blue-50 via-white to-blue-50"></div>

          {/* Subtle decorative elements */}
          <div className="absolute top-0 right-0 -z-10 w-full h-full overflow-hidden">
            <svg className="absolute top-0 right-0 w-1/2 h-auto text-blue-100 opacity-30" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
              <path fill="currentColor" d="M37.5,-48.1C52.1,-40.3,69.7,-33.7,75.2,-21.1C80.8,-8.6,74.2,9.9,65.4,25.4C56.5,40.9,45.3,53.4,31.6,58.8C17.9,64.2,1.7,62.5,-12.8,57.8C-27.4,53.1,-40.2,45.3,-51.6,33.9C-63,22.5,-72.9,7.4,-72.1,-7.5C-71.3,-22.4,-59.8,-37.1,-46.1,-45.2C-32.4,-53.3,-16.2,-54.8,-1.9,-52.5C12.5,-50.2,24.9,-44.1,37.5,-48.1Z" transform="translate(100 100)" />
            </svg>
            <svg className="absolute bottom-0 left-0 w-1/2 h-auto text-blue-100 opacity-30" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
              <path fill="currentColor" d="M45.3,-58.9C58.9,-48.7,70.2,-34.1,73.8,-17.8C77.4,-1.5,73.3,16.5,64.3,30.8C55.3,45.1,41.5,55.7,25.9,62.8C10.3,69.9,-7.2,73.5,-23.6,69.7C-40,65.9,-55.3,54.7,-65.1,39.5C-74.9,24.3,-79.2,5.1,-75.6,-12.3C-72,-29.7,-60.5,-45.3,-46.2,-55.5C-31.9,-65.7,-15.9,-70.5,0.5,-71.1C16.9,-71.8,33.7,-68.3,45.3,-58.9Z" transform="translate(100 100)" />
            </svg>
          </div>

          <div className="container mx-auto px-4">
            {/* Modern section header */}
            <motion.div
              variants={{
                hidden: { opacity: 0, y: 20 },
                visible: {
                  opacity: 1,
                  y: 0,
                  transition: { duration: 0.6 }
                }
              }}
              className="text-center mb-16"
            >
              <div className="inline-block mb-3">
                <span className="text-blue-600 font-semibold text-sm uppercase tracking-wider">Superior Quality</span>
              </div>
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                Key Features
                <span className="relative ml-2 inline-block w-12">
                  <span className="absolute inset-x-0 bottom-2 h-3 bg-blue-200/60"></span>
                  <span className="relative">✨</span>
                </span>
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto text-lg">
                Advanced technology for superior voltage regulation and protection
              </p>
            </motion.div>

            {/* Modern feature cards layout */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {keyFeatures.map((feature, index) => {
                // Determine icon based on feature content
                const getIcon = () => {
                  if (feature.includes("DSP") || feature.includes("PWM")) return "🎛️";
                  if (feature.includes("efficiency")) return "⚡";
                  if (feature.includes("Correction")) return "⚡";
                  if (feature.includes("moving parts")) return "🔧";
                  if (feature.includes("regulation")) return "📊";
                  if (feature.includes("distortion")) return "〰️";
                  if (feature.includes("IGBT")) return "🔌";
                  if (feature.includes("Protection")) return "🛡️";
                  if (feature.includes("bypass")) return "↪️";
                  if (feature.includes("LCD")) return "🖥️";
                  if (feature.includes("Silent")) return "🔇";
                  return "✓";
                };

                return (
                  <motion.div
                    key={index}
                    variants={{
                      hidden: { opacity: 0, y: 20 },
                      visible: {
                        opacity: 1,
                        y: 0,
                        transition: {
                          duration: 0.5,
                          delay: index * 0.1,
                          ease: "easeOut"
                        }
                      }
                    }}
                    whileHover={{ y: -8 }}
                    transition={{ type: "spring", stiffness: 300, damping: 15 }}
                    className="bg-white rounded-xl p-6 shadow-lg border border-blue-100 hover:shadow-xl"
                  >
                    <div className="flex items-start gap-4">
                      <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center text-2xl">
                        <span>{getIcon()}</span>
                      </div>

                      <div className="flex-1">
                        <p className="text-gray-800 font-medium text-lg">
                          {feature}
                        </p>

                        {/* Animated line */}
                        <div className="w-12 h-1 bg-blue-500 rounded-full mt-3 mb-2"></div>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>


          </div>
        </motion.div>

        {/* Technical Overview Section - Modern Design Update */}
        <motion.div
          ref={techRef}
          initial="hidden"
          animate={techControls}
          variants={{
            hidden: { opacity: 0 },
            visible: { opacity: 1, transition: { duration: 0.8 } }
          }}
          className="relative py-20 mb-32"
        >
          {/* Enhanced background with subtle gradients */}
          <div className="absolute inset-0 -z-10 bg-gradient-to-br from-blue-50 via-white to-blue-50"></div>

          {/* Subtle decorative elements */}
          <div className="absolute top-0 right-0 -z-10 w-full h-full overflow-hidden">
            <svg className="absolute top-0 left-0 w-1/2 h-auto text-blue-100 opacity-30 rotate-180" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
              <path fill="currentColor" d="M37.5,-48.1C52.1,-40.3,69.7,-33.7,75.2,-21.1C80.8,-8.6,74.2,9.9,65.4,25.4C56.5,40.9,45.3,53.4,31.6,58.8C17.9,64.2,1.7,62.5,-12.8,57.8C-27.4,53.1,-40.2,45.3,-51.6,33.9C-63,22.5,-72.9,7.4,-72.1,-7.5C-71.3,-22.4,-59.8,-37.1,-46.1,-45.2C-32.4,-53.3,-16.2,-54.8,-1.9,-52.5C12.5,-50.2,24.9,-44.1,37.5,-48.1Z" transform="translate(100 100)" />
            </svg>
            <svg className="absolute bottom-0 right-0 w-1/2 h-auto text-blue-100 opacity-30 rotate-90" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
              <path fill="currentColor" d="M45.3,-58.9C58.9,-48.7,70.2,-34.1,73.8,-17.8C77.4,-1.5,73.3,16.5,64.3,30.8C55.3,45.1,41.5,55.7,25.9,62.8C10.3,69.9,-7.2,73.5,-23.6,69.7C-40,65.9,-55.3,54.7,-65.1,39.5C-74.9,24.3,-79.2,5.1,-75.6,-12.3C-72,-29.7,-60.5,-45.3,-46.2,-55.5C-31.9,-65.7,-15.9,-70.5,0.5,-71.1C16.9,-71.8,33.7,-68.3,45.3,-58.9Z" transform="translate(100 100)" />
            </svg>
          </div>

          <div className="container mx-auto px-4">
            {/* Modern section header */}
            <motion.div
              variants={{
                hidden: { opacity: 0, y: 20 },
                visible: {
                  opacity: 1,
                  y: 0,
                  transition: { duration: 0.6 }
                }
              }}
              className="text-center mb-16"
            >
              <div className="inline-block mb-3 relative">
                <span className="text-blue-600 font-semibold text-sm uppercase tracking-wider relative z-10">Advanced Technology</span>
                <div className="absolute -inset-1 bg-blue-100/50 rounded-full blur-sm -z-10 animate-pulse-glow"></div>
              </div>
              <h2 className="text-4xl md:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-blue-800 mb-4 animate-background-pan">
                Technical Overview
                <span className="relative ml-2 inline-block w-12">
                  <span className="absolute inset-x-0 bottom-2 h-3 bg-blue-200/60 animate-scale-pulse"></span>
                  <span className="relative animate-bounce-subtle">⚡</span>
                </span>
              </h2>
              <div className="w-32 h-1 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full mx-auto mb-4 animate-scale-pulse"></div>
              <p className="text-gray-600 max-w-2xl mx-auto text-lg relative">
                <span className="relative">Cutting-edge technology for <span className="text-blue-700 font-medium animate-color-cycle">superior performance</span> and <span className="text-blue-700 font-medium animate-color-cycle" style={{ animationDelay: '2s' }}>reliability</span></span>
              </p>
            </motion.div>

            {/* Modern overview card */}
            <motion.div
              variants={{
                hidden: { opacity: 0, y: 20 },
                visible: {
                  opacity: 1,
                  y: 0,
                  transition: { duration: 0.6, delay: 0.2 }
                }
              }}
              className="bg-white rounded-xl p-8 shadow-lg border border-blue-100 mb-16"
            >              {/* Redesigned Advanced Switching Technology section to fix overlapping content */}
              <div className="flex flex-col gap-6">
                {/* Title section with improved layout */}
                <div className="flex items-center gap-4">
                  <div className="flex-shrink-0 w-16 h-16 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center text-2xl shadow-lg relative group animate-border-glow">
                    <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-300/0 via-blue-300/30 to-blue-300/0 opacity-0 group-hover:opacity-100 animate-shimmer"></div>
                    <div className="absolute -inset-2 bg-blue-400/10 rounded-full blur-md animate-pulse-glow"></div>
                    <span className="animate-bounce-subtle">🔌</span>
                  </div>
                  <h3 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-blue-800 animate-background-pan">Advanced Switching Technology</h3>
                </div>

                {/* Content section with improved spacing */}
                <div className="space-y-6 pl-2">
                  {/* First paragraph - AC-AC PWM topology */}
                  <div className="flex gap-5">
                    <div className="flex-shrink-0 w-1 bg-blue-100 rounded-full"></div>
                    <p className="text-gray-800 text-lg leading-relaxed">
                      The KRYKARD SVR uses a new switching topology where PWM is made directly in AC-to-AC switching, without any harmonic distortion. Our{" "}
                      <span className="text-blue-700 font-medium inline-flex items-center">
                        <span>AC-AC PWM topology</span>
                      </span>{" "}
                      offers simplicity in design and reliability in operation due to lower component count and improves the efficiency and reliability.
                    </p>
                  </div>

                  {/* Second paragraph - Correction speed */}
                  <div className="flex gap-5">
                    <div className="flex-shrink-0 w-1 bg-blue-100 rounded-full"></div>
                    <p className="text-gray-800 text-lg leading-relaxed">
                      With instantaneous correction speed of{" "}
                      <span className="text-blue-700 font-medium">20,000 V/Sec</span>, the KRYKARD Static Voltage Regulator provides superior protection for sensitive equipment against voltage fluctuations.
                    </p>
                  </div>
                </div>

                {/* Interactive hover effect */}
                <div className="w-0 hover:w-40 h-1 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full mt-2 ml-20 transition-all duration-1000"></div>
              </div>
            </motion.div>

            {/* Technical specifications with modern cards */}
            <div className="mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">Key Technical Specifications</h3>

              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
                {[
                  { label: "Topology", value: "AC-AC PWM", icon: "⚡" },
                  { label: "Control", value: "DSP Based", icon: "🎛️" },
                  { label: "Technology", value: "IGBT Power Modules", icon: "🔌" },
                  { label: "Response Time", value: "<10ms", icon: "⚡" },
                  { label: "Efficiency", value: ">98%", icon: "📊" },
                  { label: "Display", value: "LCD Interface", icon: "🖥️" }
                ].map((spec, idx) => (
                  <motion.div
                    key={idx}
                    variants={{
                      hidden: { opacity: 0, y: 20, scale: 0.95 },
                      visible: {
                        opacity: 1,
                        y: 0,
                        scale: 1,
                        transition: {
                          duration: 0.5,
                          delay: idx * 0.1,
                          ease: "easeOut"
                        }
                      }
                    }}
                    whileHover={{ y: -8 }}
                    transition={{ type: "spring", stiffness: 300, damping: 15 }}
                    className="bg-white rounded-xl p-5 shadow-md border border-blue-100 hover:shadow-lg hover:border-blue-200 transition-all duration-300 flex flex-col items-center text-center"
                  >
                    <div className="w-14 h-14 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center text-2xl mb-3 shadow-md group-hover:shadow-blue-300/30 transition-all duration-300 relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-300/0 via-blue-300/30 to-blue-300/0 opacity-0 group-hover:opacity-100 animate-shimmer"></div>
                      <span className="group-hover:scale-125 transition-transform duration-500 animate-bounce-subtle">{spec.icon}</span>
                    </div>

                    <h4 className="text-xl font-bold text-gray-900 mb-1 group-hover:text-blue-700 transition-colors duration-300">{spec.value}</h4>
                    <p className="text-sm text-gray-600 font-medium group-hover:text-blue-600/80 transition-colors duration-300">{spec.label}</p>

                    <div className="w-0 group-hover:w-20 h-1 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full mt-3 transition-all duration-500"></div>
                  </motion.div>
                ))}
              </div>
            </div>


          </div>
        </motion.div>

        {/* Contact Section - Updated to match the provided design */}
        <motion.div
          ref={ctaRef}
          initial="hidden"
          animate={ctaControls}
          variants={{
            hidden: { opacity: 0 },
            visible: { opacity: 1, transition: { duration: 0.8 } }
          }}
          className="container mx-auto px-4 mb-24"
        >
          <motion.div
            className="relative"
            variants={{
              hidden: { opacity: 0, y: 40 },
              visible: { opacity: 1, y: 0, transition: { duration: 0.7 } }
            }}
          >
            {/* Enhanced contact section background with animations */}
            <div className="relative bg-gradient-to-br from-blue-50 via-blue-100/50 to-blue-50 rounded-3xl p-12 text-center shadow-lg overflow-hidden animate-gradient-shift">
              {/* Animated decorative elements */}
              <div className="absolute top-0 left-0 w-full h-full overflow-hidden -z-10">
                <div className="absolute top-10 right-10 w-40 h-40 bg-blue-300/10 rounded-full blur-xl animate-pulse-glow"></div>
                <div className="absolute bottom-10 left-10 w-40 h-40 bg-blue-400/10 rounded-full blur-xl animate-pulse-glow-reverse"></div>
                <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 border border-blue-200/30 rounded-full animate-rotate-slow"></div>
                <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-64 h-64 border border-blue-200/20 rounded-full animate-rotate-slow" style={{ animationDirection: 'reverse', animationDuration: '25s' }}></div>
              </div>

              <div className="relative z-10">
                <h2 className="text-4xl font-bold mb-4 text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-blue-800 animate-background-pan">
                                   Need More Information?
                </h2>

                <div className="w-24 h-1 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full mx-auto mb-6 animate-scale-pulse"></div>

                <p className="text-lg mb-8 max-w-3xl mx-auto text-blue-700/80 leading-relaxed">
                  Our team of experts is ready to help you with product specifications, custom solutions, pricing, and any other
                  details you need about the <span className="font-semibold text-blue-700 animate-color-cycle">KRYKARD Static Voltage Regulator</span> systems.
                </p>                <div className="flex justify-center">                  {/* Direct anchor tag button for reliable navigation */}                  <a
                    href="/contact/Sales"
                    className="bg-gradient-to-r from-blue-500 to-blue-700 hover:from-blue-600 hover:to-blue-800 text-white shadow-lg hover:shadow-blue-500/40 transition-all duration-300 text-base px-8 py-3 rounded-lg flex items-center gap-3 animate-border-glow"
                    onClick={(e) => {
                      e.preventDefault();
                      window.location.href = '/contact/Sales';
                    }}
                  >
                    <div className="relative">
                      <div className="absolute inset-0 bg-white rounded-full opacity-0 hover:opacity-20 animate-ripple"></div>
                      <Mail className="h-5 w-5 group-hover:scale-110 transition-transform duration-300" />
                    </div>
                    <span className="font-medium text-shadow-blue">Contact Our Experts</span>
                  </a>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </PageLayout>
  );
};

export default StaticStabilizers;