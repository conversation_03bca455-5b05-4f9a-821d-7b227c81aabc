import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Link, useNavigate, useLocation } from "react-router-dom";
import PageLayout from "@/components/layout/PageLayout";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  ArrowRight,
  ChevronRight,
  Thermometer,
  Wind,
  Sun,
  MessageSquare,
  Factory,
  Droplets,
  Shield,
  Activity,
  Zap,
  Battery,
  BarChart2,
  Database,
  Settings,
  Layers,
  Check,
  FileText,
  Download,
  Gauge,
  ExternalLink
} from "lucide-react";

// PDF URL for brochure
const PDF_URL = "/T&M April 2025.pdf";

// Modern Background Component
const ModernBackground = () => {
  return (
    <div className="fixed inset-0 pointer-events-none z-[-1] overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Abstract shapes */}
      <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-yellow-100 rounded-bl-full opacity-30"></div>
      <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-yellow-200 rounded-tr-full opacity-20"></div>

      {/* Subtle grid pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
    </div>
  );
};

// Enhanced Hero Section Component with updated buttons
const HeroSection = ({ onRequestDemo, onViewBrochure }: { onRequestDemo: () => void; onViewBrochure: () => void }) => {
  return (
    <div className="relative py-6 md:py-12 overflow-hidden font-['Open_Sans']">
      {/* Hero Background Elements - Mobile optimized */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 right-0 w-1/2 md:w-3/4 h-full bg-yellow-50 rounded-bl-[50px] md:rounded-bl-[100px] transform -skew-x-6 md:-skew-x-12"></div>
        <div className="absolute bottom-20 left-0 w-32 h-32 md:w-64 md:h-64 bg-yellow-400 rounded-full opacity-10"></div>
      </div>

      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 items-center">
          {/* Text Content */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-4 text-center lg:text-left"
          >
            <div className="inline-block bg-yellow-400 py-1 px-3 rounded-full mb-2">
              <span className="text-sm md:text-base font-semibold text-gray-900 font-['Open_Sans']">KRYKARD Precision Instruments</span>
            </div>
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight font-['Open_Sans']">
              MULTI <span className="text-yellow-400">FUNCTIONAL</span> METERS
            </h1>

            <p className="text-base md:text-lg lg:text-xl text-gray-900 leading-relaxed font-medium text-center lg:text-justify max-w-lg mx-auto lg:mx-0 font-['Open_Sans']">
              Advanced energy measurement solutions for comprehensive monitoring and analysis.
            </p>

            <div className="pt-4 flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center lg:justify-start max-w-md sm:max-w-none mx-auto lg:mx-0">
              <Button
                className="w-full sm:w-auto px-4 sm:px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-semibold rounded-lg shadow-md transition duration-300 flex items-center justify-center space-x-2 text-sm sm:text-base"
                onClick={onRequestDemo}
              >
                <span>Request Demo</span>
                <ArrowRight className="ml-2 h-4 sm:h-5 w-4 sm:w-5" />
              </Button>
              <Button
                className="w-full sm:w-auto px-4 sm:px-6 py-3 bg-white border-2 border-yellow-400 text-gray-900 font-semibold rounded-lg shadow-sm transition duration-300 hover:bg-gray-50 flex items-center justify-center space-x-2 text-sm sm:text-base"
                onClick={onViewBrochure}
              >
                <span>View Brochure</span>
                <FileText className="ml-2 h-4 sm:h-5 w-4 sm:w-5" />
              </Button>
            </div>
          </motion.div>

          {/* Product Image */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative order-first lg:order-last"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-200 to-yellow-50 rounded-full opacity-20 blur-xl transform scale-90"></div>
            <motion.div
              animate={{
                y: [0, -10, 0],
              }}
              transition={{
                repeat: Infinity,
                duration: 3,
                ease: "easeInOut"
              }}
              className="relative z-10 flex justify-center"
            >
              <img
                src="/Multifunctional meters/Measure-Power-removebg-preview.png"
                alt="Krykard Multi Functional Meter"
                className="max-h-[300px] sm:max-h-[400px] md:max-h-[500px] lg:max-h-[600px] w-auto object-contain drop-shadow-2xl"
              />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

// Animated Product Card with improved styling
const ProductCard = ({
  title,
  modelInfo,
  image,
  features,
  measurements,
  colors = {
    primary: 'yellow-400',
    secondary: 'yellow-50'
  },
  onViewDetailsClick
}: {
  title: string;
  modelInfo: string;
  image: string;
  features: string[];
  measurements: string[];
  colors?: {
    primary: string;
    secondary: string;
  };
  onViewDetailsClick?: () => void;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="group h-full font-['Open_Sans']"
    >
      <div className={`h-full rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 bg-white border border-gray-100`}>
        {/* Product Image with Colored Background */}
        <div className={`relative p-3 md:p-4 flex justify-center items-center bg-yellow-50 h-48 md:h-56 overflow-hidden group-hover:bg-opacity-80 transition-all duration-700`}>
          <motion.img
            src={image || "/assets/meters/digi760.png"}
            alt={title}
            className="h-40 md:h-48 object-contain z-10 drop-shadow-xl"
            whileHover={{ rotate: [-1, 1, -1], transition: { repeat: Infinity, duration: 2 } }}
          />
          <div className={`absolute top-3 left-3 bg-yellow-400 text-white text-xs font-bold py-1 px-3 rounded-full`}>
            {modelInfo || title}
          </div>
        </div>

        {/* Product Content */}
        <div className="p-3 md:p-4 space-y-2 md:space-y-3">
          <h3 className="text-base md:text-lg lg:text-xl font-bold text-gray-900 group-hover:text-yellow-600 transition-colors duration-300 font-['Open_Sans']">
            {title}
          </h3>

          {/* Key Features */}
          <div className="space-y-1">
            {features.slice(0, 3).map((feature, idx) => (
              <div key={idx} className="flex items-start">
                <Check className="h-4 w-4 text-yellow-400 mt-1 mr-2 flex-shrink-0" />
                <span className="text-gray-900 text-sm font-medium font-['Open_Sans']">{feature}</span>
              </div>
            ))}
          </div>

          {/* Measurement Badge */}
          <div className="flex flex-wrap gap-1 pt-2">
            {measurements.slice(0, 2).map((measurement, idx) => (
              <span key={idx} className="inline-block bg-gray-100 rounded-full px-2 py-1 text-xs font-semibold text-gray-700 truncate max-w-full">
                {typeof measurement === 'string' ? measurement.split(' - ')[0] : measurement}
              </span>
            ))}
          </div>

          {/* View Details Button */}
          <Button
            onClick={() => onViewDetailsClick && onViewDetailsClick()}
            className={`w-full mt-3 py-3 px-4 bg-yellow-400 hover:bg-yellow-500 text-center font-semibold text-gray-900 rounded-lg transition-all duration-300 transform group-hover:-translate-y-1 flex items-center justify-center text-sm md:text-base`}
          >
            <span>View Details</span>
            <ChevronRight className="ml-1 h-4 w-4" />
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

// Feature Highlight Component with improved styling
const FeatureHighlight = ({ icon, title, description }: { icon: React.ReactNode; title: string; description: string }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
      whileHover={{ y: -8, boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)" }}
      className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 p-4 h-full border-b-4 border-yellow-400 font-['Open_Sans']"
    >
      <div className="flex flex-col h-full text-center md:text-left">
        <div className="bg-gradient-to-br from-yellow-400 to-yellow-300 w-12 h-12 rounded-lg flex items-center justify-center mb-3 shadow-md mx-auto md:mx-0">
          {icon}
        </div>
        <h3 className="text-base md:text-lg lg:text-xl font-bold text-gray-900 mb-2 font-['Open_Sans']">{title}</h3>
        <p className="text-gray-900 flex-grow font-medium text-sm md:text-base text-center md:text-justify font-['Open_Sans']">{description}</p>
      </div>
    </motion.div>
  );
};

// Product Tab Content Component
const ProductTabContent = ({ activeProduct, activeTab }: { activeProduct: string; activeTab: string }) => {
  // Features and measurements data for each product
  const productData = {
    // DiGi 530S/530H
    "digi530": {
      features: [
        "Accuracy Class 0.5s as per IEC 62053-22",
        "3-line bright LCD display",
        "PT & CT programmable",
        "Password protection for setup page",
        "Load management feature",
        "RS485 port with Modbus RTU protocol and baud rate upto 38,400bps",
        "Dimension: 96mm x 96mm x 83mm",
        "Cut-Out: 90 x 90 mm (+/- 0.5mm)"
      ],
      measurements: [
        { label: "RMS Values", value: "V, U, I, In, Hz" },
        { label: "Power", value: "kW, kVAr, kVA" },
        { label: "PF", value: "Phase wise & Total" },
        { label: "Min, Max", value: "V, I, Hz, kW" },
        { label: "Energy", value: "kWh, kVAh, kVArh with bi-directional measurements" },
        { label: "THD", value: "Voltage & Current" },
        { label: "Harmonics", value: "Individual Harmonics upto 31st order (DiGi 530H)" },
        { label: "Demand", value: "I, kW, kVAr, kVA (DiGi 530H)" }
      ]
    },
    // DiGi 630S/630D
    "digi630": {
      features: [
        "Accuracy Class 0.5s / Class 0.2s as per IEC 62053-22",
        "3-line bright LCD display",
        "PT & CT programmable",
        "Password protection for setup page",
        "4 DI, 2 DO (DiGi 630D)",
        "Screen shot recording for energy accounting",
        "RS485 port with Modbus RTU protocol and Baud rate upto 38,400 bps",
        "Modbus custom table",
        "Dimension: 96mm x 96mm x 83mm",
        "Cut-Out: 90 x 90 mm (+/- 0.5mm)"
      ],
      measurements: [
        { label: "RMS Values", value: "V, U, I, In, Hz" },
        { label: "Power", value: "kW, kVAr, kVA, PF - Phase wise & Total" },
        { label: "Min, Max", value: "V, I, Hz, kW (Real time stamping)" },
        { label: "Energy", value: "kWh, kVAh, kVArh with bi-directional, 4 Quadrant capability" },
        { label: "Dual Source", value: "Energy meter functionality" },
        { label: "Tariff", value: "Energy recording" },
        { label: "THD", value: "Voltage & Current" },
        { label: "Harmonics", value: "Individual Harmonics upto 31st order" },
        { label: "Demand", value: "I, kW, kVAr, kVA with control in DiGi 630D" },
        { label: "Load Management", value: "Feature included" }
      ]
    },
    // DiGi 730S/730D
    "digi730": {
      features: [
        "Accuracy Class 0.5s /class 0.2s as per IEC 62053-22",
        "3-line bright LCD display",
        "PT & CT programmable",
        "Password protection for setup page",
        "Load management feature",
        "6 Digital inputs",
        "3DO & 1AO (only in DiGi 730D)",
        "Screen shot recording for energy accounting",
        "RS485 port with Modbus RTU protocol and baud rate upto 115,200 bps for faster communication",
        "Modbus custom table",
        "Dimension: 96mm x 96mm x 83mm",
        "Cut-Out: 90 x 90 mm (+/- 0.5mm)"
      ],
      measurements: [
        { label: "RMS Values", value: "V, U, I, In, Hz" },
        { label: "Power", value: "kW, kVAr, kVA, PF - Phase wise & Total" },
        { label: "Min, Max", value: "V, I, Hz, kW (Real time stamping)" },
        { label: "Energy", value: "kWh, kVAh, kVArh with bi-directional, 4-Quadrant capability" },
        { label: "Dual Source", value: "Energy meter functionality" },
        { label: "Tariff", value: "Energy Recording (6 rates)" },
        { label: "THD", value: "Voltage & Current" },
        { label: "Harmonics", value: "Individual Harmonics upto 51st order" },
        { label: "Advanced Metrics", value: "Crest factor and K factor measurements" },
        { label: "Demand", value: "I, kW, kVAr, kVA with control in DiGi 730D" }
      ]
    },
    // DiGi 760
    "digi760": {
      features: [
        "Accuracy Class 0.2s as per IEC 62053-22",
        "TFT colorful LCD display with 320 x 240 resolution",
        "Real Time waveform Display",
        "CT/PT Programmable",
        "Real-time-clock for Min, Max, Time stamping",
        "Voltage swell/sag event recording with waveform",
        "Voltage input upto 480V",
        "4AO, 8DI (Optional)",
        "Ethernet Port with Modbus TCP/IP protocol",
        "Modbus RS485 communication",
        "Dimension: 96 x 96 x 66mm",
        "Cut-Out: 90 x 90 mm (+/- 0.5mm)"
      ],
      measurements: [
        { label: "RMS Values", value: "V, U, I, In, Hz" },
        { label: "Voltage", value: "3U, 3V" },
        { label: "Current", value: "3I, In" },
        { label: "Power", value: "kW, kVA, kVAr – phase wise and total" },
        { label: "Energy", value: "kWh, kVAh, kVArh with bi-directional measurements" },
        { label: "Energy Quadrants", value: "4-Quadrant Energy Data (on communication)" },
        { label: "THD", value: "Voltage & Current" },
        { label: "Harmonics", value: "Individual harmonics on V & I upto 63rd order" },
        { label: "Advanced Metrics", value: "K-Factor, Crest Factor measurements" },
        { label: "Time of Use", value: "2 Tariff rates, 8 periods & 2 lists of tariff rate" },
        { label: "Demand", value: "I, kW, kVAr, kVA" }
      ]
    },
    // DiGi 820
    "digi820": {
      features: [
        "IEC 61000-4-30 Class A power quality monitoring meter",
        "Class 0.2s high accuracy energy measurement",
        "Sampling rate: 1024 samples/cycle",
        "Color LCD Display with resolution of 640 x 480",
        "Real-Time waveform display",
        "Power quality analysis - sag, swell, Transient, RVC",
        "8DI, 2DO (Pulse) & 4 Relay",
        "Baud rate: 2400~38400bps",
        "Ethernet Port with Modbus TCP/IP protocol",
        "Modbus RS485 communication",
        "Memory: 8GB for data and event recording"
      ],
      measurements: [
        { label: "RMS Values", value: "V, U, I, In, Hz" },
        { label: "Voltage", value: "Phase, Line, Average & Phase Angle" },
        { label: "Current", value: "Phase, Average Current & Phase Angle" },
        { label: "Power", value: "kW, kVAr, kVA & PF: Phase & Total Power" },
        { label: "Energy", value: "4-Quadrant energy measurements" },
        { label: "Harmonics", value: "Harmonics & Inter-harmonics upto 63rd order" },
        { label: "Harmonic Energy", value: "Up to 31st order" },
        { label: "PQ Events", value: "Records 256 PQ events along with waveform" },
        { label: "Transients", value: "Captures 20μs Voltage transients" },
        { label: "Multi-tariff", value: "4 tariff, 8 time period, 2 time zone" },
        { label: "Demand", value: "I, kW, kVAr & kVA monitoring & control" }
      ]
    },
    // Multy4 (AC Multi-Channel)
    "multy4": {
      features: [
        "Backlit custom LCD display",
        "Accuracy Class 1.0 as per IEC 62053-21 (accuracy includes CT errors)",
        "Voltage input upto 500 V",
        "CT primary programmable 2kV isolation",
        "Password protection",
        "Load management module (on communication)",
        "Special high-accuracy CT supplied with the meter",
        "RS485-modbus RTU Protocol",
        "Dimension: 75mm (width) x 94mm (height) x 62mm (depth)"
      ],
      measurements: [
        { label: "Configuration", value: "Single phase/Three phase: 12 x 1 phase / 4 x 3 phase" },
        { label: "Common for 4 channels", value: "Voltages L-N and L-L (C) & Frequency" },
        { label: "Voltage Min and Max", value: "L-N and L-L (C)" },
        { label: "Per Channel Current", value: "Line current for each channel" },
        { label: "Max Current", value: "Phase-wise (C) for each channel" },
        { label: "Power", value: "kW, kVAr, kVA & PF: Phase and Total for each channel" },
        { label: "Energy", value: "kWh, kVAh: Phase and Total for each channel" }
      ]
    },
    // PLM R90 (1Φ DC Energy Meter)
    "plmr90": {
      features: [
        "35mm DIN rail installing, standard DIN ED5002",
        "Accuracy: Class 0.5",
        "LED indicates pulse output",
        "RS485 port, MODBUS-RTU or DLT/T645 protocol (Optional)",
        "Dimension: 100 x 36 x 65mm (2 module)"
      ],
      measurements: [
        { label: "RMS Values", value: "V, I" },
        { label: "Power", value: "kW" },
        { label: "Energy", value: "kWh" },
        { label: "Shunt", value: "100A, 200A, 300A" }
      ]
    },
    // PLM R91 (1Φ AC Energy Meter)
    "plmr91": {
      features: [
        "35mm DIN installing in accordance with the standard DIN ED5002",
        "Active energy accuracy upto Class 1.0 as per IEC 62053-21",
        "6+1 digit LCD display (999999.9 kWh)",
        "Passive pulse o/p, O/p signal is in accordance with the standard DIN43864",
        "LED indicates pulse O/p",
        "RS485 port, Modbus-RTU or DTL645 (optional)",
        "Dimension: 100 x 36 x 65mm"
      ],
      measurements: [
        { label: "RMS Values", value: "V, I, Hz" },
        { label: "Power", value: "kW, kVAr, kVA, PF" },
        { label: "Energy", value: "kWh, kVArh" },
        { label: "Max Current", value: "63A" }
      ]
    },
    // PLM R93 (3Φ AC Energy Meter)
    "plmr93": {
      features: [
        "35mm DIN in accordance with the standard ED5002",
        "kWh Class 1.0 as per IEC 62053-21",
        "7+1 digit LCD display (9999999.9 kWh)",
        "2 LED Pulse indicators for kWh & kVArh",
        "Displays for phase sequence error indication",
        "RS485 port with Modbus-RTU protocol and baud rate of 2400, 4800, 9600",
        "Dimension: 72 x 100 x 65mm, Cut-Out: 50 x 50mm"
      ],
      measurements: [
        { label: "RMS Values", value: "V, I, Hz" },
        { label: "Power", value: "kW, kVAr, kVA, PF" },
        { label: "Energy", value: "kWh, kVArh with bidirectional measurement" },
        { label: "Max Current", value: "63A" },
        { label: "Multi-tariff", value: "Energy values" },
        { label: "Additional Parameters", value: "kVAr & kVA available on communication" },
        { label: "Historical Data", value: "Present day data (15 mins), Last 31 days, Last 12 months, Last 10 years" }
      ]
    },
    // EON 4.0
    "eon40": {
      features: [
        "Active energy accuracy upto Class 0.5",
        "PT/CT programmable",
        "Password protection for setup page",
        "Auto Phase sequence adjustment",
        "2 DI Standard, 2 DO (optional)",
        "Baud rate: 4800 to 38400bps",
        "Memory: Built in memory, 7 days data storage for 5 mins data (EON 4.0G & EON 4.0E)",
        "Gateway function (EON 4.0 G & EON 4.0E)",
        "Communication: EON 4.0S: RS 485 Modbus-RTU protocol, EON 4.0G: 4G wireless for MQTT, EON 4.0E: LAN RJ45 LAN port for Modbus TCP/HTTP/MQTT"
      ],
      measurements: [
        { label: "RMS Values", value: "V, I, Hz, V & I phase angle" },
        { label: "Power", value: "kW, kVAR, kVA & PF" },
        { label: "Energy", value: "kWh, kVArh, kVAh with bidirectional capability" },
        { label: "Unbalance", value: "V & I" },
        { label: "Harmonics", value: "Individual Harmonics upto 63rd order" },
        { label: "Demand", value: "I, kW & kVA" },
        { label: "Historical Data", value: "Latest 10 years, 12 months, 31 days energy" },
        { label: "Configuration", value: "Remote configuration capability" },
        { label: "Battery Backup", value: "10 secs battery backup to capture Power interruption event" }
      ]
    }
  };

  if (activeTab === "features") {
    return (
      <div className="bg-white rounded-2xl shadow-lg overflow-hidden font-['Open_Sans']">
        <div className="bg-gradient-to-r from-yellow-400 to-yellow-400 p-3">
          <h3 className="text-xl md:text-2xl font-bold text-center text-white font-['Open_Sans']">Salient Features</h3>
        </div>
        <div className="p-4">
          <div className="space-y-2">
            {productData[activeProduct]?.features.map((feature: string, idx: number) => (
              <motion.div
                key={idx}
                initial={{ opacity: 0, x: -10 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: idx * 0.05 }}
                viewport={{ once: true }}
                className="flex items-start p-3 hover:bg-yellow-50 rounded-lg transition-colors duration-300"
              >
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-yellow-100 text-yellow-600 flex items-center justify-center mr-3">
                  <Check className="h-4 w-4" />
                </div>
                <span className="text-black font-semibold font-['Open_Sans']">{feature}</span>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    );
  } else {
    return (
      <div className="bg-white rounded-2xl shadow-lg overflow-hidden font-['Open_Sans']">
        <div className="bg-gradient-to-r from-yellow-400 to-yellow-400 p-3">
          <h3 className="text-xl md:text-2xl font-bold text-center text-white font-['Open_Sans']">Measurements</h3>
        </div>
        <div className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {productData[activeProduct]?.measurements.map((item: { label: string; value: string }, idx: number) => (
              <motion.div
                key={idx}
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: idx * 0.05 }}
                viewport={{ once: true }}
                className="bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow duration-300 border border-yellow-100"
              >
                <h4 className="font-bold text-black mb-2 border-b border-yellow-200 pb-2 font-['Open_Sans']">{item.label}</h4>
                <div className="text-black font-semibold font-['Open_Sans']">{item.value}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    );
  }
};

// Comparison Table Component with improved styling
const ComparisonTable = ({ productSeries }: { productSeries: string }) => {
  // Define comparison data based on product series
  const comparisonData = {
    "standard-series": {
      models: ['DiGi 530S/H', 'DiGi 630S/D', 'DiGi 730S/D'],
      features: [
        { name: 'Accuracy Class', values: ['0.5s', '0.5s/0.2s', '0.5s/0.2s'] },
        { name: 'Display', values: ['3-line LCD', '3-line LCD', '3-line LCD'] },
        { name: 'Harmonics Order', values: ['Up to 31st (530H)', 'Up to 31st', 'Up to 51st'] },
        { name: 'Digital I/O', values: ['Basic', '4DI, 2DO (630D)', '6DI, 3DO, 1AO (730D)'] },
        { name: 'Tariff Energy', values: ['No', 'Yes', 'Yes (6 rates)'] },
        { name: 'Baud Rate', values: ['Up to 38,400', 'Up to 38,400', 'Up to 115,200'] }
      ]
    },
    "premium-series": {
      models: ['DiGi 760', 'DiGi 820'],
      features: [
        { name: 'Accuracy Class', values: ['0.2s', '0.2s (Class A)'] },
        { name: 'Display', values: ['320 x 240 TFT', '640 x 480 Color LCD'] },
        { name: 'Sampling Rate', values: ['Standard', '1024 samples/cycle'] },
        { name: 'Harmonics', values: ['Up to 63rd order', 'Up to 63rd order + Inter-harmonics'] },
        { name: 'Power Quality', values: ['Swell/Sag', 'Swell/Sag/Transient/RVC'] },
        { name: 'Memory', values: ['Standard', '8GB'] },
        { name: 'Communication', values: ['Modbus RS485/TCP/IP', 'Modbus RS485/TCP/IP'] }
      ]
    },
    "plm-series": {
      models: ['PLM R90', 'PLM R91', 'PLM R93'],
      features: [
        { name: 'Type', values: ['1Φ DC', '1Φ AC', '3Φ AC'] },
        { name: 'Accuracy', values: ['Class 0.5', 'Class 1.0', 'Class 1.0'] },
        { name: 'Current Rating', values: ['Up to 300A', 'Up to 63A', 'Up to 63A'] },
        { name: 'Display', values: ['LED indicators', '6+1 digit LCD', '7+1 digit LCD'] },
        { name: 'Communication', values: ['Optional RS485', 'Optional RS485', 'RS485 Standard'] },
        { name: 'Installation', values: ['DIN Rail', 'DIN Rail', 'DIN Rail'] },
        { name: 'Dimensions', values: ['100 x 36 x 65mm', '100 x 36 x 65mm', '72 x 100 x 65mm'] }
      ]
    },
    "eon-series": {
      models: ['EON 4.0S', 'EON 4.0G', 'EON 4.0E'],
      features: [
        { name: 'Accuracy', values: ['Class 0.5', 'Class 0.5', 'Class 0.5'] },
        { name: 'Communication', values: ['RS485 Modbus-RTU', '4G wireless for MQTT', 'LAN RJ45 for Modbus TCP/HTTP/MQTT'] },
        { name: 'Data Storage', values: ['No', '7 days (5 mins data)', '7 days (5 mins data)'] },
        { name: 'Gateway Function', values: ['No', 'Yes', 'Yes'] },
        { name: 'Harmonics', values: ['Up to 63rd order', 'Up to 63rd order', 'Up to 63rd order'] },
        { name: 'Installation', values: ['Panel Mount', 'Panel Mount', 'Panel Mount'] }
      ]
    }
  };

  // Get appropriate comparison data
  const { models, features } = comparisonData[productSeries] || { models: [], features: [] };

  return (
    <div className="bg-white rounded-2xl shadow-lg overflow-hidden mt-6 font-['Open_Sans']">
      <div className="bg-gradient-to-r from-yellow-400 to-yellow-400 p-3">
        <h3 className="text-xl md:text-2xl font-bold text-center text-white font-['Open_Sans']">Model Comparison</h3>
      </div>
      <div className="p-2 sm:p-4 overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead>
            <tr>
              <th className="px-2 sm:px-4 py-2 bg-yellow-50 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider rounded-tl-lg">Feature</th>
              {models.map((model: string, idx: number) => (
                <th key={idx} className={`px-2 sm:px-4 py-2 bg-yellow-50 text-center text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider ${idx === models.length - 1 ? 'rounded-tr-lg' : ''}`}>
                  {model}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {features.map((feature: { name: string; values: string[] }, idx: number) => (
              <motion.tr
                key={idx}
                initial={{ opacity: 0, y: 5 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: idx * 0.05 }}
                viewport={{ once: true }}
                className={idx % 2 === 0 ? 'bg-white hover:bg-yellow-50 transition-colors duration-200' : 'bg-gray-50 hover:bg-yellow-50 transition-colors duration-200'}
              >
                <td className="px-2 sm:px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 font-['Open_Sans']">{feature.name}</td>
                {feature.values.map((value: string, i: number) => (
                  <td key={i} className="px-2 sm:px-4 py-3 whitespace-nowrap text-sm text-gray-900 text-center font-medium font-['Open_Sans']">
                    {value}
                  </td>
                ))}
              </motion.tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

// Contact Section Component with improved styling
const ContactSection = ({ onContactClick }: { onContactClick: () => void }) => {
  return (
    <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 py-6 px-4 rounded-2xl shadow-lg font-['Open_Sans']">
      <div className="max-w-full mx-auto text-center">
        <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-black mb-3 font-['Open_Sans']">Need More Information?</h2>
        <p className="text-black mb-6 max-w-4xl mx-auto font-semibold text-sm md:text-base lg:text-lg text-center font-['Open_Sans']">
          Our team of experts is ready to help you with product specifications, custom solutions,
          pricing, and any other details you need about the KRYKARD Multi Functional Meters.
        </p>
        <Button
          className="inline-flex items-center px-6 md:px-8 py-3 md:py-4 bg-yellow-400 hover:bg-yellow-500 text-black font-semibold rounded-lg shadow-md transition duration-300 transform hover:-translate-y-1 text-sm md:text-base"
          onClick={onContactClick}
        >
          Contact Our Experts
          <ArrowRight className="ml-2 h-4 md:h-5 w-4 md:w-5" />
        </Button>
      </div>
    </div>
  );
};

// Applications Section Component with improved styling
const ApplicationsSection = () => {
  const applications = [
    {
      title: "Power Distribution",
      icon: <Zap className="h-8 w-8 text-yellow-600" />,
      description: "Monitor and optimize power distribution systems in industrial facilities, commercial buildings, and utility grids."
    },
    {
      title: "Energy Management",
      icon: <BarChart2 className="h-8 w-8 text-yellow-600" />,
      description: "Track energy consumption patterns, implement cost-saving measures, and ensure regulatory compliance."
    },
    {
      title: "Power Quality Analysis",
      icon: <Activity className="h-8 w-8 text-yellow-600" />,
      description: "Analyze harmonics, power factor, and other quality parameters to ensure efficient energy use and equipment protection."
    },
    {
      title: "Building Automation",
      icon: <Factory className="h-8 w-8 text-yellow-600" />,
      description: "Integrate with building management systems to monitor and control energy usage across facility operations."
    }
  ];

  return (
    <div className="py-6 md:py-8 bg-gradient-to-br from-yellow-50 to-white rounded-2xl my-6 shadow-lg font-['Open_Sans']">
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-6 md:mb-8">
          <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-gray-900 mb-3 font-['Open_Sans']">Application Areas</h2>
          <p className="text-sm md:text-base lg:text-lg text-gray-800 max-w-4xl mx-auto font-medium text-center font-['Open_Sans']">
            Our multi-functional meters are designed for a wide range of energy measurement applications
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
          {applications.map((app, idx) => (
            <motion.div
              key={idx}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: idx * 0.1 }}
              viewport={{ once: true }}
              className="bg-white p-4 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-2 border-b-4 border-yellow-400"
            >
              <div className="bg-yellow-100 w-16 h-16 rounded-lg flex items-center justify-center mb-3 text-yellow-600 mx-auto md:mx-0">
                {app.icon}
              </div>
              <h3 className="text-base md:text-lg lg:text-xl font-semibold mb-2 text-gray-900 text-center md:text-left font-['Open_Sans']">{app.title}</h3>
              <p className="text-gray-900 font-medium text-sm md:text-base text-center md:text-justify font-['Open_Sans']">{app.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Main Component
const MultiFunctionalMeters = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const [activeProduct, setActiveProduct] = useState("digi530");
  const [activeDetailTab, setActiveDetailTab] = useState("features");
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // Effect to check URL parameters for initial tab and product type
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const tab = params.get('tab');
    const product = params.get('product');
    const detailTab = params.get('detailTab');

    if (tab) setActiveTab(tab);
    if (product) setActiveProduct(product);
    if (detailTab) setActiveDetailTab(detailTab);

    // If we're viewing product details, scroll to the product section
    if (tab === 'details' && product) {
      // Use requestAnimationFrame instead of setTimeout to prevent blinking
      requestAnimationFrame(() => {
        // Find the product detail section by ID and scroll directly to it
        const productDetailSection = document.getElementById('product-detail-view');
        if (productDetailSection) {
          productDetailSection.scrollIntoView({ behavior: 'auto', block: 'start' });
        }
      });
    }
  }, [location]);

  // Handler for Request Demo button
  const handleRequestDemo = () => {
    navigate("/contact/sales");
  };

  // Handler for View Brochure button
  const handleViewBrochure = () => {
    window.open(PDF_URL, '_blank');
  };

  // Handler for View Details button
  const handleViewDetails = (product: string) => {
    // Update state
    setActiveProduct(product);
    setActiveTab('details');
    setActiveDetailTab('features');

    // Update URL - this will directly show the details tab
    navigate(`?tab=details&product=${product}&detailTab=features`, { replace: true });

    // Use requestAnimationFrame instead of setTimeout to prevent blinking
    // This ensures the DOM updates before scrolling
    requestAnimationFrame(() => {
      // Find the product details section and scroll to it
      const detailsSection = document.getElementById('product-detail-view');
      if (detailsSection) {
        // Use scrollIntoView with block: 'start' to position at the top of viewport
        detailsSection.scrollIntoView({ behavior: 'auto', block: 'start' });
      }
    });
  };

  // Product data definition
  // DiGi 530S/530H data
  const diGi530Data = {
    title: "DiGi 530S/H",
    modelInfo: "Standard Series",
    image: "/Multifunctional meters/630-removebg-preview.png",
    features: [
      "Accuracy Class 0.5s as per IEC 62053-22",
      "3-line bright LCD display",
      "PT & CT programmable",
      "Password protection for setup page",
      "RS485 port with Modbus RTU protocol"
    ],
    measurements: [
      "RMS - V, U, I, In, Hz",
      "Power - kW, kVAr, kVA",
      "PF - Phase wise & Total",
      "Energy - kWh, kVAh, kVArh",
      "THD - Voltage & Current"
    ],
    colors: {
      primary: 'yellow-600',
      secondary: 'yellow-100'
    }
  };

  // DiGi 630S/630D data
  const diGi630Data = {
    title: "DiGi 630S/D",
    modelInfo: "Advanced Series",
    image: "/Multifunctional meters/630-removebg-preview.png",
    features: [
      "Accuracy Class 0.5s / Class 0.2s",
      "3-line bright LCD display",
      "4 DI, 2 DO (DiGi 630D)",
      "PT & CT programmable",
      "Modbus custom table"
    ],
    measurements: [
      "RMS - V, U, I, In, Hz",
      "Power - kW, kVAr, kVA, PF",
      "Bi-directional, 4-Quadrant energy",
      "Individual Harmonics upto 31st order",
      "Demand - I, kW, kVAr, kVA"
    ],
    colors: {
      primary: 'yellow-600',
      secondary: 'yellow-100'
    }
  };

  // DiGi 730S/730D data
  const diGi730Data = {
    title: "DiGi 730S/D",
    modelInfo: "Premium Series",
    image: "/Multifunctional meters/730-removebg-preview.png",
    features: [
      "Accuracy Class 0.5s /class 0.2s",
      "6 Digital inputs, 3DO & 1AO",
      "PT & CT programmable",
      "Tariff Energy Recording (6 rates)",
      "Baud rate up to 115,200 bps"
    ],
    measurements: [
      "RMS - V, U, I, In, Hz",
      "Power - kW, kVAr, kVA, PF",
      "Individual Harmonics upto 51st order",
      "Measures Crest factor and K factor",
      "Demand monitoring - I, kW, kVAr, kVA"
    ],
    colors: {
      primary: 'yellow-600',
      secondary: 'yellow-100'
    }
  };

  // DiGi 760 data
  const diGi760Data = {
    title: "DiGi 760",
    modelInfo: "High Precision Meter",
    image: "/Multifunctional meters/760-removebg-preview.png",
    features: [
      "Accuracy Class 0.2s as per IEC 62053-22",
      "TFT colorful LCD display with 320 x 240 resolution",
      "Real Time waveform Display",
      "Voltage swell/sag event recording",
      "Ethernet Port with Modbus TCP/IP protocol"
    ],
    measurements: [
      "RMS - V, U, I, In, Hz",
      "Powers – kW, kVA, kVAr – phase wise and total",
      "Bi-directional energy measurements",
      "THD - Voltage & Current",
      "Individual harmonics on V & I upto 63rd order"
    ],
    colors: {
      primary: 'yellow-600',
      secondary: 'yellow-100'
    }
  };

  // DiGi 820 data
  const diGi820Data = {
    title: "DiGi 820",
    modelInfo: "Class A Power Quality",
    image: "/Multifunctional meters/digi820-removebg-preview.png",
    features: [
      "IEC 61000-4-30 Class A power quality monitoring",
      "Class 0.2s high accuracy energy measurement",
      "Sampling rate: 1024 samples/cycle",
      "Color LCD Display with resolution of 640 x 480",
      "8GB memory for data and event recording"
    ],
    measurements: [
      "RMS - V, U, I, In, Hz",
      "Power (kW, kVAr, kVA) & PF : Phase & Total",
      "4-Quadrant energy measurements",
      "Harmonics & Inter-harmonics upto 63rd order",
      "Records 256 PQ events with waveform"
    ],
    colors: {
      primary: 'yellow-600',
      secondary: 'yellow-100'
    }
  };

  // AC Multi-Channel data
  const multy4Data = {
    title: "Multy4",
    modelInfo: "Multi-Channel Meter",
    image: "/Multifunctional meters/Multy_4-removebg-preview.png",
    features: [
      "Backlit custom LCD display",
      "Accuracy Class 1.0 as per IEC 62053-21",
      "CT primary programmable with 2kV isolation",
      "Special high-accuracy CT supplied",
      "RS485-modbus RTU Protocol"
    ],
    measurements: [
      "Single phase/Three phase: 12 x 1 phase / 4 x 3 phase",
      "Voltages L-N and L-L (C) & Frequency",
      "Currents: Line for each channel",
      "kW, kVAr, kVA & PF: Phase and Total",
      "kWh, kVAh: Phase and Total"
    ],
    colors: {
      primary: 'yellow-600',
      secondary: 'yellow-100'
    }
  };

  // PLM R90 data
  const plmR90Data = {
    title: "PLM R90",
    modelInfo: "1Φ DC Energy Meter",
    image: "/Multifunctional meters/PLm_R90-removebg-preview.png",
    features: [
      "35mm DIN rail installing",
      "Accuracy : Class 0.5",
      "LED indicates pulse output",
      "RS485 port (Optional)",
      "2 module size (100 x 36 x 65mm)"
    ],
    measurements: [
      "RMS - V, I",
      "Power : kW",
      "Energy : kWh",
      "Shunt: 100A, 200A, 300A",
      "Suitable for DC applications"
    ],
    colors: {
      primary: 'yellow-600',
      secondary: 'yellow-100'
    }
  };

  // PLM R91 data
  const plmR91Data = {
    title: "PLM R91",
    modelInfo: "1Φ AC Energy Meter",
    image: "/Multifunctional meters/R_91-removebg-preview.png",
    features: [
      "35mm DIN rail installation",
      "Active energy accuracy Class 1.0",
      "6+1 digit LCD display",
      "Passive pulse output",
      "RS485 port (optional)"
    ],
    measurements: [
      "RMS - V, I, Hz",
      "Power - kW, kVAr, kVA, PF",
      "Energy - kWh, kVArh",
      "Max. current 63A",
      "Ideal for single phase monitoring"
    ],
    colors: {
      primary: 'yellow-600',
      secondary: 'yellow-100'
    }
  };

  // PLM R93 data
  const plmR93Data = {
    title: "PLM R93",
    modelInfo: "3Φ AC Energy Meter",
    image: "/Multifunctional meters/R_93-removebg-preview.png",
    features: [
      "35mm DIN rail installation",
      "kWh Class 1.0 as per IEC 62053-21",
      "7+1 digit LCD display",
      "Phase sequence error indication",
      "RS485 port with Modbus-RTU protocol"
    ],
    measurements: [
      "RMS - V, I, Hz",
      "Power - kW, kVAr, kVA, PF",
      "Bidirectional energy measurement",
      "Multi tariff energy values",
      "Historical data recording"
    ],
    colors: {
      primary: 'yellow-600',
      secondary: 'yellow-100'
    }
  };

  // EON 4.0 data
  const eon40Data = {
    title: "EON 4.0",
    modelInfo: "Advanced Energy Meter",
    image: "/assets/meters/eon40.png",
    features: [
      "Active energy accuracy up to Class 0.5",
      "PT/CT programmable",
      "Auto Phase sequence adjustment",
      "2 DI Standard, 2 DO (optional)",
      "Multiple communication options"
    ],
    measurements: [
      "RMS - V, I, Hz, V & I phase angle",
      "Power - kW, kVAR, kVA & PF",
      "Bidirectional energy measurements",
      "Individual Harmonics up to 63rd order",
      "Historical energy data storage"
    ],
    colors: {
      primary: 'yellow-500',
      secondary: 'yellow-50'
    }
  };

  // Function to get product data based on active product
  const getProductData = () => {
    switch(activeProduct) {
      case "digi530": return diGi530Data;
      case "digi630": return diGi630Data;
      case "digi730": return diGi730Data;
      case "digi760": return diGi760Data;
      case "digi820": return diGi820Data;
      case "multy4": return multy4Data;
      case "plmr90": return plmR90Data;
      case "plmr91": return plmR91Data;
      case "plmr93": return plmR93Data;
      case "eon40": return eon40Data;
      default: return diGi530Data;
    }
  };

  // Function to get series for comparison based on active product
  const getComparisonSeries = () => {
    if (["digi530", "digi630", "digi730"].includes(activeProduct))
      return "standard-series";
    if (["digi760", "digi820"].includes(activeProduct))
      return "premium-series";
    if (["plmr90", "plmr91", "plmr93"].includes(activeProduct))
      return "plm-series";
    if (activeProduct === "eon40")
      return "eon-series";
    return "standard-series";
  };

  // Navigation tabs data
  const navTabs = [
    { id: "overview", label: "Overview", icon: <Gauge className="h-5 w-5" /> },
    { id: "details", label: "Product Details", icon: <Zap className="h-5 w-5" /> },
    { id: "applications", label: "Applications", icon: <Shield className="h-5 w-5" /> }
  ];

  return (
    <PageLayout
      title="Multi Functional Meters"
      subtitle=""
      category="measure"
    >
      <style>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
      <div className="font-['Open_Sans']">
      {/* Premium Modern Navigation Tabs - Responsive Design */}
      <div className="sticky top-0 z-30 bg-white shadow-lg border-b border-gray-100 font-['Open_Sans']">
        <div className="max-w-full mx-auto px-2">
          {/* Desktop Navigation */}
          <div className="hidden md:flex justify-center py-2">
            <div className="bg-gray-50 p-1.5 rounded-full flex shadow-sm">
              {navTabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => {
                    setActiveTab(tab.id);
                    if (tab.id === "details") {
                      navigate(`?tab=${tab.id}&product=${activeProduct}&detailTab=${activeDetailTab}`, { replace: true });
                    } else {
                      navigate(`?tab=${tab.id}`, { replace: true });
                    }
                    setIsMobileMenuOpen(false);
                  }}
                  className={cn(
                    "relative px-6 py-2.5 font-medium rounded-full transition-all duration-300 flex items-center mx-1 overflow-hidden text-base",
                    activeTab === tab.id
                      ? "bg-gradient-to-r from-yellow-400 to-yellow-400 text-black shadow-md transform -translate-y-0.5"
                      : "text-gray-700 hover:bg-yellow-50 hover:text-yellow-500"
                  )}
                >
                  <div className="flex items-center relative z-10">
                    <span className="mr-2">{tab.icon}</span>
                    <span>{tab.label}</span>
                  </div>
                  {activeTab === tab.id && (
                    <motion.div
                      layoutId="navIndicator"
                      className="absolute inset-0 bg-gradient-to-r from-yellow-400 to-yellow-400 -z-0"
                      initial={false}
                      transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                    />
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Mobile Navigation */}
          <div className="md:hidden py-2 flex justify-between items-center">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="text-gray-700 hover:text-yellow-500 focus:outline-none"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            <span className="font-semibold text-gray-900 text-lg">
              {navTabs.find(tab => tab.id === activeTab)?.label}
            </span>

            <div className="w-6"></div> {/* Spacer for balanced layout */}
          </div>

          {/* Mobile Menu Dropdown */}
          <div className={`md:hidden overflow-hidden transition-all duration-300 ease-in-out ${isMobileMenuOpen ? 'max-h-60' : 'max-h-0'}`}>
            <div className="bg-white rounded-lg shadow-lg p-2 mb-2 z-50 relative">
              {navTabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => {
                    setActiveTab(tab.id);
                    if (tab.id === "details") {
                      navigate(`?tab=${tab.id}&product=${activeProduct}&detailTab=${activeDetailTab}`, { replace: true });
                    } else {
                      navigate(`?tab=${tab.id}`, { replace: true });
                    }
                    setIsMobileMenuOpen(false);
                  }}
                  className={`w-full text-left px-4 py-3 rounded-lg my-1 flex items-center ${
                    activeTab === tab.id
                      ? "bg-yellow-50 text-yellow-600 font-medium"
                      : "text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  <span className="mr-3">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {activeTab === "overview" && (
        <>
          {/* Hero Section */}
          <HeroSection
            onRequestDemo={handleRequestDemo}
            onViewBrochure={handleViewBrochure}
          />

          {/* Key Features Overview */}
          <div className="py-6 md:py-8 bg-gradient-to-br from-yellow-50 to-white font-['Open_Sans']">
            <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-6 md:mb-8">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                  viewport={{ once: true }}
                >
                  <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-yellow-600 mb-3 font-['Open_Sans']">Why Choose Our Multi Functional Meters?</h2>
                  <p className="mt-3 text-sm md:text-base lg:text-lg text-gray-800 max-w-4xl mx-auto font-medium text-center font-['Open_Sans']">
                    Precision-engineered instruments that combine accuracy, durability, and advanced features
                  </p>
                </motion.div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
                <FeatureHighlight
                  icon={<Gauge className="h-6 w-6 text-white" />}
                  title="Precision Accuracy"
                  description="Our energy meters offer high precision measurements with accuracy classes from 0.2s to 1.0, ensuring reliable readings for critical applications."
                />

                <FeatureHighlight
                  icon={<Activity className="h-6 w-6 text-white" />}
                  title="Power Quality Analysis"
                  description="Advanced harmonic analysis, sag/swell detection, and power quality monitoring to ensure optimal electrical system performance."
                />

                <FeatureHighlight
                  icon={<Database className="h-6 w-6 text-white" />}
                  title="Data Management"
                  description="Comprehensive data logging, memory storage, and communication options for effective energy monitoring and management systems."
                />
              </div>
            </div>
          </div>

          {/* DiGi 530, 630 & 730 Series Products */}
          <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6 md:py-8 font-['Open_Sans']">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-6 md:mb-8"
            >
              <span className="inline-block bg-yellow-100 text-yellow-800 px-4 py-1 rounded-full text-sm md:text-base font-semibold mb-3 font-['Open_Sans']">
                STANDARD SERIES
              </span>
              <h2 className="text-xl md:text-2xl lg:text-4xl font-bold mb-4 text-gray-900 font-['Open_Sans']">
                DiGi 530, 630 & 730 Series
              </h2>
              <p className="max-w-4xl mx-auto text-gray-800 text-sm md:text-base lg:text-lg font-medium text-center font-['Open_Sans']">
                Versatile and reliable energy measurement solutions for industrial applications
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
              <ProductCard
                {...diGi530Data}
                onViewDetailsClick={() => handleViewDetails("digi530")}
              />
              <ProductCard
                {...diGi630Data}
                onViewDetailsClick={() => handleViewDetails("digi630")}
              />
              <ProductCard
                {...diGi730Data}
                onViewDetailsClick={() => handleViewDetails("digi730")}
              />
            </div>
          </div>

          {/* DiGi 760 & 820 Series Products */}
          <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6 md:py-8 bg-gray-50 font-['Open_Sans']">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-6 md:mb-8"
            >
              <span className="inline-block bg-yellow-100 text-yellow-800 px-4 py-1 rounded-full text-sm md:text-base font-semibold mb-3 font-['Open_Sans']">
                PREMIUM SERIES
              </span>
              <h2 className="text-xl md:text-2xl lg:text-4xl font-bold mb-4 text-gray-900 font-['Open_Sans']">
                DiGi 760 & DiGi 820 Series
              </h2>
              <p className="max-w-4xl mx-auto text-gray-800 text-sm md:text-base lg:text-lg font-medium text-center font-['Open_Sans']">
                High precision energy meters with advanced power quality analysis capabilities
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 max-w-5xl mx-auto">
              <ProductCard
                {...diGi760Data}
                onViewDetailsClick={() => handleViewDetails("digi760")}
              />
              <ProductCard
                {...diGi820Data}
                onViewDetailsClick={() => handleViewDetails("digi820")}
              />
            </div>
          </div>

          {/* Multi-Channel Products */}
          <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6 md:py-8 font-['Open_Sans']">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-6 md:mb-8"
            >
              <span className="inline-block bg-yellow-100 text-yellow-800 px-4 py-1 rounded-full text-sm md:text-base font-semibold mb-3 font-['Open_Sans']">
                SPECIALIZED SERIES
              </span>
              <h2 className="text-xl md:text-2xl lg:text-4xl font-bold mb-4 text-gray-900 font-['Open_Sans']">
                AC Multi-Channel Energy Meter
              </h2>
              <p className="max-w-4xl mx-auto text-gray-800 text-sm md:text-base lg:text-lg font-medium text-center font-['Open_Sans']">
                Monitor multiple circuits with a single device for comprehensive energy management
              </p>
            </motion.div>

            <div className="flex justify-center">
              <div className="max-w-lg w-full">
                <ProductCard
                  {...multy4Data}
                  onViewDetailsClick={() => handleViewDetails("multy4")}
                />
              </div>
            </div>
          </div>

          {/* PLM Series Products */}
          <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6 md:py-8 bg-gray-50 font-['Open_Sans']">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-6 md:mb-8"
            >
              <span className="inline-block bg-yellow-100 text-yellow-800 px-4 py-1 rounded-full text-sm md:text-base font-semibold mb-3 font-['Open_Sans']">
                COMPACT SERIES
              </span>
              <h2 className="text-xl md:text-2xl lg:text-4xl font-bold mb-4 text-gray-900 font-['Open_Sans']">
                PLM Series Energy Meters
              </h2>
              <p className="max-w-4xl mx-auto text-gray-800 text-sm md:text-base lg:text-lg font-medium text-center font-['Open_Sans']">
                Space-saving DIN rail mounted meters for efficient energy monitoring
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
              <ProductCard
                {...plmR90Data}
                onViewDetailsClick={() => handleViewDetails("plmr90")}
              />
              <ProductCard
                {...plmR91Data}
                onViewDetailsClick={() => handleViewDetails("plmr91")}
              />
              <ProductCard
                {...plmR93Data}
                onViewDetailsClick={() => handleViewDetails("plmr93")}
              />
            </div>
          </div>

          {/* EON Series Products */}
          <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6 md:py-8 font-['Open_Sans']">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-6 md:mb-8"
            >
              <span className="inline-block bg-yellow-100 text-yellow-800 px-4 py-1 rounded-full text-sm md:text-base font-semibold mb-3 font-['Open_Sans']">
                ADVANCED SERIES
              </span>
              <h2 className="text-xl md:text-2xl lg:text-4xl font-bold mb-4 text-gray-900 font-['Open_Sans']">
                EON Series Energy Meter
              </h2>
              <p className="max-w-4xl mx-auto text-gray-800 text-sm md:text-base lg:text-lg font-medium text-center font-['Open_Sans']">
                Advanced energy meters with multiple communication options and historical data logging
              </p>
            </motion.div>

            <div className="flex justify-center">
              <div className="max-w-lg w-full">
                <ProductCard
                  {...eon40Data}
                  onViewDetailsClick={() => handleViewDetails("eon40")}
                />
              </div>
            </div>
          </div>

          {/* Contact Information Section */}
          <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6 md:py-8">
            <ContactSection onContactClick={handleRequestDemo} />
          </div>
        </>
      )}

      {activeTab === "details" && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Enhanced Product Type Selector - Compact Version */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-gradient-to-r from-yellow-50 to-white rounded-xl shadow-md p-6 mb-8 relative overflow-hidden"
          >
            <div className="absolute top-0 right-0 w-64 h-64 bg-yellow-200 rounded-full opacity-20 transform translate-x-1/2 -translate-y-1/2 blur-3xl"></div>

            <h2 className="text-2xl font-bold text-gray-900 mb-6 relative z-10">
              Select <span className="text-yellow-500">Model Series</span>
            </h2>

            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 relative z-10">
              {[
                { id: "digi530", label: "DiGi 530S/H", icon: <Gauge className="h-7 w-7" /> },
                { id: "digi630", label: "DiGi 630S/D", icon: <Gauge className="h-7 w-7" /> },
                { id: "digi730", label: "DiGi 730S/D", icon: <Gauge className="h-7 w-7" /> },
                { id: "digi760", label: "DiGi 760", icon: <BarChart2 className="h-7 w-7" /> },
                { id: "digi820", label: "DiGi 820", icon: <Activity className="h-7 w-7" /> }
              ].map((option) => (
                <motion.button
                  key={option.id}
                  whileHover={{ y: -3, boxShadow: "0 8px 20px -5px rgba(0, 0, 0, 0.1)" }}
                  whileTap={{ y: 0 }}
                  onClick={() => {
                    setActiveProduct(option.id);
                    navigate(`?tab=details&product=${option.id}&detailTab=${activeDetailTab}`, { replace: true });
                  }}
                  className={`relative rounded-lg transition-all duration-300 overflow-hidden ${
                    activeProduct === option.id
                      ? "ring-1 ring-yellow-400"
                      : "hover:ring-1 hover:ring-yellow-300"
                  }`}
                >
                  <div className={`h-full py-5 px-3 flex flex-col items-center text-center ${
                    activeProduct === option.id
                      ? "bg-yellow-500 text-white"
                      : "bg-white hover:bg-yellow-50"
                  }`}>
                    <div className="mb-3">
                      <div className={`${activeProduct === option.id ? "text-white" : "text-yellow-500"}`}>
                        {option.icon}
                      </div>
                    </div>

                    <h3 className={`text-base font-bold mb-1 ${activeProduct === option.id ? "text-white" : "text-gray-900"}`}>
                      {option.label}
                    </h3>

                    {activeProduct === option.id && (
                      <div className="mt-2 bg-white bg-opacity-20 rounded-full px-3 py-1 text-sm font-semibold">
                        Selected
                      </div>
                    )}
                  </div>
                </motion.button>
              ))}
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 mt-4 relative z-10">
              {[
                { id: "multy4", label: "Multy4", icon: <Database className="h-7 w-7" /> },
                { id: "plmr90", label: "PLM R90", icon: <Zap className="h-7 w-7" /> },
                { id: "plmr91", label: "PLM R91", icon: <Zap className="h-7 w-7" /> },
                { id: "plmr93", label: "PLM R93", icon: <Zap className="h-7 w-7" /> },
                { id: "eon40", label: "EON 4.0", icon: <Settings className="h-7 w-7" /> }
              ].map((option) => (
                <motion.button
                  key={option.id}
                  whileHover={{ y: -3, boxShadow: "0 8px 20px -5px rgba(0, 0, 0, 0.1)" }}
                  whileTap={{ y: 0 }}
                  onClick={() => {
                    setActiveProduct(option.id);
                    navigate(`?tab=details&product=${option.id}&detailTab=${activeDetailTab}`, { replace: true });
                  }}
                  className={`relative rounded-lg transition-all duration-300 overflow-hidden ${
                    activeProduct === option.id
                      ? "ring-1 ring-yellow-400"
                      : "hover:ring-1 hover:ring-yellow-300"
                  }`}
                >
                  <div className={`h-full py-5 px-3 flex flex-col items-center text-center ${
                    activeProduct === option.id
                      ? "bg-yellow-500 text-white"
                      : "bg-white hover:bg-yellow-50"
                  }`}>
                    <div className="mb-3">
                      <div className={`${activeProduct === option.id ? "text-white" : "text-yellow-500"}`}>
                        {option.icon}
                      </div>
                    </div>

                    <h3 className={`text-base font-bold mb-1 ${activeProduct === option.id ? "text-white" : "text-gray-900"}`}>
                      {option.label}
                    </h3>

                    {activeProduct === option.id && (
                      <div className="mt-2 bg-white bg-opacity-20 rounded-full px-3 py-1 text-sm font-semibold">
                        Selected
                      </div>
                    )}
                  </div>
                </motion.button>
              ))}
            </div>
          </motion.div>

          {/* Enhanced Product Detail Section */}
          <div id="product-detail-view" className="grid grid-cols-1 md:grid-cols-12 gap-6 md:gap-8">
            <div className="md:col-span-5">
              <div className="sticky top-24">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                  className="bg-gradient-to-br from-white to-yellow-50 rounded-2xl shadow-lg p-8 mb-6 relative overflow-hidden"
                >
                  {/* Background decoration */}
                  <div className="absolute top-6 left-6 bg-yellow-500 text-white px-3 py-1 rounded-full text-xs font-semibold z-10">
                    {getProductData().modelInfo}
                  </div>

                  {/* Image container with glow effect */}
                  <div className="relative mb-8 mt-4">
                    <div className="absolute inset-0 bg-gradient-to-br from-yellow-200 to-yellow-50 rounded-full opacity-30 blur-xl transform scale-90"></div>
                    <motion.div
                      animate={{ y: [0, -10, 0] }}
                      transition={{ repeat: Infinity, duration: 3, ease: "easeInOut" }}
                      className="relative z-10 flex justify-center items-center py-4 md:py-8"
                    >
                      <img
                        src={getProductData().image}
                        alt={getProductData().title}
                        className="max-h-[250px] md:max-h-[300px] w-auto object-contain drop-shadow-2xl transform transition-transform duration-500 hover:scale-110"
                      />
                    </motion.div>
                  </div>

                  {/* Product details */}
                  <div className="text-center mb-8">
                    <h3 className="text-3xl font-bold text-gray-900 mb-3">{getProductData().title}</h3>
                    <div className="flex justify-center space-x-3 mb-6">
                      <span className="inline-block bg-yellow-100 text-yellow-700 px-3 py-1 rounded-full text-sm font-medium">
                        {getProductData().features[0]}
                      </span>
                    </div>
                  </div>

                  {/* View Brochure button */}
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      className="w-full bg-gradient-to-r from-yellow-500 to-yellow-400 hover:from-yellow-600 hover:to-yellow-500 text-gray-900 font-semibold rounded-lg shadow-md transition-all duration-300 flex items-center justify-center py-3"
                      onClick={handleViewBrochure}
                    >
                      <span>View Product Brochure</span>
                      <FileText className="ml-2 h-5 w-5" />
                    </Button>
                  </motion.div>
                </motion.div>
              </div>
            </div>

            <div className="md:col-span-7">
              {/* Enhanced Detail Tabs Navigation */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="bg-white rounded-xl shadow-lg mb-8 overflow-hidden"
              >
                <div className="flex border-b overflow-x-auto scrollbar-hide">
                  {[
                    { id: "features", label: "Features", icon: <Shield className="h-5 w-5" /> },
                    { id: "measurements", label: "Measurements", icon: <Gauge className="h-5 w-5" /> },
                    { id: "comparison", label: "Comparison", icon: <BarChart2 className="h-5 w-5" /> }
                  ].map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => {
                        setActiveDetailTab(tab.id);
                        navigate(`?tab=details&product=${activeProduct}&detailTab=${tab.id}`, { replace: true });
                      }}
                      className={`px-6 py-4 font-medium whitespace-nowrap flex items-center transition-all duration-300 ${
                        activeDetailTab === tab.id
                          ? "bg-yellow-50 border-b-2 border-yellow-500 text-yellow-700"
                          : "text-gray-700 hover:text-yellow-600 hover:bg-yellow-50/50"
                      }`}
                    >
                      <span className="mr-2">{tab.icon}</span>
                      {tab.label}
                    </button>
                  ))}
                </div>
              </motion.div>

              {/* Tab Content with enhanced styling */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="transform origin-top"
              >
                {activeDetailTab === "comparison" ? (
                  <ComparisonTable productSeries={getComparisonSeries()} />
                ) : (
                  <ProductTabContent activeProduct={activeProduct} activeTab={activeDetailTab} />
                )}
              </motion.div>
            </div>
          </div>

          {/* Enhanced Contact Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
            className="mt-20"
          >
            <ContactSection onContactClick={handleRequestDemo} />
          </motion.div>
        </div>
      )}

      {activeTab === "applications" && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h1 className="text-3xl font-bold text-gray-900 mb-4">Applications</h1>
              <p className="text-lg text-gray-800 max-w-3xl mx-auto">
                KRYKARD multi functional meters are versatile instruments designed for a wide range of professional applications
              </p>
            </motion.div>
          </div>

          <ApplicationsSection />

          <div className="mt-16 bg-white rounded-2xl shadow-lg p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Industry-Specific Solutions</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 mb-8">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Commercial Buildings</h3>
                  <p className="text-gray-800 font-medium">
                    Monitor power quality, analyze energy consumption patterns, and implement cost-saving measures in commercial facilities.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Manufacturing</h3>
                  <p className="text-gray-800 font-medium">
                    Optimize energy usage, detect power quality issues, and ensure continuous operation of critical manufacturing processes.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Data Centers</h3>
                  <p className="text-gray-800 font-medium">
                    Monitor critical power systems, ensure reliable operation, and maximize energy efficiency in data center environments.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Renewable Energy</h3>
                  <p className="text-gray-800 font-medium">
                    Monitor and optimize generation, analyze energy flows, and ensure grid compliance in solar and wind installations.
                  </p>
                </div>
              </motion.div>
            </div>

            <div className="flex justify-center mt-8">
              <Button
                className="px-6 py-3 bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-semibold rounded-lg shadow-md transition duration-300 flex items-center space-x-2"
                onClick={handleViewBrochure}
              >
                <span>View Brochure</span>
                <FileText className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </div>

          {/* Contact Section */}
          <div className="mt-16">
            <ContactSection onContactClick={handleRequestDemo} />
          </div>
        </div>
      )}
      </div>

      {/* No PDF Viewer Modal - Using direct link instead */}
    </PageLayout>
  );
};

export default MultiFunctionalMeters;