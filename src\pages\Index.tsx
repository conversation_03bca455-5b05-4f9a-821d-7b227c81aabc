import { useState, useEffect } from 'react';
import { X, MapPin } from 'lucide-react';
import { Link } from 'react-router-dom';
import Layout from "@/components/layout/Layout";
import ClientLogosSection from "@/components/ClientLogosSection";

const Krykard = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [showContactModal, setShowContactModal] = useState(false);

  const bannerImages = [
    '/background_images/Slide_1.png',
    '/background_images/Slide_2.png',
    '/background_images/Slide_3.png',
    '/background_images/Slide_4.png',
    '/background_images/Slide_5.png',
  ];

  const productCategories = [
    {
      title: 'Measure',
      image: '/background_images/test and measurement.jpg',
      description: 'Advanced measurement tools and equipment for precise diagnostics',
      redirectUrl: '/measure'
    },
    {
      title: 'Online UPS',
      image: 'https://www.j-schneider.de/fileadmin/_processed_/4/8/csm_USV_1ad6803997.jpg',
      description: 'Reliable uninterrupted power supply solutions for critical applications',
      redirectUrl: '/protect/ups'
    },
    {
      title: 'Power Conditioners',
      image: '/StaticVoltageRegulator (1).jpg',
      description: 'Advanced servo stabilizers and voltage regulation systems',
      redirectUrl: '/protect/servo-stabilizers'
    },
    {
      title: 'Static Voltage Regulators',
      image: 'https://spectronstabilizer.com/wp-content/uploads/2021/03/About-US-4.jpg',
      description: 'High-performance voltage regulation for critical applications',
      redirectUrl: '/protect/static-stabilizers'
    }
  ];



  // Auto-slide carousel
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide(prev => (prev + 1) % bannerImages.length);
    }, 5000);

    return () => clearInterval(timer);
  }, [bannerImages.length]);

  const HeroCarousel = () => (
    <div className="relative w-full overflow-hidden mt-[60px] sm:mt-[64px] md:mt-[68px] lg:mt-[72px] xl:mt-[76px] h-[60vh] sm:h-[65vh] md:h-[70vh] lg:h-[80vh] xl:h-[90vh] min-h-[450px] max-h-[800px]">
      <div
        className="flex transition-transform duration-500 ease-in-out h-full w-full"
        style={{ transform: `translateX(-${currentSlide * 100}%)` }}
      >
        {bannerImages.map((image, index) => (
          <div key={index} className="w-full h-full flex-shrink-0 relative bg-gray-900 overflow-hidden">
            <img
              src={image}
              alt={`Banner ${index + 1} - Atandra Energy Power Solutions`}
              className="absolute inset-0 w-full h-full object-cover object-center"
              style={{
                imageRendering: 'crisp-edges',
                willChange: 'transform',
                minWidth: '100%',
                minHeight: '100%'
              }}
              loading={index === 0 ? "eager" : "lazy"}
              onError={(e) => {
                console.error(`Banner image ${index + 1} failed to load:`, e);
                // Fallback to a solid color background
                e.currentTarget.style.display = 'none';
                const parent = e.currentTarget.parentElement;
                if (parent) {
                  parent.style.background = 'linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%)';
                }
              }}
            />
            {/* Enhanced overlay for better visual prominence */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-black/10 pointer-events-none"></div>
            {/* Additional subtle vignette effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-black/10 via-transparent to-black/10 pointer-events-none"></div>
          </div>
        ))}
      </div>

      {/* Carousel indicators - Responsive size */}
      <div className="absolute bottom-4 sm:bottom-6 md:bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-2 sm:space-x-3 z-20">
        {bannerImages.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`w-2.5 h-2.5 sm:w-3 sm:h-3 md:w-3.5 md:h-3.5 rounded-full transition-all duration-300 touch-target ${
              currentSlide === index
                ? 'bg-white scale-110 shadow-lg ring-2 ring-white/30'
                : 'bg-white/60 hover:bg-white/80 hover:scale-105'
            }`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>

      {/* Navigation arrows - Responsive size */}
      <button
        onClick={() => setCurrentSlide(prev => (prev - 1 + bannerImages.length) % bannerImages.length)}
        className="absolute left-2 sm:left-4 md:left-6 top-1/2 transform -translate-y-1/2 bg-black/60 hover:bg-black/80 text-white w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 rounded-full transition-all duration-300 z-20 shadow-lg flex items-center justify-center touch-target backdrop-blur-sm"
        aria-label="Previous slide"
      >
        <span className="text-sm sm:text-base md:text-lg font-bold">‹</span>
      </button>
      <button
        onClick={() => setCurrentSlide(prev => (prev + 1) % bannerImages.length)}
        className="absolute right-2 sm:right-4 md:right-6 top-1/2 transform -translate-y-1/2 bg-black/60 hover:bg-black/80 text-white w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 rounded-full transition-all duration-300 z-20 shadow-lg flex items-center justify-center touch-target backdrop-blur-sm"
        aria-label="Next slide"
      >
        <span className="text-sm sm:text-base md:text-lg font-bold">›</span>
      </button>
    </div>
  );

  const StatsSection = () => (
    <div id="stats-section" className="relative py-8 sm:py-12 lg:py-16 w-full overflow-hidden">
      {/* Background image with transparency */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat z-0"
        style={{ backgroundImage: 'url("https://cdn.pixabay.com/photo/2016/09/30/18/28/substation-1705950_1280.jpg")' }}
      ></div>

      {/* Dark overlay for better text readability */}
      <div className="absolute inset-0 bg-gradient-to-r from-gray-800/90 to-gray-900/90 z-0"></div>

      {/* Content - Responsive design */}
      <div className="relative z-10 w-full container-responsive">
        <div className="mb-8 sm:mb-12 text-center">
          <div className="relative inline-block mb-4 sm:mb-6">
            <h2 className="text-responsive-2xl font-bold text-blue-400 font-['Open_Sans'] relative z-10">
              Our Legacy
            </h2>
            {/* Decorative underline */}
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-20 sm:w-24 h-1 bg-blue-400 rounded-full"></div>
          </div>

          {/* Subtitle */}
          <div className="flex items-center justify-center gap-4 mt-4">
            <div className="h-px w-12 sm:w-16 bg-blue-300"></div>
            <span className="text-responsive-sm font-medium text-blue-300 tracking-wide uppercase font-['Open_Sans']">
              40+ Years Excellence
            </span>
            <div className="h-px w-12 sm:w-16 bg-blue-300"></div>
          </div>
        </div>
        <div className="flex flex-col lg:flex-row gap-6 sm:gap-8 lg:gap-12 w-full items-stretch">
          {/* Left Content - Mobile First Design */}
          <div className="w-full lg:w-1/2 flex flex-col justify-center">
            <div className="text-white spacing-responsive-lg">
              <p className="text-responsive-base font-medium text-white leading-relaxed font-['Open_Sans'] text-justify">
                Atandra Energy Pvt. Ltd., headquartered in Chennai, draws
                upon a rich foundation of more than 40+ years of expertise in
                the realm of Power & Energy Management.
              </p>
              <div>
                <h3 className="text-responsive-xl font-bold mb-4 text-white font-['Open_Sans']">
                  Sustainability Commitment
                </h3>
                <p className="text-responsive-base font-medium text-white/90 leading-relaxed font-['Open_Sans'] text-justify">
                  State-of-the-art facilities empower us to address the
                  requirements of Indian industries comprehensively, effectively
                  & efficiently, ensuring they derive maximum benefits from the
                  power conditioning & energy management solutions we provide.
                </p>
              </div>
            </div>
          </div>

          {/* Right Content - Responsive Stats Grid */}
          <div className="w-full lg:w-1/2 flex flex-col justify-center">
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3 sm:gap-4 md:gap-6">
              {/* Stat 1 */}
              <div className="text-center p-3 sm:p-4 md:p-5 flex flex-col justify-center bg-white/5 rounded-lg backdrop-blur-sm border border-white/10 min-h-[100px] sm:min-h-[120px]">
                <div className="text-xl sm:text-2xl md:text-3xl mb-1 sm:mb-2">🏆</div>
                <div className="text-white text-sm sm:text-base md:text-lg font-bold font-['Open_Sans'] mb-1 sm:mb-2">INDIA'S NO.1</div>
                <h4 className="text-white text-xs sm:text-sm font-semibold font-['Open_Sans'] leading-tight">MANUFACTURER OF SERVO STABILISERS</h4>
              </div>

              <div className="text-center p-3 sm:p-4 md:p-5 flex flex-col justify-center bg-white/5 rounded-lg backdrop-blur-sm border border-white/10 min-h-[100px] sm:min-h-[120px]">
                <div className="text-xl sm:text-2xl md:text-3xl mb-1 sm:mb-2">🏢</div>
                <div className="text-white text-sm sm:text-base md:text-lg font-bold font-['Open_Sans'] mb-1 sm:mb-2">100+</div>
                <h4 className="text-white text-xs sm:text-sm font-semibold font-['Open_Sans'] leading-tight">SERVICE CENTRES</h4>
              </div>

              <div className="text-center p-3 sm:p-4 md:p-5 flex flex-col justify-center bg-white/5 rounded-lg backdrop-blur-sm border border-white/10 col-span-2 md:col-span-1 min-h-[100px] sm:min-h-[120px]">
                <div className="text-xl sm:text-2xl md:text-3xl mb-1 sm:mb-2">👥</div>
                <div className="text-white text-sm sm:text-base font-bold font-['Open_Sans'] mb-1 sm:mb-2">PREFERRED</div>
                <h4 className="text-white text-xs sm:text-sm font-semibold font-['Open_Sans'] leading-tight">SUPPLIER OF LARGE CORPORATES & OEM'S</h4>
              </div>

              <div className="text-center p-3 sm:p-4 md:p-5 flex flex-col justify-center bg-white/5 rounded-lg backdrop-blur-sm border border-white/10 min-h-[100px] sm:min-h-[120px]">
                <div className="text-xl sm:text-2xl md:text-3xl mb-1 sm:mb-2">👨‍🔧</div>
                <div className="text-white text-sm sm:text-base md:text-lg font-bold font-['Open_Sans'] mb-1 sm:mb-2">CE</div>
                <h4 className="text-white text-xs sm:text-sm font-semibold font-['Open_Sans'] leading-tight">CERTIFIED PRODUCTS</h4>
              </div>

              <div className="text-center p-3 sm:p-4 md:p-5 flex flex-col justify-center bg-white/5 rounded-lg backdrop-blur-sm border border-white/10 min-h-[100px] sm:min-h-[120px]">
                <div className="text-xl sm:text-2xl md:text-3xl mb-1 sm:mb-2">⭐</div>
                <div className="text-white text-sm sm:text-base md:text-lg font-bold font-['Open_Sans'] mb-1 sm:mb-2">40+</div>
                <h4 className="text-white text-xs sm:text-sm font-semibold font-['Open_Sans'] leading-tight">YEARS EXPERIENCE</h4>
              </div>

              <div className="text-center p-3 sm:p-4 md:p-5 flex flex-col justify-center bg-white/5 rounded-lg backdrop-blur-sm border border-white/10 min-h-[100px] sm:min-h-[120px]">
                <div className="text-xl sm:text-2xl md:text-3xl mb-1 sm:mb-2">🌍</div>
                <div className="text-white text-sm sm:text-base md:text-lg font-bold font-['Open_Sans'] mb-1 sm:mb-2">ISO</div>
                <h4 className="text-white text-xs sm:text-sm font-semibold font-['Open_Sans'] leading-tight">CERTIFIED QUALITY STANDARDS</h4>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const ProductCategories = () => (
    <div className="py-12 sm:py-16 lg:py-20 bg-white w-full overflow-hidden">
      <div className="container-responsive">
        <div className="mb-8 sm:mb-12 lg:mb-16 text-center">
          <h2 className="text-responsive-2xl font-bold text-gray-800 mb-6 font-['Open_Sans']">Our Product Categories</h2>
          <div className="w-24 sm:w-32 md:w-40 h-1 bg-blue-600 mx-auto rounded-full"></div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 sm:gap-8 lg:gap-10 w-full">
          {productCategories.map((category, index) => (
            <div key={index} className="group rounded-2xl overflow-hidden shadow-xl bg-white transform transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl border border-gray-100 flex flex-col">
              <div className="relative h-64 sm:h-72 md:h-80 lg:h-96 bg-gray-200 overflow-hidden">
                <img
                  src={category.image}
                  alt={`${category.title} - Atandra Energy Solutions`}
                  className="absolute inset-0 w-full h-full object-cover object-center transform transition-transform duration-700 group-hover:scale-110"
                  style={{
                    imageRendering: 'crisp-edges'
                  }}
                  loading="lazy"
                  onError={(e) => {
                    console.error(`Product category image failed to load:`, e);
                    // Fallback to a gradient background
                    const parent = e.currentTarget.parentElement;
                    if (parent) {
                      parent.style.background = 'linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%)';
                      e.currentTarget.style.display = 'none';
                    }
                  }}
                />
                {/* Enhanced gradient overlay for better text readability */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
              <div className="p-6 sm:p-8 flex flex-col flex-grow">
                <h3 className="text-responsive-xl font-bold text-gray-800 mb-4 font-['Open_Sans']">{category.title}</h3>
                <p className="text-responsive-base text-gray-600 leading-relaxed flex-grow font-['Open_Sans'] text-justify">{category.description}</p>
                <div className="mt-6">
                  <Link to={category.redirectUrl} className="inline-block w-full sm:w-auto">
                    <button className="bg-blue-600 hover:bg-blue-700 text-white touch-target rounded-lg text-responsive-sm font-medium transition-all duration-300 w-full sm:w-auto px-6 py-3 font-['Open_Sans'] shadow-lg hover:shadow-xl">
                      Know More
                    </button>
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const CompanyStory = () => (
    <div className="py-12 sm:py-16 lg:py-20 bg-white w-full overflow-hidden">
      <div className="container-responsive">
        {/* Header Section */}
        <div className="text-center mb-8 sm:mb-12">
          <div className="relative inline-block mb-4 sm:mb-6">
            <h2 className="text-responsive-2xl font-bold font-['Open_Sans'] text-blue-600 relative z-10 pb-3">
              Network of Trust
            </h2>
            {/* Decorative underline */}
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-blue-600 rounded-full"></div>
          </div>

          {/* Subtitle with decorative line */}
          <div className="flex items-center justify-center gap-4 mb-6">
            <div className="h-px w-16 bg-blue-300"></div>
            <span className="text-sm font-semibold text-blue-600 tracking-wider uppercase font-['Open_Sans']">
              Nationwide Service Excellence
            </span>
            <div className="h-px w-16 bg-blue-300"></div>
          </div>

          <p className="text-gray-800 text-responsive-base leading-relaxed font-medium font-['Open_Sans'] max-w-4xl mx-auto text-justify">
            We built this nationwide footprint so you can focus on running your business, not chasing service calls. Fast response times, genuine parts, and expert technicians are just a phone call away—no matter how spread out your operations might be.
          </p>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-12 items-start">

          {/* Left Column - Map and Stats */}
          <div className="lg:col-span-1 space-y-6 sm:space-y-8">
            {/* Enhanced India Map Container - Mobile Responsive */}
            <div className="relative w-full bg-gradient-to-br from-blue-50 via-white to-indigo-100 rounded-xl sm:rounded-2xl shadow-xl sm:shadow-2xl overflow-hidden border border-blue-200 sm:border-2 transform transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl sm:hover:shadow-3xl">
              {/* Decorative border effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 via-transparent to-indigo-400/20 rounded-xl sm:rounded-2xl"></div>
              <div className="relative p-3 sm:p-4 md:p-6">
                <img
                  src="/background_images/Service-Locations-India.jpeg"
                  alt="India Service Locations Map - Krykard Network Coverage"
                  className="w-full h-auto rounded-lg sm:rounded-xl shadow-md sm:shadow-lg transform transition-transform duration-500 hover:scale-105"
                  style={{
                    minHeight: '250px',
                    maxHeight: '400px',
                    aspectRatio: '4/3',
                    objectFit: 'contain',
                    objectPosition: 'center center',
                    imageRendering: 'crisp-edges',
                    filter: 'contrast(1.1) saturate(1.1)'
                  }}
                  loading="eager"
                  onError={(e) => {
                    console.error('India map failed to load:', e);
                    const fallback = e.currentTarget.parentElement?.querySelector('.map-fallback');
                    if (fallback) {
                      fallback.classList.remove('hidden');
                      e.currentTarget.style.display = 'none';
                    }
                  }}
                />
                {/* Subtle overlay for enhanced visual appeal */}
                <div className="absolute inset-3 sm:inset-4 md:inset-6 bg-gradient-to-t from-blue-50/30 to-transparent rounded-lg sm:rounded-xl pointer-events-none"></div>
              </div>
              {/* Fallback content if image fails to load */}
              <div className="map-fallback hidden absolute inset-0 flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 text-gray-600">
                <div className="text-center p-4 sm:p-6">
                  <MapPin className="h-16 w-16 sm:h-20 sm:w-20 mx-auto mb-3 sm:mb-4 text-blue-400" />
                  <p className="text-lg sm:text-xl font-bold text-blue-800 font-['Open_Sans']">India Service Network Map</p>
                  <p className="text-sm sm:text-base font-medium text-blue-600 font-['Open_Sans']">100+ Service Centers Nationwide</p>
                  <p className="text-xs sm:text-sm text-blue-500 mt-2 font-['Open_Sans']">Map temporarily unavailable</p>
                </div>
              </div>
            </div>

            {/* Enhanced Quick Stats Cards - Mobile Responsive */}
            <div className="grid grid-cols-2 gap-3 sm:gap-4 md:gap-6">
              <div className="bg-gradient-to-br from-blue-50 via-blue-100 to-blue-200 rounded-lg sm:rounded-xl p-3 sm:p-4 md:p-6 text-center border border-blue-300 sm:border-2 shadow-md sm:shadow-lg transform transition-all duration-300 hover:scale-105 hover:shadow-lg sm:hover:shadow-xl">
                <div className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-blue-600 mb-1 sm:mb-2 font-['Open_Sans']">100+</div>
                <div className="text-xs sm:text-sm font-semibold text-blue-800 font-['Open_Sans'] leading-tight">Service Centers</div>
              </div>
              <div className="bg-gradient-to-br from-green-50 via-green-100 to-green-200 rounded-lg sm:rounded-xl p-3 sm:p-4 md:p-6 text-center border border-green-300 sm:border-2 shadow-md sm:shadow-lg transform transition-all duration-300 hover:scale-105 hover:shadow-lg sm:hover:shadow-xl">
                <div className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-green-600 mb-1 sm:mb-2 font-['Open_Sans']">24/7</div>
                <div className="text-xs sm:text-sm font-semibold text-green-800 font-['Open_Sans'] leading-tight">Support Available</div>
              </div>
              <div className="bg-gradient-to-br from-purple-50 via-purple-100 to-purple-200 rounded-lg sm:rounded-xl p-3 sm:p-4 md:p-6 text-center border border-purple-300 sm:border-2 shadow-md sm:shadow-lg transform transition-all duration-300 hover:scale-105 hover:shadow-lg sm:hover:shadow-xl">
                <div className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-purple-600 mb-1 sm:mb-2 font-['Open_Sans']">24hrs</div>
                <div className="text-xs sm:text-sm font-semibold text-purple-800 font-['Open_Sans'] leading-tight">Response Time</div>
              </div>
              <div className="bg-gradient-to-br from-orange-50 via-orange-100 to-orange-200 rounded-lg sm:rounded-xl p-3 sm:p-4 md:p-6 text-center border border-orange-300 sm:border-2 shadow-md sm:shadow-lg transform transition-all duration-300 hover:scale-105 hover:shadow-lg sm:hover:shadow-xl">
                <div className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-orange-600 mb-1 sm:mb-2 font-['Open_Sans']">100%</div>
                <div className="text-xs sm:text-sm font-semibold text-orange-800 font-['Open_Sans'] leading-tight">Genuine Parts</div>
              </div>
            </div>
          </div>

          {/* Right Column - Key Highlights */}
          <div className="lg:col-span-2 font-['Open_Sans']">
            <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 border border-gray-200 shadow-lg">
              <div className="text-center lg:text-left mb-6">
                <h3 className="text-responsive-xl font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent font-['Open_Sans'] mb-2">
                  Service Excellence
                </h3>
                <div className="flex items-center justify-center lg:justify-start gap-3">
                  <div className="h-1 w-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full"></div>
                  <span className="text-responsive-lg font-semibold text-gray-700 font-['Open_Sans']">Across India</span>
                  <div className="h-1 w-12 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full"></div>
                </div>
              </div>
              <p className="text-gray-800 text-responsive-base leading-relaxed font-medium font-['Open_Sans'] mb-6 text-justify">
                From snow-clad valleys to sun-baked coastlines, from bustling metros to emerging industrial hubs, every dot means a certified engineer is on standby, ready to deliver rapid UPS and stabilizer support.
              </p>
            </div>

            {/* Split highlights into responsive columns */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
              {/* Column 1 */}
              <div className="space-y-4">
                <div className="flex items-start">
                  <span className="text-red-500 text-lg sm:text-xl mr-3 flex-shrink-0 mt-1">🚩</span>
                  <span className="text-gray-800 text-responsive-sm font-medium font-['Open_Sans'] leading-relaxed">
                    <strong className="text-red-600">Nationwide Coverage:</strong> <span className="text-justify">100+ centers across every state and key industrial region.</span>
                  </span>
                </div>
                <div className="flex items-start">
                  <span className="text-yellow-500 text-lg sm:text-xl mr-3 flex-shrink-0 mt-1">⚡</span>
                  <span className="text-gray-800 text-responsive-sm font-medium font-['Open_Sans'] leading-relaxed">
                    <strong className="text-yellow-600">24-Hour Response:</strong> <span className="text-justify">Guaranteed on-site support within 24 hours, 365 days a year.</span>
                  </span>
                </div>
                <div className="flex items-start">
                  <span className="text-blue-600 text-lg sm:text-xl mr-3 flex-shrink-0 mt-1">👨‍🔧</span>
                  <span className="text-gray-800 text-responsive-sm font-medium font-['Open_Sans'] leading-relaxed">
                    <strong className="text-blue-600">Certified Technicians:</strong> <span className="text-justify">Local engineers trained on latest UPS technologies.</span>
                  </span>
                </div>
                <div className="flex items-start">
                  <span className="text-gray-600 text-lg sm:text-xl mr-3 flex-shrink-0 mt-1">🛠️</span>
                  <span className="text-gray-800 text-responsive-sm font-medium font-['Open_Sans'] leading-relaxed">
                    <strong className="text-gray-600">Stocked Inventory:</strong> <span className="text-justify">Genuine replacement parts available at every center.</span>
                  </span>
                </div>
              </div>

              {/* Column 2 */}
              <div className="space-y-4">
                <div className="flex items-start">
                  <span className="text-blue-500 text-lg sm:text-xl mr-3 flex-shrink-0 mt-1">🌐</span>
                  <span className="text-gray-800 text-responsive-sm font-medium font-['Open_Sans'] leading-relaxed">
                    <strong className="text-blue-500">Remote Monitoring:</strong> <span className="text-justify">Proactive system health checks with real-time alerts.</span>
                  </span>
                </div>
                <div className="flex items-start">
                  <span className="text-green-600 text-lg sm:text-xl mr-3 flex-shrink-0 mt-1">📞</span>
                  <span className="text-gray-800 text-responsive-sm font-medium font-['Open_Sans'] leading-relaxed">
                    <strong className="text-green-600">24/7 Helpline:</strong> <span className="text-justify">Toll-free support line for instant troubleshooting.</span>
                  </span>
                </div>
                <div className="flex items-start">
                  <span className="text-purple-600 text-lg sm:text-xl mr-3 flex-shrink-0 mt-1">🔄</span>
                  <span className="text-gray-800 text-responsive-sm font-medium font-['Open_Sans'] leading-relaxed">
                    <strong className="text-purple-600">Preventive Maintenance:</strong> <span className="text-justify">Scheduled inspections to minimize downtime.</span>
                  </span>
                </div>
                <div className="flex items-start">
                  <span className="text-indigo-600 text-lg sm:text-xl mr-3 flex-shrink-0 mt-1">📆</span>
                  <span className="text-gray-800 text-responsive-sm font-medium font-['Open_Sans'] leading-relaxed">
                    <strong className="text-indigo-600">Easy Scheduling:</strong> <span className="text-justify">Book service visits online or via mobile app.</span>
                  </span>
                </div>
                <div className="flex items-start">
                  <span className="text-orange-600 text-lg sm:text-xl mr-3 flex-shrink-0 mt-1">🔒</span>
                  <span className="text-gray-800 text-responsive-sm font-medium font-['Open_Sans'] leading-relaxed">
                    <strong className="text-orange-600">Warranty Support:</strong> <span className="text-justify">Comprehensive warranty fulfillment and extensions.</span>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );



  const ContactModal = () => (
    showContactModal && (
      <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center padding-responsive-sm overflow-hidden">
        <div className="bg-white rounded-lg max-w-md w-full mx-4 padding-responsive-md max-h-[90vh] overflow-y-auto no-overlap shadow-2xl">
          <div className="flex justify-between items-center mb-4 sm:mb-6">
            <h3 className="text-responsive-lg font-semibold font-['Open_Sans']">Quick Enquiry</h3>
            <button
              onClick={() => setShowContactModal(false)}
              className="text-gray-400 hover:text-gray-600 touch-target rounded-md"
              aria-label="Close modal"
            >
              <X className="h-5 w-5 sm:h-6 sm:w-6" />
            </button>
          </div>
          <div className="spacing-responsive-sm">
            <input
              type="text"
              placeholder="Your Name"
              className="w-full px-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 text-base touch-manipulation font-['Open_Sans']"
            />
            <input
              type="email"
              placeholder="Email Address"
              className="w-full px-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 text-base touch-manipulation font-['Open_Sans']"
            />
            <input
              type="tel"
              placeholder="Phone Number"
              className="w-full px-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 text-base touch-manipulation font-['Open_Sans']"
            />
            <textarea
              placeholder="Your Message"
              rows={4}
              className="w-full px-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 text-base resize-none touch-manipulation font-['Open_Sans']"
            />
            <button
              onClick={() => setShowContactModal(false)}
              className="w-full bg-gray-600 hover:bg-gray-700 text-white touch-target rounded-md transition-colors text-base font-medium font-['Open_Sans']"
            >
              Send Enquiry
            </button>
          </div>
        </div>
      </div>
    )
  );

  return (
    <Layout>
      <div className="min-h-screen">
        <HeroCarousel />
        <ProductCategories />
        <StatsSection />
        <CompanyStory />
        <ClientLogosSection isInView={true} />
        <ContactModal />
      </div>
    </Layout>
  );
};

export default Krykard;