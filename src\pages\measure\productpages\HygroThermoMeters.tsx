import React, { useState } from 'react';

interface ProductSpec {
  label: string;
  description: string;
}

interface TableRow {
  parameter: string;
  range: string;
  accuracy: string;
}

interface Product {
  id: string;
  name: string;
  title: string;
  subtitle?: string;
  model: string;
  image: string;
  description?: string;
  features: string[];
  specs: ProductSpec[];
  hasRangeTable?: boolean;
  rangeData?: TableRow[];
}

const HydroThermoMeters: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('logger-thermo-hygrometers');
  
  const products: Product[] = [
    {
      id: 'logger-thermo-hygrometers',
      name: 'Logger Thermo-Hygrometers',
      title: 'LOGGER THERMO-',
      subtitle: 'HYGROMETERS',
      model: 'C.A 1246',
      image: '/images/logger-thermo-hygrometers.png',
      description: 'The C.A 1246 is a 3-in-1 instrument for measuring relative humidity, ambient temperature and the dew point. Equipped with a wide backlit display, the C.A 1246 offers all the useful functions for work in the field, such as Min, Max, Hold, as well as spot or programmable recording possibilities. Communicating via USB or Bluetooth, it is possible to program alarms and recording triggers on thresholds by means of the Data Logger Transfer software.',
      features: [
        'Models: C.A 1246',
        'Compact and magnetized for fixed or portable use',
        'Wide backlit display',
        'Recording up to 1 million points',
        'Communicating via USB or Bluetooth',
        'Alarms and recording trigger on alarms',
        'Battery life of up to 3 years',
        'IP54 casing',
        'Data Logger Transfer software with automatic report generation'
      ],
      specs: [
        { label: 'Relative humidity', description: '0.0 to 98.0 %RH' },
        { label: 'Ambient temperature', description: '-10.0 to +60.0°C' },
        { label: 'Dew point', description: '-20.0 to +60.0 °Ctd' },
        { label: 'Min, Max, Hold functions, Alarms', description: '°C or °F / Backlighting' },
        { label: 'Recording', description: 'up to 1 million points' },
        { label: 'USB or Bluetooth interfaces', description: '' },
        { label: 'IP54 casing', description: '' },
        { label: 'Dimensions', description: '137 x 72 x 32 mm' },
        { label: 'Weight', description: '240 g with batteries' },
        { label: 'Compatible with the MultiFix accessory', description: '' },
        { label: 'Shockproof protective sheath', description: 'available as an accessory' }
      ]
    },
    {
      id: 'logger-hygro-thermometer',
      name: 'Logger Hygro Thermometer',
      title: 'LOGGER HYGRO',
      subtitle: 'THERMOMETER',
      model: 'ATEST EH 540D',
      image: '/images/logger-hygro-thermometer.png',
      features: [
        'Models - ATEST EH 540D',
        'Interchangeable Digital probes',
        'Thermistor Sensor For Temperature measurement, Fast Response Time',
        'Dual Display',
        'Dew Point measurement',
        'Response Time (@90%): Humidity: 60 sec Temperature: 10 sec',
        'PC Interface & Extending Cable Available',
        '16,000 Records Data Logger',
        'Auto Shut Off For Battery Saving',
        'Sensor: Electronic capacitance polymer film sensor/NTC'
      ],
      specs: [
        { label: 'Interchangeable Digital probes', description: '' },
        { label: 'Thermistor Sensor', description: 'For Temperature measurement with Fast Response Time' },
        { label: 'Dual Display', description: 'Shows temperature and humidity readings simultaneously' },
        { label: 'Dew Point measurement', description: 'Calculates dew point automatically' },
        { label: 'Response Time (@90%)', description: 'Humidity: 60 sec, Temperature: 10 sec' },
        { label: 'PC Interface', description: 'For data transfer and analysis' },
        { label: 'Extending Cable', description: 'Available for remote measurements' },
        { label: '16,000 Records Data Logger', description: 'For long-term monitoring' },
        { label: 'Auto Shut Off', description: 'For Battery Saving' }
      ],
      hasRangeTable: true,
      rangeData: [
        { parameter: 'Temperature °C', range: '-20~60°C', accuracy: '0.8' },
        { parameter: 'Temperature °F', range: '-4~140°F', accuracy: '1.5' },
        { parameter: 'Humidity', range: '0~100%RH', accuracy: '2' }
      ]
    },
    {
      id: 'hygro-thermometer-520',
      name: 'Hygro Thermometer 520',
      title: 'HYGRO',
      subtitle: 'THERMOMETER',
      model: 'ATEST EH 520',
      image: '/images/hygro-thermometer-520.png',
      features: [
        'Models - ATEST EH 520',
        'Large, Easy-to-read Dual LCD Displays Humidity and Temperature',
        '3%RH Humidity accuracy',
        'Calibration available on top',
        'Desk Or wall Mount',
        'Min/Max Value Monitor',
        'Audible and Visual Alarms, with Adjustable Set Points, Warns If Limits Are Exceeded'
      ],
      specs: [
        { label: 'Dual LCD Display', description: 'Shows humidity and temperature simultaneously' },
        { label: 'Humidity accuracy', description: '3%RH for reliable measurements' },
        { label: 'Calibration', description: 'Available on top for easy adjustment' },
        { label: 'Mounting options', description: 'Flexible desk or wall mounting' },
        { label: 'Min/Max monitoring', description: 'Tracks extreme values automatically' },
        { label: 'Alarm system', description: 'Both audible and visual with adjustable set points' },
        { label: 'Limit warnings', description: 'Alerts when environmental conditions exceed safe ranges' }
      ],
      hasRangeTable: true,
      rangeData: [
        { parameter: 'Temperature °C', range: '-20~60°C', accuracy: '0.8' },
        { parameter: 'Temperature °F', range: '-4~140°F', accuracy: '1.5' },
        { parameter: 'Dew Point °C', range: '-20°C~60°C', accuracy: '0.8' },
        { parameter: 'Dew Point °F', range: '-4~140°F', accuracy: '1.5' },
        { parameter: 'Humidity', range: '0~100%RH', accuracy: '3' }
      ]
    },
    {
      id: 'humidity-temperature-meter-500',
      name: 'Humidity & Temperature Meter 500',
      title: 'HUMIDITY &',
      subtitle: 'TEMPERATURE METER',
      model: 'ATEST EH 500',
      image: '/images/humidity-temperature-meter-500.png',
      features: [
        'Models - ATEST EH 500',
        'LCD with Back Light',
        'Battery Status Display',
        'Memory Status Display',
        'Shipped with wall hanging tag',
        '22000 Records Datalogger per channel',
        'PC Interface',
        'Windows Software',
        'ATEST 600C Communication Base'
      ],
      specs: [
        { label: 'LCD with Back Light', description: 'For easy reading in dark environments' },
        { label: 'Status displays', description: 'Shows battery and memory status' },
        { label: 'Wall hanging', description: 'Comes with wall hanging tag for convenient placement' },
        { label: 'Data logging', description: '22000 records per channel for comprehensive monitoring' },
        { label: 'PC Interface', description: 'For data transfer and analysis' },
        { label: 'Software compatibility', description: 'Works with Windows software for data management' },
        { label: 'Communication base', description: 'ATEST 600C Communication Base included' }
      ],
      hasRangeTable: true,
      rangeData: [
        { parameter: 'Temperature °C', range: '-30~70°C', accuracy: '0.7' },
        { parameter: 'Temperature °F', range: '-22~158 °F', accuracy: '0.7' },
        { parameter: 'Humidity', range: '5%~98%RH', accuracy: '3' }
      ]
    },
    {
      id: 'humidity-temperature-meter-502',
      name: 'Humidity & Temperature Meter 502',
      title: 'HUMIDITY &',
      subtitle: 'TEMPERATURE METER',
      model: 'ATEST EH 502',
      image: '/images/humidity-temperature-meter-502.png',
      features: [
        'Models - ATEST EH 502',
        'Dew Point and Wet Bulb Temperature measurement',
        'Fast Response Time',
        'Data Hold',
        'Auto Shut Off For Battery Saving',
        'Min/Max function',
        'Dual Displays: Primary Display For Humidity',
        'Secondary Displays For Temperature'
      ],
      specs: [
        { label: 'Measurement capabilities', description: 'Measures dew point and wet bulb temperature' },
        { label: 'Response time', description: 'Fast response for quick measurements' },
        { label: 'Data functions', description: 'Data hold and Min/Max tracking' },
        { label: 'Power saving', description: 'Auto shut off for longer battery life' },
        { label: 'Display system', description: 'Dual displays showing humidity and temperature simultaneously' }
      ],
      hasRangeTable: true,
      rangeData: [
        { parameter: 'Temperature °C', range: '-20~60°C', accuracy: '0.8' },
        { parameter: 'Temperature °F', range: '-4~140 °F', accuracy: '1.6' },
        { parameter: 'Humidity', range: '0~99%RH', accuracy: '3' }
      ]
    },
    {
      id: 'humidity-temperature-meter-504',
      name: 'Humidity & Temperature Meter 504',
      title: 'HUMIDITY &',
      subtitle: 'TEMPERATURE METER',
      model: 'ATEST EH 504',
      image: '/images/humidity-temperature-meter-504.png',
      features: [
        'Models - ATEST EH 504',
        'Dew Point and Wet Bulb Temperature measurement',
        'Fast Response',
        'Auto Shut Off For Battery Saving',
        'Min/Max function',
        'Data Hold',
        'Dual Displays: Primary Display For Humidity',
        'Secondary Displays For Temperature',
        'LED Back Light'
      ],
      specs: [
        { label: 'Measurement capabilities', description: 'Measures dew point and wet bulb temperature' },
        { label: 'Response speed', description: 'Fast response for real-time readings' },
        { label: 'Battery preservation', description: 'Auto shut off extends battery life' },
        { label: 'Data functions', description: 'Min/Max tracking and Data hold for analysis' },
        { label: 'Display features', description: 'Dual displays with LED backlight for low-light conditions' }
      ],
      hasRangeTable: true,
      rangeData: [
        { parameter: 'Temperature °C', range: '-20~60°C', accuracy: '0.8' },
        { parameter: 'Temperature °F', range: '-4~140 °F', accuracy: '1.6' },
        { parameter: 'Humidity', range: '0~99%RH', accuracy: '2.5' }
      ]
    }
  ];
  
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };
  
  const activeProduct = products.find(product => product.id === activeTab);
  
  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-md">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="text-2xl font-bold text-gray-800">Measurement Solutions</div>
            <nav className="hidden md:flex space-x-4">
              <a href="#" className="text-gray-600 hover:text-yellow-600">Overview</a>
              <a href="#" className="text-gray-600 hover:text-yellow-600">Thermo Anemometer</a>
              <a href="#" className="text-gray-600 hover:text-yellow-600">Lux Meter</a>
              <a href="#" className="text-gray-600 hover:text-yellow-600">Tachometers</a>
              <a href="#" className="text-gray-600 hover:text-yellow-600">Gas Meter</a>
              <a href="#" className={`text-gray-600 hover:text-yellow-600 ${activeTab.includes('hygro') || activeTab.includes('logger') ? 'text-yellow-600 font-medium' : ''}`}>Logger Thermo-Hygrometers</a>
            </nav>
            <div className="md:hidden">
              <button className="text-gray-600 hover:text-yellow-600">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </header>
      
      {/* Product Tabs */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex overflow-x-auto py-4 space-x-6">
            {products.map(product => (
              <button
                key={product.id}
                className={`whitespace-nowrap px-4 py-2 font-medium rounded-md transition-colors ${
                  activeTab === product.id 
                    ? 'bg-yellow-500 text-white' 
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
                onClick={() => handleTabChange(product.id)}
              >
                {product.name}
              </button>
            ))}
          </div>
        </div>
      </div>
      
      {/* Main Content */}
      {activeProduct && (
        <main className="flex-grow container mx-auto px-4 py-8">
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            {/* Product Hero Section */}
            <div className="relative">
              <div className={`absolute inset-0 ${activeTab.includes('logger') ? 'bg-gray-100' : 'bg-yellow-50'}`}></div>
              <div className="relative flex flex-col md:flex-row p-6 md:p-12 items-center">
                <div className="w-full md:w-1/3 flex justify-center mb-8 md:mb-0">
                  <div className="bg-white p-4 rounded-lg shadow-md transform hover:scale-105 transition-transform duration-300">
                    <img 
                      src={activeProduct.image} 
                      alt={activeProduct.name} 
                      className="max-h-80 object-contain"
                    />
                  </div>
                </div>
                <div className="w-full md:w-2/3 md:pl-12">
                  <div className="flex flex-col">
                    <h1 className="text-4xl font-normal text-gray-700 mb-2">
                      {activeProduct.title}
                    </h1>
                    {activeProduct.subtitle && (
                      <h2 className="text-4xl font-bold text-yellow-500 mb-6">
                        {activeProduct.subtitle}
                      </h2>
                    )}
                  </div>
                  <p className="text-lg font-medium mb-4">Model: {activeProduct.model}</p>
                  
                  {/* Features List */}
                  <div className="mb-6">
                    <ul className="space-y-3">
                      {activeProduct.features.map((feature, index) => (
                        <li key={index} className="flex items-start">
                          <div className="flex-shrink-0 h-5 w-5 rounded-full bg-yellow-500 mt-1 mr-3"></div>
                          <span className="text-gray-700">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="flex space-x-4 mt-6">
                    <button className="px-6 py-2 bg-transparent border-2 border-yellow-500 text-yellow-500 font-medium rounded-full hover:bg-yellow-50 transition-colors">
                      ENQUIRE
                    </button>
                    <button className="px-6 py-2 bg-transparent border-2 border-yellow-500 text-yellow-500 font-medium rounded-full hover:bg-yellow-50 transition-colors">
                      BROCHURE
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Product Description */}
            {activeProduct.description && (
              <div className="p-6 md:p-12 bg-yellow-50 border-t border-yellow-100">
                <div className="flex flex-col md:flex-row">
                  <div className="w-full md:w-2/3 md:pr-8">
                    <p className="text-gray-700 leading-relaxed">
                      {activeProduct.description}
                    </p>
                  </div>
                  <div className="w-full md:w-1/3 mt-6 md:mt-0">
                    <img 
                      src="/images/product-in-use.jpg" 
                      alt="Product in use" 
                      className="rounded-lg shadow-md w-full h-auto" 
                    />
                  </div>
                </div>
              </div>
            )}
            
            {/* Product Specifications */}
            <div className="p-6 md:p-12 border-t border-gray-200">
              <h3 className="text-2xl font-bold text-gray-700 mb-8">Specifications</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {activeProduct.specs.map((spec, index) => (
                  <div key={index} className="flex items-start">
                    <div className="flex-shrink-0 flex items-start">
                      <div className="h-2 w-2 rounded-full bg-gray-500 mt-2 mr-2"></div>
                    </div>
                    <div>
                      <span className="font-bold text-gray-700">{spec.label}: </span>
                      <span className="text-gray-600">{spec.description}</span>
                    </div>
                  </div>
                ))}
              </div>
              
              {/* Range Table */}
              {activeProduct.hasRangeTable && activeProduct.rangeData && (
                <div className="mt-12 overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr>
                        <th className="px-4 py-3 bg-yellow-500 text-white font-bold text-left">PARAMETER</th>
                        <th className="px-4 py-3 bg-yellow-500 text-white font-bold text-left">RANGE</th>
                        <th className="px-4 py-3 bg-yellow-500 text-white font-bold text-left">ACCURACY %</th>
                      </tr>
                    </thead>
                    <tbody>
                      {activeProduct.rangeData.map((row, index) => (
                        <tr key={index} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                          <td className="px-4 py-3 border border-gray-200">{row.parameter}</td>
                          <td className="px-4 py-3 border border-gray-200">{row.range}</td>
                          <td className="px-4 py-3 border border-gray-200">{row.accuracy}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
            
            {/* Features Section */}
            <div className="p-6 md:p-12 bg-gray-50 border-t border-gray-200">
              <h3 className="text-2xl font-bold text-gray-700 mb-8">Key Benefits</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                  <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
                    <svg className="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                  <h4 className="text-lg font-bold text-gray-700 mb-2">High Accuracy</h4>
                  <p className="text-gray-600">Precise measurements for professional field applications with minimal uncertainty.</p>
                </div>
                
                <div className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                  <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
                    <svg className="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                  <h4 className="text-lg font-bold text-gray-700 mb-2">User-Friendly Design</h4>
                  <p className="text-gray-600">Wide backlit display and intuitive controls for easy operation in all working conditions.</p>
                </div>
                
                <div className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                  <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
                    <svg className="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                    </svg>
                  </div>
                  <h4 className="text-lg font-bold text-gray-700 mb-2">Advanced Data Management</h4>
                  <p className="text-gray-600">Record up to 1 million points with USB and Bluetooth connectivity for comprehensive analysis.</p>
                </div>
              </div>
            </div>
            
            {/* Related Products */}
            <div className="p-6 md:p-12 border-t border-gray-200">
              <h3 className="text-2xl font-bold text-gray-700 mb-8">Related Products</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                {products.filter(product => product.id !== activeTab).slice(0, 4).map(product => (
                  <div key={product.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                    <div className="p-4">
                      <img 
                        src={product.image} 
                        alt={product.name} 
                        className="w-full h-40 object-contain mb-4" 
                      />
                      <h4 className="text-lg font-bold text-gray-700 mb-2">{product.name}</h4>
                      <p className="text-sm text-gray-600 mb-4">Model: {product.model}</p>
                      <button 
                        className="w-full py-2 bg-yellow-500 text-white font-medium rounded-md hover:bg-yellow-600 transition-colors"
                        onClick={() => handleTabChange(product.id)}
                      >
                        View Details
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Call to Action */}
            <div className="p-6 md:p-12 bg-gray-800 text-white text-center">
              <h3 className="text-2xl font-bold mb-4">Need Professional Measurement Solutions?</h3>
              <p className="text-gray-300 mb-6 max-w-2xl mx-auto">Contact our team of experts to find the right equipment for your specific measurement and monitoring needs.</p>
              <button className="px-8 py-3 bg-yellow-500 text-white font-medium rounded-md hover:bg-yellow-600 transition-colors">
                REQUEST A QUOTE
              </button>
            </div>
          </div>
        </main>
      )}
      
      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h4 className="text-lg font-bold mb-4">About Us</h4>
              <p className="text-gray-400">Providing high-quality measurement solutions for professional field applications and environmental monitoring.</p>
            </div>
            <div>
              <h4 className="text-lg font-bold mb-4">Products</h4>
              <ul className="space-y-2">
                {products.map(product => (
                  <li key={product.id}>
                    <a href="#" className="text-gray-400 hover:text-yellow-500 transition-colors" onClick={() => handleTabChange(product.id)}>
                      {product.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-bold mb-4">Support</h4>
              <ul className="space-y-2">
                <li><a href="#" className="text-gray-400 hover:text-yellow-500 transition-colors">Technical Support</a></li>
                <li><a href="#" className="text-gray-400 hover:text-yellow-500 transition-colors">User Manuals</a></li>
                <li><a href="#" className="text-gray-400 hover:text-yellow-500 transition-colors">FAQs</a></li>
                <li><a href="#" className="text-gray-400 hover:text-yellow-500 transition-colors">Contact Us</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-bold mb-4">Connect With Us</h4>
              <div className="flex space-x-4">
                <a href="#" className="text-gray-400 hover:text-yellow-500 transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"></path>
                  </svg>
                </a>
                <a href="#" className="text-gray-400 hover:text-yellow-500 transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.198.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
                  </svg>
                </a>
                <a href="#" className="text-gray-400 hover:text-yellow-500 transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"></path>
                  </svg>
                </a>
              </div>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; {new Date().getFullYear()} Measurement Solutions. All rights reserved.</p>
          </div>
        </div>
      </footer>

      {/* Social Media Sidebar */}
      <div className="fixed right-0 top-1/2 transform -translate-y-1/2 bg-red-600 py-2 px-1 flex flex-col items-center space-y-3 rounded-l-md z-50">
        <a href="#" className="text-white hover:text-gray-200" title="Share">
          <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z"></path>
          </svg>
        </a>
        <a href="#" className="text-white hover:text-gray-200" title="Facebook">
          <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"></path>
          </svg>
        </a>
        <a href="#" className="text-white hover:text-gray-200" title="Twitter">
          <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
          </svg>
        </a>
        <a href="#" className="text-white hover:text-gray-200" title="LinkedIn">
          <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"></path>
          </svg>
        </a>
        <a href="#" className="text-white hover:text-gray-200" title="Email">
          <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"></path>
          </svg>
        </a>
      </div>
    </div>
  );
};

export default HydroThermoMeters;

// Additional components that could be extracted for better organization

// ProductFeature.tsx
/*
import React from 'react';

interface ProductFeatureProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const ProductFeature: React.FC<ProductFeatureProps> = ({ icon, title, description }) => {
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
      <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
        {icon}
      </div>
      <h4 className="text-lg font-bold text-gray-700 mb-2">{title}</h4>
      <p className="text-gray-600">{description}</p>
    </div>
  );
};

export default ProductFeature;
*/

// FeatureList.tsx
/*
import React from 'react';

interface FeatureListProps {
  features: string[];
}

const FeatureList: React.FC<FeatureListProps> = ({ features }) => {
  return (
    <ul className="space-y-3">
      {features.map((feature, index) => (
        <li key={index} className="flex items-start">
          <div className="flex-shrink-0 h-5 w-5 rounded-full bg-yellow-500 mt-1 mr-3"></div>
          <span className="text-gray-700">{feature}</span>
        </li>
      ))}
    </ul>
  );
};

export default FeatureList;
*/

// ProductCard.tsx
/*
import React from 'react';
import PdfViewer from "@/components/ui/pdf-viewer";

interface ProductCardProps {
  id: string;
  name: string;
  model: string;
  image: string;
  onSelect: (id: string) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ id, name, model, image, onSelect }) => {
  return (
    <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
      <div className="p-4">
        <img 
          src={image} 
          alt={name} 
          className="w-full h-40 object-contain mb-4" 
        />
        <h4 className="text-lg font-bold text-gray-700 mb-2">{name}</h4>
        <p className="text-sm text-gray-600 mb-4">Model: {model}</p>
        <button 
          className="w-full py-2 bg-yellow-500 text-white font-medium rounded-md hover:bg-yellow-600 transition-colors"
          onClick={() => onSelect(id)}
        >
          View Details
        </button>
      </div>
    </div>
  );
};

export default ProductCard;
*/