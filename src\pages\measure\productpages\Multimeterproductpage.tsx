import React, { useState } from 'react';
// import PdfViewer from "@/components/ui/pdf-viewer";

// Shared Components
const SocialButton: React.FC<{ icon: string; light?: boolean }> = ({ icon, light }) => {
  const baseClasses = "w-8 h-8 flex items-center justify-center rounded-full";
  const lightClasses = light ? "bg-gray-700 hover:bg-gray-600" : "bg-gray-200 hover:bg-gray-300 text-gray-700";
  return (
    <a href="#" className={`${baseClasses} ${lightClasses} transition-colors duration-200`}>
      {icon === "facebook" && "f"}
      {icon === "twitter" && "t"}
      {icon === "linkedin" && "in"}
      {icon === "youtube" && "yt"}
      {icon === "message" && "msg"}
    </a>
  );
};

const FeatureItem: React.FC<{ children: React.ReactNode; small?: boolean }> = ({ children, small }) => (
  <li className="flex items-start gap-2">
    <div className={`${small ? 'w-4 h-4' : 'w-5 h-5'} mt-1 flex-shrink-0 rounded-full bg-yellow-500 flex items-center justify-center text-white`}>
      ✓
    </div>
    <span className={small ? 'text-sm' : ''}>{children}</span>
  </li>
);

// Header Component
const Header: React.FC<{ title: string }> = ({ title }) => (
  <header className="bg-gray-100 py-4 border-b">
    <div className="container mx-auto px-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">{title}</h1>
        <div className="flex space-x-1">
          <SocialButton icon="facebook" />
          <SocialButton icon="twitter" />
          <SocialButton icon="linkedin" />
          <SocialButton icon="youtube" />
          <SocialButton icon="message" />
        </div>
      </div>
    </div>
  </header>
);

// Footer Component
const Footer: React.FC = () => (
  <footer className="bg-gray-900 text-white py-8">
    <div className="container mx-auto px-4">
      <div className="flex justify-between items-center">
        <p>© 2025 CHAUVIN ARNOUX. All rights reserved.</p>
        <div className="flex space-x-4">
          <SocialButton icon="facebook" light />
          <SocialButton icon="twitter" light />
          <SocialButton icon="linkedin" light />
          <SocialButton icon="youtube" light />
        </div>
      </div>
    </div>
  </footer>
);

// Section 1: CHAUVIN ARNOUX Product Overview
const ChauvinArnouxProducts: React.FC = () => (
  <div>
    {/* TRMS AC Digital Multimeters */}
    <section className="py-12 border-b">
      <div className="container mx-auto px-4">
        <div className="flex flex-col lg:flex-row items-center gap-8">
          <div className="lg:w-1/3">
            <img 
              src="/api/placeholder/400/500" 
              alt="TRMS AC Digital Multimeter MTX 203" 
              className="mx-auto"
            />
          </div>
          <div className="lg:w-2/3">
            <h2 className="text-5xl font-bold mb-6">
              <span className="text-gray-700">TRMS AC </span>
              <span className="text-yellow-500">DIGITAL MULTIMETERS</span>
            </h2>
            <ul className="space-y-4 mb-8">
              <FeatureItem>Models - MTX 203</FeatureItem>
              <FeatureItem>TRMS AC current and voltage measurements with auto-ranging</FeatureItem>
              <FeatureItem>NCV (no-contact voltage) detection for total safety</FeatureItem>
              <FeatureItem>Convenient backlit screen with built-in torch</FeatureItem>
              <FeatureItem>Ergonomic design for one-handed use</FeatureItem>
              <FeatureItem>Shockproof sheath with storage slots and magnetic mounting</FeatureItem>
            </ul>
            <div className="flex gap-4">
              <button className="px-8 py-2 bg-white text-yellow-500 border border-yellow-500 rounded-full hover:bg-yellow-50 transition">
                ENQUIRE
              </button>
              <button className="px-8 py-2 bg-white text-yellow-500 border border-yellow-500 rounded-full hover:bg-yellow-50 transition">
                BROCHURE
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    {/* Description Section for MTX 203 */}
    <section className="py-12 bg-yellow-100">
      <div className="container mx-auto px-4">
        <div className="flex flex-col lg:flex-row gap-8">
          <div className="lg:w-2/3">
            <p className="mb-4">
              The patented Multifix mounting system allows you to hook the multimeter onto a cabinet door or belt. The two-position stand ensures easy reading while the blue backlit display (available in 4,000 or 6,000 counts) improves clarity. The built-in torch enables use even in the dark.
            </p>
            <p className="mb-4">
              With a rotary switch that offers one function per position and 3 easily accessible keys on the front panel, the 600V CAT III, IP54 double-well input terminals are within reach for effortless operation.
            </p>
            <p className="mb-4">
              Electrical maintenance is optimized thanks to the VLowZ low-impedance voltage measurement function and ease of troubleshooting on PCBs by measuring resistance, capacitance, diode, etc.
            </p>
            <p className="mb-4">
              In addition to traditional measurements, MTX200 models measure temperature via a standard K thermocouple sensor.
            </p>
            <ul className="list-disc pl-6 mb-4">
              <li>Electrical maintenance</li>
              <li>Initial PCB troubleshooting</li>
              <li>Radiator control verification</li>
            </ul>
          </div>
          <div className="lg:w-1/3">
            <img 
              src="/api/placeholder/400/300" 
              alt="TRMS AC Digital Multimeter in use" 
              className="mx-auto rounded-lg shadow-lg"
            />
          </div>
        </div>
      </div>
    </section>

    {/* TRMS AC DC Digital Multimeters */}
    <section className="py-12 border-b">
      <div className="container mx-auto px-4">
        <div className="flex flex-col lg:flex-row items-center gap-8">
          <div className="lg:w-1/3">
            <img 
              src="/api/placeholder/400/500" 
              alt="TRMS AC DC Digital Multimeter C.A 5273" 
              className="mx-auto"
            />
          </div>
          <div className="lg:w-2/3">
            <h2 className="text-5xl font-bold mb-6">
              <span className="text-gray-700">TRMS AC DC </span>
              <span className="text-yellow-500">DIGITAL MULTIMETERS</span>
            </h2>
            <ul className="space-y-4 mb-8">
              <FeatureItem>Models - C.A 5273, C.A 5275, C.A 5277</FeatureItem>
              <FeatureItem>CAT IV 600V / CAT III 1,000V</FeatureItem>
              <FeatureItem>TRMS measurements</FeatureItem>
              <FeatureItem>Bi-mode double 6,000-count backlit display and 61+2-segment bargraph</FeatureItem>
              <FeatureItem>Auto AC/DC selection with manual range option</FeatureItem>
              <FeatureItem>VLowZ low-impedance voltage measurement with low-pass filter</FeatureItem>
              <FeatureItem>1,000V, 10A capacity</FeatureItem>
              <FeatureItem>Resistance, audible continuity, temperature, capacitance measurements</FeatureItem>
              <FeatureItem>Max/Min storage</FeatureItem>
              <FeatureItem>3-year warranty</FeatureItem>
            </ul>
            <div className="flex gap-4">
              <button className="px-8 py-2 bg-white text-yellow-500 border border-yellow-500 rounded-full hover:bg-yellow-50 transition">
                ENQUIRE
              </button>
              <button className="px-8 py-2 bg-white text-yellow-500 border border-yellow-500 rounded-full hover:bg-yellow-50 transition">
                BROCHURE
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    {/* Description Section for C.A 5273 */}
    <section className="py-12 bg-yellow-100">
      <div className="container mx-auto px-4">
        <div className="flex flex-col lg:flex-row gap-8">
          <div className="lg:w-2/3">
            <p className="mb-4">
              The C.A 5273 is designed for electrical maintenance of installations and small AC/DC machines. It features a double 6,000-count backlit display and a 61+2-segment bargraph with remanent effect.
            </p>
            <p className="mb-4">
              With 600V CAT IV safety and IP54 ingress protection, it is built and designed in France by CHAUVIN ARNOUX – backed by a 3-year warranty.
            </p>
          </div>
          <div className="lg:w-1/3">
            <img 
              src="/api/placeholder/400/300" 
              alt="Technician using C.A 5273 multimeter" 
              className="mx-auto rounded-lg shadow-lg"
            />
          </div>
        </div>
      </div>
    </section>
  </div>
);

// Section 2: ATEST Digital Multimeters (Tabbed Layout)
interface Product {
  id: number;
  name: string;
  color: "gray" | "red";
  image: string;
  features: string[];
  specs: { parameter: string; range: string; accuracy: string }[];
}

const AtestProducts: Product[] = [
  {
    id: 1,
    name: "ATEST M 3105",
    color: "gray",
    image: "/api/placeholder/400/500",
    features: [
      "600V ACV/DCV",
      "True RMS on ACV",
      "6000 count digital large display",
      "Auto ranging",
      "Relative function",
      "Non-Contact Voltage detection",
      "Continuity with buzzer and display",
      "μF/nF function",
      "High Voltage view",
      "Double size screen for clarity",
      "Safety standard CAT III 600V"
    ],
    specs: [
      { parameter: "DC Voltage", range: "60, 600, 600V", accuracy: "0.5" },
      { parameter: "AC Voltage", range: "60, 600, 600V", accuracy: "1" },
      { parameter: "Diode", range: "600mA", accuracy: "0.5" },
      { parameter: "ACA, DCA", range: "6A, 10A", accuracy: "1.5" },
      { parameter: "μCAP", range: "400μA, 4000μA", accuracy: "0.8" },
      { parameter: "Resistance", range: "600Ω-40MΩ", accuracy: "0.5" },
      { parameter: "Capacitance", range: "3nF ~ 3000μF", accuracy: "2" },
      { parameter: "Frequency Counter", range: "100Hz ~ 40KHz", accuracy: "0.1" },
      { parameter: "Diode Test", range: "3.000V", accuracy: "1" }
    ]
  },
  {
    id: 2,
    name: "ATEST M 3106",
    color: "gray",
    image: "/api/placeholder/400/500",
    features: [
      "600V ACV/DCV",
      "True RMS on ACV & DCA",
      "6000 count digital large display",
      "μF/nF function",
      "True Hold for capture peak readings",
      "Relative function",
      "Non-Contact Voltage detection",
      "Continuity with buzzer and display",
      "High Voltage view",
      "Double size screen for clarity",
      "Safety standard CAT III 600V"
    ],
    specs: [
      { parameter: "DC Voltage", range: "60, 600, 600V", accuracy: "0.5" },
      { parameter: "AC Voltage", range: "60, 600, 600V", accuracy: "1" },
      { parameter: "Diode", range: "600mA", accuracy: "0.5" },
      { parameter: "ACA, DCA", range: "6A, 10A", accuracy: "1.5" },
      { parameter: "μCAP", range: "400μA, 4000μA", accuracy: "0.8" },
      { parameter: "Resistance", range: "600Ω-40MΩ", accuracy: "0.5" },
      { parameter: "Capacitance", range: "3nF ~ 3000μF", accuracy: "2" },
      { parameter: "Frequency Counter", range: "100Hz ~ 40KHz", accuracy: "0.1" },
      { parameter: "Temperature", range: "-40°C ~ 400°C", accuracy: "1" },
      { parameter: "Diode Test", range: "3.000V", accuracy: "1" }
    ]
  },
  {
    id: 3,
    name: "ATEST M 2590",
    color: "red",
    image: "/api/placeholder/400/500",
    features: [
      "1000V ACV/DCV",
      "True RMS measurements on ACV",
      "6000 count digital and 60 segments analog display",
      "LoZ to prevent false readings from ghost voltage",
      "True Hold for capture peak readings",
      "Relative function",
      "Non-Contact Voltage detection",
      "Double with built-in magnetic holder",
      "Continuity with buzzer and display",
      "Battery capacity indication in segments",
      "Safety standard CAT IV 600V"
    ],
    specs: [
      { parameter: "DC Voltage", range: "600mV, 1000V", accuracy: "0.5" },
      { parameter: "AC Voltage", range: "600mV, 1000V", accuracy: "1" },
      { parameter: "Auto V-LoZ", range: "600mV, 1000V", accuracy: "2" },
      { parameter: "DC Current", range: "600μA", accuracy: "2" },
      { parameter: "AC Current", range: "600μA", accuracy: "1.5" },
      { parameter: "Resistance", range: "600Ω-40MΩ", accuracy: "0.5" },
      { parameter: "Capacitance", range: "1.000μF ~ 4,000μF", accuracy: "1.5" },
      { parameter: "Frequency Counter", range: "100Hz ~ 500KHz", accuracy: "0.1" },
      { parameter: "Diode Test", range: "3.000V", accuracy: "" }
    ]
  },
  {
    id: 4,
    name: "ATEST M 2591",
    color: "red",
    image: "/api/placeholder/400/500",
    features: [
      "1000V ACV/DCV",
      "True RMS measurements on ACV",
      "6000 count digital and 60 segments analog display",
      "μA measurements on both AC and DC",
      "LoZ to prevent false readings from ghost voltage",
      "True Hold for capture peak readings",
      "Relative function",
      "Non-Contact Voltage detection",
      "Double with built-in magnetic holder",
      "Continuity with buzzer and display",
      "Battery capacity indication in segments",
      "Safety standard CAT IV 600V"
    ],
    specs: [
      { parameter: "DC Voltage", range: "600mV, 1000V", accuracy: "0.5" },
      { parameter: "AC Voltage", range: "600mV, 1000V", accuracy: "1" },
      { parameter: "Auto V-LoZ", range: "600mV, 1000V", accuracy: "2" },
      { parameter: "DC Current", range: "600μA", accuracy: "2" },
      { parameter: "AC Current", range: "600μA", accuracy: "1.5" },
      { parameter: "Resistance", range: "600Ω-40MΩ", accuracy: "0.5" },
      { parameter: "Capacitance", range: "1μF ~ 10mF", accuracy: "1.5" },
      { parameter: "Frequency Counter", range: "100Hz ~ 500KHz", accuracy: "0.1" },
      { parameter: "Temperature", range: "-40°C ~ 400°C", accuracy: "1" },
      { parameter: "Diode Test", range: "3.000V", accuracy: "" }
    ]
  },
  {
    id: 5,
    name: "ATEST M 2592",
    color: "red",
    image: "/api/placeholder/400/500",
    features: [
      "1000V ACV/DCV",
      "True RMS measurements on ACV/ACA",
      "(AC+DC)V and (AC+DC)mV measurements",
      "(AC+DC)A and (AC+DC)mA measurements",
      "6000 count digital and 62 segments analog display",
      "LoZ to prevent false readings from ghost voltage",
      "Non-Contact Voltage detection",
      "11A/1000V & 400mA/1000V High Energy Fuses",
      "Shock proof from 4 feet drops",
      "Safety standard CAT IV 600V"
    ],
    specs: [
      { parameter: "DC Voltage", range: "60mV, 1000V", accuracy: "0.08" },
      { parameter: "AC Voltage", range: "60mV, 1000V", accuracy: "0.8" },
      { parameter: "Auto V-LoZ", range: "600V, 1000V", accuracy: "0.8" },
      { parameter: "DC Current", range: "60mA, 10A", accuracy: "0.8" },
      { parameter: "AC Current", range: "60mA, 10A", accuracy: "1.2" },
      { parameter: "Resistance", range: "600Ω-40MΩ", accuracy: "0.8" },
      { parameter: "Capacitance", range: "1μF-10mF", accuracy: "1.2" },
      { parameter: "Frequency Counter", range: "100Hz ~ 100KHz", accuracy: "0.1" },
      { parameter: "Temperature", range: "-40°C ~ 400°C", accuracy: "1" },
      { parameter: "Diode Test", range: "3.000V", accuracy: "" }
    ]
  }
];

const ProductCard: React.FC<{ product: Product; onClick: () => void }> = ({ product, onClick }) => (
  <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
    <div className="p-4">
      <div className="flex flex-col items-center mb-4">
        <img 
          src={product.image} 
          alt={`ATEST ${product.name} Digital Multimeter`} 
          className="h-64 object-contain"
        />
        <h3 className="text-xl font-semibold mt-4">{product.name}</h3>
      </div>
      <div className="mb-4">
        <ul className="space-y-2">
          {product.features.slice(0, 4).map((feature, index) => (
            <FeatureItem key={index} small>{feature}</FeatureItem>
          ))}
        </ul>
      </div>
      <div className="flex justify-center mt-4">
        <button 
          onClick={onClick}
          className="px-6 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 transition w-full"
        >
          View Details
        </button>
      </div>
    </div>
  </div>
);

const ProductDetail: React.FC<{ product: Product }> = ({ product }) => (
  <div className="space-y-8">
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="flex flex-col lg:flex-row">
        <div className="lg:w-1/3 p-8 flex justify-center items-center bg-gray-50">
          <img 
            src={product.image} 
            alt={`ATEST ${product.name} Digital Multimeter`} 
            className="max-h-96 object-contain"
          />
        </div>
        <div className="lg:w-2/3 p-8">
          <h2 className="text-4xl font-bold mb-6">
            <span className="text-gray-700">TRMS AC DC </span>
            <span className="text-yellow-500">DIGITAL MULTIMETERS</span>
          </h2>
          <h3 className="text-2xl font-medium mb-6">Model: {product.name}</h3>
          <div className="mb-8">
            <h4 className="text-xl font-medium mb-4">Features</h4>
            <ul className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {product.features.map((feature, index) => (
                <FeatureItem key={index}>{feature}</FeatureItem>
              ))}
            </ul>
          </div>
          <div className="flex gap-4">
            <button className="px-8 py-2 bg-white text-yellow-500 border border-yellow-500 rounded-md hover:bg-yellow-50 transition">
              ENQUIRE
            </button>
            <button className="px-8 py-2 bg-white text-yellow-500 border border-yellow-500 rounded-md hover:bg-yellow-50 transition">
              BROCHURE
            </button>
          </div>
        </div>
      </div>
    </div>
    {/* Specifications Table */}
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="p-8">
        <h3 className="text-2xl font-medium mb-6">Technical Specifications</h3>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead className="bg-yellow-500 text-white">
              <tr>
                <th className="p-3 text-left">PARAMETER</th>
                <th className="p-3 text-left">RANGE</th>
                <th className="p-3 text-left">ACCURACY %</th>
              </tr>
            </thead>
            <tbody>
              {product.specs.map((spec, index) => (
                <tr key={index} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                  <td className="p-3 border">{spec.parameter}</td>
                  <td className="p-3 border">{spec.range}</td>
                  <td className="p-3 border">{spec.accuracy}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
    {/* Key Applications */}
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="p-8">
        <h3 className="text-2xl font-medium mb-4">Key Applications</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <ApplicationCard 
            title="Electrical Maintenance" 
            description="Ideal for general electrical troubleshooting and maintenance in residential and commercial settings."
          />
          <ApplicationCard 
            title="Electronics Testing" 
            description="Perfect for testing electronic components and circuits with precision."
          />
          <ApplicationCard 
            title="HVAC Systems" 
            description="Suitable for diagnosing and maintaining heating, ventilation, and air conditioning systems."
          />
          {product.features.some(f => f.includes("Temperature")) && (
            <ApplicationCard 
              title="Temperature Monitoring" 
              description="Built-in temperature measurement for monitoring thermal conditions."
            />
          )}
          {product.features.some(f => f.includes("data logging")) && (
            <ApplicationCard 
              title="Data Logging" 
              description="Record measurements over time for detailed analysis."
            />
          )}
          <ApplicationCard 
            title="Safety Verification" 
            description={`CAT ${product.features.find(f => f.includes('CAT'))?.split('CAT ')[1] || 'IV 600V'} rated for demanding environments.`}
          />
        </div>
      </div>
    </div>
  </div>
);

const ApplicationCard: React.FC<{ title: string; description: string }> = ({ title, description }) => (
  <div className="bg-gray-50 rounded-lg p-4 border border-gray-200 hover:border-yellow-300 transition-colors">
    <h4 className="font-medium text-lg mb-2">{title}</h4>
    <p className="text-gray-600 text-sm">{description}</p>
  </div>
);

const AtestDigitalMultimeters: React.FC = () => {
  const [activeTab, setActiveTab] = useState<number | 0>(0);
  const activeProduct = AtestProducts.find(p => p.id === activeTab);

  return (
    <div className="bg-gray-50">
      <Header title="ATEST Digital Multimeters" />
      {/* Navigation Tabs */}
      <nav className="bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex overflow-x-auto whitespace-nowrap py-2 hide-scrollbar">
            <button 
              className={`px-4 py-2 mx-1 ${activeTab === 0 ? 'bg-yellow-100 text-yellow-800 font-medium rounded-md' : 'hover:bg-gray-100 rounded-md'}`}
              onClick={() => setActiveTab(0)}
            >
              All Products
            </button>
            {AtestProducts.map(product => (
              <button 
                key={product.id}
                className={`px-4 py-2 mx-1 ${activeTab === product.id ? 'bg-yellow-100 text-yellow-800 font-medium rounded-md' : 'hover:bg-gray-100 rounded-md'}`}
                onClick={() => setActiveTab(product.id)}
              >
                {product.name}
              </button>
            ))}
          </div>
        </div>
      </nav>
      {/* Main Content */}
      <main className="py-8">
        <div className="container mx-auto px-4">
          {activeTab === 0 ? (
            <div className="space-y-16">
              <div className="mb-8">
                <h2 className="text-2xl font-bold mb-6 pb-2 border-b">Gray Series - General Purpose Multimeters</h2>
                <div className="grid md:grid-cols-2 gap-8">
                  {AtestProducts.filter(p => p.color === "gray").map(product => (
                    <ProductCard key={product.id} product={product} onClick={() => setActiveTab(product.id)} />
                  ))}
                </div>
              </div>
              <div>
                <h2 className="text-2xl font-bold mb-6 pb-2 border-b">Red Series - Professional Multimeters</h2>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {AtestProducts.filter(p => p.color === "red").map(product => (
                    <ProductCard key={product.id} product={product} onClick={() => setActiveTab(product.id)} />
                  ))}
                </div>
              </div>
            </div>
          ) : activeProduct ? (
            <ProductDetail product={activeProduct} />
          ) : null}
        </div>
      </main>
      <Footer />
    </div>
  );
};

// Section 3: Detailed Product Page (from multimeter4)
const DetailedMultimeterProduct: React.FC = () => {
  const features = [
    "Models - A101Y BL-2502",
    "1999/6000 AC/DC V",
    "True RMS reading on AC mode",
    "6000-count large-scale digital display",
    "Autoranging and manual selection",
    "Smart safety hold, NCV",
    "Non-Contact Voltage detection",
    "Data Hold/Relative function key",
    "Data Guard",
    "Shock proof from 6-feet drops",
    "Certificate holster with probe holder and UI case",
    "Safety standard CAT II 1000V"
  ];

  const specifications = [
    { parameter: "DC Voltage", range: "600mV - 1000V", accuracy: "0.5%" },
    { parameter: "AC Voltage", range: "600mV - 1000V", accuracy: "1%" },
    { parameter: "DC Current", range: "6A/10A", accuracy: "1%" },
    { parameter: "AC Current", range: "6A/10A", accuracy: "1.5%" },
    { parameter: "Resistance", range: "600Ω-60MΩ", accuracy: "0.8%" },
    { parameter: "Capacitance", range: "10nF - 60mF", accuracy: "1.5%" },
    { parameter: "Frequency Counter", range: "60Hz - 10MHz", accuracy: "0.1%" },
    { parameter: "Temperature", range: "-40°C - 400°C", accuracy: "1%" },
    { parameter: "Diode Test", range: "3V", accuracy: "-" },
    { parameter: "Continuity Beeper", range: "<35Ω With tone Buzzer", accuracy: "-" }
  ];

  return (
    <div className="max-w-6xl mx-auto p-6 bg-white my-16">
      {/* Header for detailed section */}
      <div className="flex flex-col md:flex-row items-center mb-8">
        <div className="w-full md:w-1/3 flex justify-center mb-6 md:mb-0">
          <img 
            src="/api/placeholder/400/400" 
            alt="TRMS AC DC Digital Multimeter" 
            className="h-64 object-contain"
          />
        </div>
        <div className="w-full md:w-2/3 px-4">
          <h1 className="text-3xl font-bold text-gray-800">
            <span className="text-gray-600">TRMS AC DC</span> <span className="text-yellow-500">DIGITAL MULTIMETERS</span>
          </h1>
          <div className="mt-6">
            <ul className="space-y-2">
              {features.map((feature, index) => (
                <li key={index} className="flex items-start">
                  <span className="inline-block w-5 h-5 mr-2 bg-yellow-500 rounded-full flex-shrink-0 mt-1"></span>
                  <span>{feature}</span>
                </li>
              ))}
            </ul>
          </div>
          <div className="mt-8 flex space-x-4">
            <button className="px-6 py-2 border border-orange-500 text-orange-500 rounded hover:bg-orange-500 hover:text-white transition-colors">
              PURCHASE
            </button>
            <button className="px-6 py-2 border border-orange-500 text-orange-500 rounded hover:bg-orange-500 hover:text-white transition-colors">
              BROCHURE
            </button>
          </div>
        </div>
      </div>
      {/* Specifications Table */}
      <div className="mt-12">
        <h2 className="text-2xl font-bold mb-6">Technical Specifications</h2>
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border border-gray-200">
            <thead>
              <tr>
                <th className="py-3 px-4 bg-yellow-500 text-white text-left">PARAMETER</th>
                <th className="py-3 px-4 bg-yellow-500 text-white text-left">RANGE</th>
                <th className="py-3 px-4 bg-yellow-500 text-white text-left">ACCURACY %</th>
              </tr>
            </thead>
            <tbody>
              {specifications.map((spec, index) => (
                <tr key={index} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                  <td className="py-3 px-4 border-b border-gray-200">{spec.parameter}</td>
                  <td className="py-3 px-4 border-b border-gray-200">{spec.range}</td>
                  <td className="py-3 px-4 border-b border-gray-200">{spec.accuracy}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      {/* Additional Information */}
      <div className="mt-12 grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <h2 className="text-2xl font-bold mb-4">Product Description</h2>
          <p className="text-gray-700">
            The TRMS AC DC Digital Multimeter is a professional-grade measurement tool designed for electrical testing. With true RMS reading capability and a large 6000-count display, it offers both autoranging and manual selection for versatile operation.
          </p>
        </div>
        <div>
          <h2 className="text-2xl font-bold mb-4">Applications</h2>
          <ul className="list-disc pl-5 space-y-2 text-gray-700">
            <li>Electrical installation testing</li>
            <li>Equipment maintenance</li>
            <li>Automotive diagnostics</li>
            <li>HVAC system troubleshooting</li>
            <li>Electronics repair and testing</li>
          </ul>
        </div>
      </div>
      {/* Contact Section */}
      <div className="mt-12 bg-gray-100 p-6 rounded-lg">
        <h2 className="text-2xl font-bold mb-4">Need More Information?</h2>
        <p className="mb-4">Contact our technical support team for additional details or to request a personalized demonstration.</p>
        <button className="px-6 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition-colors">
          Contact Support
        </button>
      </div>
    </div>
  );
};

// Main Merged Product Page
const MergedProductPage: React.FC = () => {
  return (
    <div className="flex flex-col min-h-screen bg-white">
      <Header title="CHAUVIN ARNOUX" />
      <main className="flex-grow">
        {/* Section 1: Chauvin Arnoux Products */}
        <ChauvinArnouxProducts />

        {/* Section 2: ATEST Digital Multimeters */}
        <AtestDigitalMultimeters />

        {/* Section 3: Detailed Multimeter Product */}
        <DetailedMultimeterProduct />
      </main>
      <Footer />
    </div>
  );
};

export default MergedProductPage;
