"use client";

import React, { useRef, useState } from "react";
import { motion, useInView } from "framer-motion";
import PageLayout from "@/components/layout/PageLayout";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import {
  Search,
  Calendar,
  Clock,
  ChevronDown,
  MapPin,
  Phone,
  Mail,
  Building,
  User,
  MessageSquare,
  FileText,
  Wrench,
  Send,
  Navigation,
  AtSign
} from "lucide-react";

const Service = () => {
  const [activeTab, setActiveTab] = useState('register');
  const formRef = useRef(null);
  const formInView = useInView(formRef, { amount: 0.1, once: true });

  // State for form inputs to handle floating labels
  const [formInputs, setFormInputs] = useState({
    serialNumber: '',
    productCategory: '',
    rating: '',
    model: '',
    brand: '',
    company: '',
    companyAddress: '',
    person: '',
    personContact: '',
    mobileNo: '',
    email: '',
    serviceCenter: '',
    callType: '',
    callCategory: '',
    currentCondition: '',
    preferredTime: '',
    problemDetails: ''
  });

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { id, value } = e.target;
    setFormInputs(prev => ({
      ...prev,
      [id]: value
    }));
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 15
      }
    }
  };

  return (
    <PageLayout
      title="TECHNICAL SERVICE"
      subtitle="24/7 assistance for all our products and services"
      category="contact"
    >
      <div className="max-w-7xl mx-auto px-4 py-12 relative overflow-hidden">
        {/* Simplified background decorative elements */}
        <div className="absolute top-0 right-0 w-64 h-64 bg-blue-500/5 rounded-full filter blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-green-500/5 rounded-full filter blur-3xl"></div>

        <div
          ref={formRef}
          className="relative"
        >
          {/* Animated background elements - subtle and non-intrusive */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            {[...Array(4)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute rounded-full opacity-5"
                style={{
                  width: 100 + Math.random() * 200,
                  height: 100 + Math.random() * 200,
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  background: i % 3 === 0
                    ? "radial-gradient(circle, rgba(59, 130, 246, 0.6) 0%, rgba(29, 78, 216, 0) 70%)"
                    : i % 3 === 1
                      ? "radial-gradient(circle, rgba(16, 185, 129, 0.6) 0%, rgba(4, 120, 87, 0) 70%)"
                      : "radial-gradient(circle, rgba(245, 158, 11, 0.6) 0%, rgba(180, 83, 9, 0) 70%)",
                }}
                animate={{
                  scale: [1, 1.1, 1],
                  opacity: [0.05, 0.08, 0.05],
                }}
                transition={{
                  duration: 12 + Math.random() * 10,
                  repeat: Infinity,
                  repeatType: "reverse",
                  ease: "easeInOut",
                }}
              />
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={formInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-10"
          >
            <span className="inline-block bg-gradient-to-r from-blue-700 via-green-700 to-yellow-700 text-white px-4 py-1 rounded-full text-sm font-medium mb-3 border border-white/20 shadow-md hover:shadow-lg hover:scale-105 transition-all duration-300 cursor-default">
              Service Registration
            </span>
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-blue-900 drop-shadow-md">
              Let's Address Your <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-green-600 to-yellow-600 animate-gradient">Service Needs</span>
            </h2>
            <p className="text-lg text-blue-900 max-w-2xl mx-auto font-medium drop-shadow">
              Please register your complaint in the form below, with as many details as possible. Our service team will contact you promptly.
            </p>
          </motion.div>

          <div className="flex border-b border-gray-200 mb-8 justify-center">
            <button
              className={`pb-2 px-6 ${activeTab === 'register' ? 'text-blue-600 border-b-2 border-blue-600 font-medium' : 'text-gray-600'} transition-all duration-300 hover:text-blue-500`}
              onClick={() => setActiveTab('register')}
            >
              Log Service Call
            </button>
            <button
              className={`pb-2 px-6 ${activeTab === 'track' ? 'text-blue-600 border-b-2 border-blue-600 font-medium' : 'text-gray-600'} transition-all duration-300 hover:text-blue-500`}
              onClick={() => setActiveTab('track')}
            >
              Track Service Call
            </button>
          </div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate={formInView ? "visible" : "hidden"}
            className="grid grid-cols-1 lg:grid-cols-12 gap-8"
          >
            {/* Form Section - 8 columns on large screens */}
            <motion.div
              variants={itemVariants}
              className="lg:col-span-8 space-y-6 transition-all duration-700 hover:scale-[1.01]"
            >
              {/* Product Details */}
              <div className="p-8 bg-gradient-to-br from-white/70 to-white/95 backdrop-blur-md rounded-xl shadow-xl border border-blue-100/50 hover:border-blue-200 transition-all duration-300 hover:shadow-blue-100/30 relative overflow-hidden">
                {/* Decorative corner accent */}
                <div className="absolute -top-10 -right-10 w-20 h-20 bg-blue-500/10 rounded-full blur-xl"></div>
                <div className="absolute -bottom-10 -left-10 w-20 h-20 bg-green-500/10 rounded-full blur-xl"></div>

                {/* Header with shine effect */}
                <div className="relative mb-6 overflow-hidden rounded-lg bg-gradient-to-r from-blue-50/50 to-transparent">
                  <h3 className="text-xl font-bold text-blue-800 py-3 px-4 drop-shadow-sm flex items-center">
                    <FileText className="h-5 w-5 mr-2 text-blue-600" />
                    Product Details
                  </h3>
                  {/* Enhanced shine effect */}
                  <div className="absolute top-0 left-0 w-1/2 h-full bg-gradient-to-r from-transparent via-white/40 to-transparent transform -skew-x-20 animate-shine"></div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label htmlFor="serialNumber" className="block text-sm font-medium text-blue-800">
                      Serial Number
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <FileText className="h-5 w-5 text-blue-600 group-hover:text-blue-500 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <input
                            type="text"
                            id="serialNumber"
                            value={formInputs.serialNumber}
                            onChange={handleInputChange}
                            className="w-full py-3 px-3 bg-white/90 border border-blue-300/70 rounded-md text-blue-800 placeholder-transparent focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 hover:border-blue-400 peer"
                            placeholder="Enter serial number"
                          />
                          <label
                            htmlFor="serialNumber"
                            className={`absolute text-sm text-blue-500 duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.serialNumber ? 'top-2 scale-75 -translate-y-4' : ''}`}
                          >
                            Enter serial number
                          </label>
                        </div>
                        <button className="absolute right-3 top-1/2 -translate-y-1/2 text-blue-600 hover:text-blue-800 transition-colors">
                          <Search size={18} />
                        </button>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="productCategory" className="block text-sm font-medium text-blue-800">
                      Select Product Category
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <Wrench className="h-5 w-5 text-green-600 group-hover:text-green-500 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <select
                            id="productCategory"
                            value={formInputs.productCategory}
                            onChange={handleInputChange}
                            className="w-full py-3 pl-3 pr-10 bg-white/90 border border-green-300/70 rounded-md text-blue-800 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 appearance-none transition-all duration-300 hover:border-green-400"
                          >
                            <option value="">Select a category</option>
                            <option value="energy-meters">Energy Meters</option>
                            <option value="protection-relays">Protection Relays</option>
                            <option value="monitoring-systems">Monitoring Systems</option>
                          </select>
                          <ChevronDown size={16} className="absolute right-3 top-1/2 -translate-y-1/2 text-green-600 pointer-events-none" />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="rating" className="block text-sm font-medium text-blue-800">
                      Rating
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <FileText className="h-5 w-5 text-blue-600 group-hover:text-blue-500 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <input
                            type="text"
                            id="rating"
                            value={formInputs.rating}
                            onChange={handleInputChange}
                            className="w-full py-3 px-3 bg-white/90 border border-blue-300/70 rounded-md text-blue-800 placeholder-transparent focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 hover:border-blue-400 peer"
                            placeholder="Enter rating"
                          />
                          <label
                            htmlFor="rating"
                            className={`absolute text-sm text-blue-500 duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.rating ? 'top-2 scale-75 -translate-y-4' : ''}`}
                          >
                            Enter rating
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="model" className="block text-sm font-medium text-blue-800">
                      Model
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <Wrench className="h-5 w-5 text-green-600 group-hover:text-green-500 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <input
                            type="text"
                            id="model"
                            value={formInputs.model}
                            onChange={handleInputChange}
                            className="w-full py-3 px-3 bg-white/90 border border-green-300/70 rounded-md text-blue-800 placeholder-transparent focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300 hover:border-green-400 peer"
                            placeholder="Enter model"
                          />
                          <label
                            htmlFor="model"
                            className={`absolute text-sm text-green-500 duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.model ? 'top-2 scale-75 -translate-y-4' : ''}`}
                          >
                            Enter model
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="md:col-span-2 space-y-2">
                    <label htmlFor="brand" className="block text-sm font-medium text-blue-800">
                      Select Brand
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <Building className="h-5 w-5 text-blue-600 group-hover:text-blue-500 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <select
                            id="brand"
                            value={formInputs.brand}
                            onChange={handleInputChange}
                            className="w-full py-3 pl-3 pr-10 bg-white/90 border border-blue-300/70 rounded-md text-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none transition-all duration-300 hover:border-blue-400"
                          >
                            <option value="">Select a brand</option>
                            <option value="krykard">Krykard</option>
                            <option value="other">Other</option>
                          </select>
                          <ChevronDown size={16} className="absolute right-3 top-1/2 -translate-y-1/2 text-blue-600 pointer-events-none" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Customer Details */}
              <div className="p-8 bg-gradient-to-br from-white/70 to-white/95 backdrop-blur-md rounded-xl shadow-xl border border-blue-100/50 hover:border-blue-200 transition-all duration-300 hover:shadow-blue-100/30 relative overflow-hidden">
                {/* Decorative corner accent */}
                <div className="absolute -top-10 -right-10 w-20 h-20 bg-blue-500/10 rounded-full blur-xl"></div>
                <div className="absolute -bottom-10 -left-10 w-20 h-20 bg-green-500/10 rounded-full blur-xl"></div>

                {/* Header with shine effect */}
                <div className="relative mb-6 overflow-hidden rounded-lg bg-gradient-to-r from-blue-50/50 to-transparent">
                  <h3 className="text-xl font-bold text-blue-800 py-3 px-4 drop-shadow-sm flex items-center">
                    <User className="h-5 w-5 mr-2 text-blue-600" />
                    Customer Details
                  </h3>
                  {/* Enhanced shine effect */}
                  <div className="absolute top-0 left-0 w-1/2 h-full bg-gradient-to-r from-transparent via-white/40 to-transparent transform -skew-x-20 animate-shine"></div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label htmlFor="company" className="block text-sm font-medium text-blue-800">
                      Company
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <Building className="h-5 w-5 text-blue-600 group-hover:text-blue-500 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <input
                            type="text"
                            id="company"
                            value={formInputs.company}
                            onChange={handleInputChange}
                            className="w-full py-3 px-3 bg-white/90 border border-blue-300/70 rounded-md text-blue-800 placeholder-transparent focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 hover:border-blue-400 peer"
                            placeholder="Enter company name"
                          />
                          <label
                            htmlFor="company"
                            className={`absolute text-sm text-blue-500 duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.company ? 'top-2 scale-75 -translate-y-4' : ''}`}
                          >
                            Enter company name
                          </label>
                        </div>
                        <button className="absolute right-3 top-1/2 -translate-y-1/2 text-blue-600 hover:text-blue-800 transition-colors">
                          <Search size={18} />
                        </button>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="companyAddress" className="block text-sm font-medium text-blue-800">
                      Company Address
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <MapPin className="h-5 w-5 text-green-600 group-hover:text-green-500 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <input
                            type="text"
                            id="companyAddress"
                            value={formInputs.companyAddress}
                            onChange={handleInputChange}
                            className="w-full py-3 px-3 bg-white/90 border border-green-300/70 rounded-md text-blue-800 placeholder-transparent focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300 hover:border-green-400 peer"
                            placeholder="Enter company address"
                          />
                          <label
                            htmlFor="companyAddress"
                            className={`absolute text-sm text-green-500 duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.companyAddress ? 'top-2 scale-75 -translate-y-4' : ''}`}
                          >
                            Enter company address
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="person" className="block text-sm font-medium text-blue-800">
                      Person
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <User className="h-5 w-5 text-yellow-600 group-hover:text-yellow-500 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <input
                            type="text"
                            id="person"
                            value={formInputs.person}
                            onChange={handleInputChange}
                            className="w-full py-3 px-3 bg-white/90 border border-yellow-300/70 rounded-md text-blue-800 placeholder-transparent focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 transition-all duration-300 hover:border-yellow-400 peer"
                            placeholder="Enter contact person"
                          />
                          <label
                            htmlFor="person"
                            className={`absolute text-sm text-yellow-600 duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.person ? 'top-2 scale-75 -translate-y-4' : ''}`}
                          >
                            Enter contact person
                          </label>
                        </div>
                        <button className="absolute right-3 top-1/2 -translate-y-1/2 text-yellow-600 hover:text-yellow-700 transition-colors">
                          <Search size={18} />
                        </button>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="personContact" className="block text-sm font-medium text-blue-800">
                      Person
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <User className="h-5 w-5 text-yellow-600 group-hover:text-yellow-500 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <input
                            type="text"
                            id="personContact"
                            value={formInputs.personContact}
                            onChange={handleInputChange}
                            className="w-full py-3 px-3 bg-white/90 border border-yellow-300/70 rounded-md text-blue-800 placeholder-transparent focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 transition-all duration-300 hover:border-yellow-400 peer"
                            placeholder="Enter alternative contact"
                          />
                          <label
                            htmlFor="personContact"
                            className={`absolute text-sm text-yellow-600 duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.personContact ? 'top-2 scale-75 -translate-y-4' : ''}`}
                          >
                            Enter alternative contact
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="mobileNo" className="block text-sm font-medium text-blue-800">
                      Mobile No
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <Phone className="h-5 w-5 text-blue-600 group-hover:text-blue-500 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <input
                            type="tel"
                            id="mobileNo"
                            value={formInputs.mobileNo}
                            onChange={handleInputChange}
                            className="w-full py-3 px-3 bg-white/90 border border-blue-300/70 rounded-md text-blue-800 placeholder-transparent focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 hover:border-blue-400 peer"
                            placeholder="Enter mobile number"
                          />
                          <label
                            htmlFor="mobileNo"
                            className={`absolute text-sm text-blue-500 duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.mobileNo ? 'top-2 scale-75 -translate-y-4' : ''}`}
                          >
                            Enter mobile number
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="email" className="block text-sm font-medium text-blue-800">
                      Email
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <Mail className="h-5 w-5 text-green-600 group-hover:text-green-500 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <input
                            type="email"
                            id="email"
                            value={formInputs.email}
                            onChange={handleInputChange}
                            className="w-full py-3 px-3 bg-white/90 border border-green-300/70 rounded-md text-blue-800 placeholder-transparent focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300 hover:border-green-400 peer"
                            placeholder="Enter email address"
                          />
                          <label
                            htmlFor="email"
                            className={`absolute text-sm text-green-500 duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.email ? 'top-2 scale-75 -translate-y-4' : ''}`}
                          >
                            Enter email address
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* General Section */}
              <div className="p-8 bg-gradient-to-br from-white/70 to-white/95 backdrop-blur-md rounded-xl shadow-xl border border-blue-100/50 hover:border-blue-200 transition-all duration-300 hover:shadow-blue-100/30 relative overflow-hidden">
                {/* Decorative corner accent */}
                <div className="absolute -top-10 -right-10 w-20 h-20 bg-blue-500/10 rounded-full blur-xl"></div>
                <div className="absolute -bottom-10 -left-10 w-20 h-20 bg-green-500/10 rounded-full blur-xl"></div>

                {/* Header with shine effect */}
                <div className="relative mb-6 overflow-hidden rounded-lg bg-gradient-to-r from-blue-50/50 to-transparent">
                  <h3 className="text-xl font-bold text-blue-800 py-3 px-4 drop-shadow-sm flex items-center">
                    <Wrench className="h-5 w-5 mr-2 text-blue-600" />
                    General
                  </h3>
                  {/* Enhanced shine effect */}
                  <div className="absolute top-0 left-0 w-1/2 h-full bg-gradient-to-r from-transparent via-white/40 to-transparent transform -skew-x-20 animate-shine"></div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label htmlFor="serviceCenter" className="block text-sm font-medium text-blue-800">
                      Service Center
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <Building className="h-5 w-5 text-blue-600 group-hover:text-blue-500 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <select
                            id="serviceCenter"
                            value={formInputs.serviceCenter}
                            onChange={handleInputChange}
                            className="w-full py-3 pl-3 pr-10 bg-white/90 border border-blue-300/70 rounded-md text-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none transition-all duration-300 hover:border-blue-400"
                          >
                            <option value="">Select service center</option>
                            <option value="chennai">Chennai</option>
                            <option value="mumbai">Mumbai</option>
                            <option value="delhi">Delhi</option>
                            <option value="bangalore">Bangalore</option>
                          </select>
                          <ChevronDown size={16} className="absolute right-3 top-1/2 -translate-y-1/2 text-blue-600 pointer-events-none" />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="callType" className="block text-sm font-medium text-blue-800">
                      Call Type
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <Phone className="h-5 w-5 text-green-600 group-hover:text-green-500 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <select
                            id="callType"
                            value={formInputs.callType}
                            onChange={handleInputChange}
                            className="w-full py-3 pl-3 pr-10 bg-white/90 border border-green-300/70 rounded-md text-blue-800 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 appearance-none transition-all duration-300 hover:border-green-400"
                          >
                            <option value="">Select call type</option>
                            <option value="installation">Installation</option>
                            <option value="repair">Repair</option>
                            <option value="maintenance">Maintenance</option>
                            <option value="inquiry">Inquiry</option>
                          </select>
                          <ChevronDown size={16} className="absolute right-3 top-1/2 -translate-y-1/2 text-green-600 pointer-events-none" />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="callCategory" className="block text-sm font-medium text-blue-800">
                      Call Category
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <MessageSquare className="h-5 w-5 text-yellow-600 group-hover:text-yellow-500 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <select
                            id="callCategory"
                            value={formInputs.callCategory}
                            onChange={handleInputChange}
                            className="w-full py-3 pl-3 pr-10 bg-white/90 border border-yellow-300/70 rounded-md text-blue-800 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 appearance-none transition-all duration-300 hover:border-yellow-400"
                          >
                            <option value="">Select category</option>
                            <option value="urgent">Urgent</option>
                            <option value="normal">Normal</option>
                            <option value="scheduled">Scheduled</option>
                          </select>
                          <ChevronDown size={16} className="absolute right-3 top-1/2 -translate-y-1/2 text-yellow-600 pointer-events-none" />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="currentCondition" className="block text-sm font-medium text-blue-800">
                      Select Current Condition
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <Wrench className="h-5 w-5 text-yellow-600 group-hover:text-yellow-500 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <select
                            id="currentCondition"
                            value={formInputs.currentCondition}
                            onChange={handleInputChange}
                            className="w-full py-3 pl-3 pr-10 bg-white/90 border border-yellow-300/70 rounded-md text-blue-800 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 appearance-none transition-all duration-300 hover:border-yellow-400"
                          >
                            <option value="">Select condition</option>
                            <option value="working">Working with issues</option>
                            <option value="not-working">Not working</option>
                            <option value="intermittent">Intermittent problems</option>
                            <option value="new-install">New installation</option>
                          </select>
                          <ChevronDown size={16} className="absolute right-3 top-1/2 -translate-y-1/2 text-yellow-600 pointer-events-none" />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="preferredTime" className="block text-sm font-medium text-blue-800">
                      Preferred Time
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <Calendar className="h-5 w-5 text-blue-600 group-hover:text-blue-500 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <input
                            type="text"
                            id="preferredTime"
                            value={formInputs.preferredTime}
                            onChange={handleInputChange}
                            className="w-full py-3 px-3 bg-white/90 border border-blue-300/70 rounded-md text-blue-800 placeholder-transparent focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 hover:border-blue-400 peer"
                            placeholder="Select preferred time"
                          />
                          <label
                            htmlFor="preferredTime"
                            className={`absolute text-sm text-blue-500 duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.preferredTime ? 'top-2 scale-75 -translate-y-4' : ''}`}
                          >
                            Select preferred time
                          </label>
                        </div>
                        <div className="absolute right-12 top-1/2 -translate-y-1/2 text-blue-600 hover:text-blue-800 transition-colors">
                          <Calendar size={18} />
                        </div>
                        <div className="absolute right-3 top-1/2 -translate-y-1/2 text-blue-600 hover:text-blue-800 transition-colors">
                          <Clock size={18} />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="md:col-span-2 space-y-2">
                    <label htmlFor="problemDetails" className="block text-sm font-medium text-blue-800">
                      Problem Details
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 h-12 group-hover:scale-110 transition-transform duration-300">
                          <MessageSquare className="h-5 w-5 text-green-600 group-hover:text-green-500 transition-colors duration-300" />
                        </div>
                        <textarea
                          id="problemDetails"
                          rows={4}
                          value={formInputs.problemDetails}
                          onChange={handleInputChange}
                          className="w-full py-3 px-3 bg-white/90 border border-green-300/70 rounded-md text-blue-800 placeholder-blue-400/80 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300 hover:border-green-400 resize-none"
                          placeholder="Please describe the problem in detail"
                        ></textarea>
                      </div>
                    </div>
                  </div>
                </div>

                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="relative inline-block group w-full mt-8"
                >
                  {/* Button with enhanced gradient glow effect */}
                  <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-600 via-green-500 to-yellow-500 rounded-lg blur opacity-30 group-hover:opacity-70 transition duration-700 group-hover:duration-200"></div>

                  <Button
                    type="submit"
                    className="relative w-full inline-flex items-center justify-center px-8 py-5 text-lg font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 rounded-lg transition-all duration-300 shadow-lg hover:shadow-blue-500/30 overflow-hidden"
                  >
                    <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-blue-400/20 to-transparent transform -skew-x-30 transition-all duration-300 opacity-0 group-hover:opacity-100"></span>
                    <Send className="h-5 w-5 mr-2 group-hover:translate-x-1 transition-transform duration-300" />
                    <span className="relative z-10 text-base font-bold">Register Complaint/Query</span>
                  </Button>
                </motion.div>
              </div>
            </motion.div>

            {/* Map Section - 4 columns on large screens */}
            <motion.div
              variants={itemVariants}
              className="lg:col-span-4 space-y-6"
            >
              {/* Service Locations Map */}
              <div className="transition-all duration-700 hover:scale-[1.01] p-6 bg-gradient-to-br from-white/70 to-white/95 backdrop-blur-md rounded-xl shadow-xl border border-blue-100/50 hover:border-blue-200 hover:shadow-blue-100/30 relative overflow-hidden">
                {/* Decorative corner accent */}
                <div className="absolute -top-10 -right-10 w-20 h-20 bg-blue-500/10 rounded-full blur-xl"></div>
                <div className="absolute -bottom-10 -left-10 w-20 h-20 bg-green-500/10 rounded-full blur-xl"></div>

                {/* Header with shine effect */}
                <div className="relative mb-6 overflow-hidden rounded-lg bg-gradient-to-r from-blue-50/50 to-transparent">
                  <h3 className="text-xl font-bold text-blue-800 py-3 px-4 drop-shadow-sm flex items-center">
                    <MapPin className="h-5 w-5 mr-2 text-blue-600" />
                    Service Locations
                  </h3>
                  {/* Enhanced shine effect */}
                  <div className="absolute top-0 left-0 w-1/2 h-full bg-gradient-to-r from-transparent via-white/40 to-transparent transform -skew-x-20 animate-shine"></div>
                </div>

                <div className="relative overflow-hidden rounded-lg shadow-md group">
                  <img
                    src="/background_images/Service-Locations-India.jpeg"
                    alt="Krykard service locations map of India"
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-blue-900/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end p-4">
                    <div className="text-white">
                      <p className="font-semibold">Our nationwide presence</p>
                      <p className="text-sm text-white/90">Red dots indicate service centers</p>
                    </div>
                  </div>
                </div>

                <div className="mt-6 space-y-4">
                  <div className="flex items-center space-x-2 bg-blue-50/80 rounded-md p-3 transition-all duration-300 hover:bg-blue-50 hover:shadow-sm">
                    <Mail className="h-5 w-5 text-blue-600 flex-shrink-0" />
                    <div>
                      <p className="text-sm font-medium text-blue-800">Email Support</p>
                      <a href="mailto:<EMAIL>" className="text-sm text-blue-600 hover:text-blue-800 transition-colors">
                        <EMAIL>
                      </a>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 bg-green-50/80 rounded-md p-3 transition-all duration-300 hover:bg-green-50 hover:shadow-sm">
                    <Phone className="h-5 w-5 text-green-600 flex-shrink-0" />
                    <div>
                      <p className="text-sm font-medium text-green-800">24/7 Helpline</p>
                      <a href="tel:+911234567890" className="text-sm text-green-600 hover:text-green-800 transition-colors">
                        +91 95000 97966
                      </a>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 bg-yellow-50/80 rounded-md p-3 transition-all duration-300 hover:bg-yellow-50 hover:shadow-sm">
                    <MessageSquare className="h-5 w-5 text-yellow-600 flex-shrink-0" />
                    <div>
                      <p className="text-sm font-medium text-yellow-800">WhatsApp Support</p>
                      <a href="https://wa.me/911234567890" className="text-sm text-yellow-600 hover:text-yellow-800 transition-colors">
                        +91 1234 567 890
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick FAQ Section */}
              <div className="transition-all duration-700 hover:scale-[1.01] p-6 bg-gradient-to-br from-white/70 to-white/95 backdrop-blur-md rounded-xl shadow-xl border border-blue-100/50 hover:border-blue-200 hover:shadow-blue-100/30 relative overflow-hidden">
                {/* Decorative corner accent */}
                <div className="absolute -top-10 -right-10 w-20 h-20 bg-blue-500/10 rounded-full blur-xl"></div>
                <div className="absolute -bottom-10 -left-10 w-20 h-20 bg-green-500/10 rounded-full blur-xl"></div>

                {/* Header with shine effect */}
                <div className="relative mb-6 overflow-hidden rounded-lg bg-gradient-to-r from-blue-50/50 to-transparent">
                  <h3 className="text-xl font-bold text-blue-800 py-3 px-4 drop-shadow-sm flex items-center">
                    <MessageSquare className="h-5 w-5 mr-2 text-blue-600" />
                    Quick FAQ
                  </h3>
                  {/* Enhanced shine effect */}
                  <div className="absolute top-0 left-0 w-1/2 h-full bg-gradient-to-r from-transparent via-white/40 to-transparent transform -skew-x-20 animate-shine"></div>
                </div>

                <div className="space-y-4">
                  <div className="bg-blue-50/50 p-4 rounded-lg transition-all duration-300 hover:bg-blue-50 hover:shadow-sm">
                    <h4 className="font-medium text-blue-800 mb-2">What happens after I submit?</h4>
                    <p className="text-sm text-blue-700">Our team will contact you within 24 hours to schedule a visit or address your query remotely.</p>
                  </div>

                  <div className="bg-green-50/50 p-4 rounded-lg transition-all duration-300 hover:bg-green-50 hover:shadow-sm">
                    <h4 className="font-medium text-green-800 mb-2">How can I track my complaint?</h4>
                    <p className="text-sm text-green-700">Use the "Track Service Call" tab with your complaint ID to check the status of your service request.</p>
                  </div>

                  <div className="bg-yellow-50/50 p-4 rounded-lg transition-all duration-300 hover:bg-yellow-50 hover:shadow-sm">
                    <h4 className="font-medium text-yellow-800 mb-2">Is there an emergency support?</h4>
                    <p className="text-sm text-yellow-700">Yes, mark your call as "Urgent" for priority service or call our 24/7 helpline for immediate assistance.</p>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>

        {/* Add the animations */}
        <style>{`
          @keyframes shine {
            0% {
              left: -100%;
            }
            100% {
              left: 100%;
            }
          }

          @keyframes pulse {
            0%, 100% {
              opacity: 0.2;
            }
            50% {
              opacity: 0.5;
            }
          }

          @keyframes ping {
            0% {
              transform: scale(1);
              opacity: 0.8;
            }
            75%, 100% {
              transform: scale(1.5);
              opacity: 0;
            }
          }

          .animate-shine {
            animation: shine 3s infinite;
          }

          .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
          }

          .animate-ping {
            animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
          }

          /* Gradient shift animation */
          @keyframes gradient-shift {
            0% {
              background-position: 0% 50%;
            }
            50% {
              background-position: 100% 50%;
            }
            100% {
              background-position: 0% 50%;
            }
          }

          .animate-gradient {
            background-size: 200% 200%;
            animation: gradient-shift 5s ease infinite;
          }
        `}</style>
      </div>

      {/* Back to Contact button */}
      <div className="max-w-7xl mx-auto px-4 pb-8 text-center">
        <Link
          to="/contact"
          className="inline-flex items-center gap-2 text-green-600 hover:text-green-800 transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Back to Contact Page
        </Link>
      </div>
    </PageLayout>
  );
};

export default Service;