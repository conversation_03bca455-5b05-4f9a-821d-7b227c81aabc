import React, { useEffect, useMemo, useCallback, useState } from "react";
import { motion, useAnimation, useInView } from "framer-motion";

// Enhanced type definitions
interface ClientLogosSectionProps {
  isInView?: boolean;
  className?: string;
  pauseOnHover?: boolean;
}

interface ClientLogo {
  readonly name: string;
  readonly logo: string;
  readonly alt?: string;
}

interface CarouselRowProps {
  logos: readonly ClientLogo[];
  direction: 'left' | 'right';
  rowIndex: number;
  pauseOnHover: boolean;
}



// Custom hook for intersection observer
const useIntersectionObserver = (threshold = 0.1) => {
  const [isVisible, setIsVisible] = useState(false);
  const ref = React.useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      { threshold }
    );

    const currentRef = ref.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [threshold]);

  return { ref, isVisible };
};

// Custom hook for animation controls
const useAnimationControls = (isInView: boolean) => {
  const controls = useAnimation();

  useEffect(() => {
    if (isInView) {
      controls.start("visible");
    } else {
      controls.start("hidden");
    }
  }, [isInView, controls]);

  return controls;
};

// Memoized logo item component for better performance
const LogoItem = React.memo<{
  client: ClientLogo;
  index: number;
  rowIndex: number;
  isDuplicate?: boolean;
}>(({ client, index, rowIndex, isDuplicate = false }) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const handleImageLoad = useCallback(() => {
    setImageLoaded(true);
  }, []);

  const handleImageError = useCallback(() => {
    setImageError(true);
  }, []);

  const logoContainerClass = useMemo(() =>
    "logo-container-uniform flex items-center justify-center flex-shrink-0",
    []
  );

  const logoImageClass = useMemo(() =>
    "img-responsive-contain filter brightness-100 transition-all duration-200 opacity-90 hover:opacity-100 hover:scale-105 drop-shadow-lg",
    []
  );

  return (
    <div
      key={`logo-row${rowIndex}-${isDuplicate ? 'dup-' : ''}${index}`}
      className="logo-item relative group"
      role="img"
      aria-label={`${client.name} logo`}
    >
      <div className={logoContainerClass}>
        {!imageError ? (
          <img
            src={client.logo === "/api/placeholder/150/60" ? "/api/placeholder/120/50" : client.logo}
            alt={client.alt || client.name}
            className={`${logoImageClass} ${!imageLoaded ? 'opacity-0' : 'opacity-100'}`}
            loading="lazy"
            onLoad={handleImageLoad}
            onError={handleImageError}
            decoding="async"
          />
        ) : (
          <div className="flex items-center justify-center text-gray-600 text-responsive-xs font-medium break-words text-center">
            {client.name}
          </div>
        )}
      </div>
      <div className="absolute -bottom-6 sm:-bottom-7 left-0 right-0 text-center opacity-0 group-hover:opacity-80 text-responsive-xs text-gray-800 font-medium transition-opacity duration-200 font-['Open_Sans'] break-words">
        {client.name}
      </div>
    </div>
  );
});

LogoItem.displayName = 'LogoItem';

// Carousel row component
const CarouselRow = React.memo<CarouselRowProps>(({
  logos,
  direction,
  rowIndex,
  pauseOnHover
}) => {
  const animationClass = useMemo(() => {
    const baseClass = direction === 'right' ? 'carousel-row-right' : 'carousel-row-left';
    return `carousel-row ${baseClass}`;
  }, [direction]);

  return (
    <div className="mb-8 sm:mb-10 md:mb-12 relative overflow-hidden">
      <div className={animationClass}>
        <div className="carousel-track flex items-center">
          {/* First set of logos */}
          {logos.map((client, index) => (
            <LogoItem
              key={`${rowIndex}-${index}`}
              client={client}
              index={index}
              rowIndex={rowIndex}
            />
          ))}
          {/* Second set for seamless loop */}
          {logos.map((client, index) => (
            <LogoItem
              key={`${rowIndex}-dup-${index}`}
              client={client}
              index={index}
              rowIndex={rowIndex}
              isDuplicate
            />
          ))}
          {/* Third set for extra smooth transition */}
          {logos.map((client, index) => (
            <LogoItem
              key={`${rowIndex}-dup2-${index}`}
              client={client}
              index={index}
              rowIndex={rowIndex}
              isDuplicate
            />
          ))}
        </div>
      </div>
    </div>
  );
});

CarouselRow.displayName = 'CarouselRow';

// Main component
const ClientLogosSection: React.FC<ClientLogosSectionProps> = ({
  isInView: externalIsInView,
  className = "",
  pauseOnHover = true
}) => {
  const { ref, isVisible } = useIntersectionObserver(0.1);
  const finalIsInView = externalIsInView ?? isVisible;
  const controls = useAnimationControls(finalIsInView);

  // Memoized client logos data for better performance
  const clientsData: readonly ClientLogo[][] = useMemo(() => [
    // Row 1 - 15 items
    [
      { name: "Adani", logo: "/logos/1024px-Adani_2012_logo.png" },
      { name: "Amul", logo: "/logos/1200px-Amul_official_logo.svg.png" },
      { name: "Escorts Group", logo: "/logos/1200px-Escorts_Group.svg.png" },
      { name: "ISRO", logo: "/logos/1200px-Indian_Space_Research_Organisation_Logo.svg.png" },
      { name: "Wipro", logo: "/logos/1200px-Wipro_Primary_Logo_Color_RGB.svg.png" },
      { name: "BHEL", logo: "/logos/2492px-BHEL_logo.svg.png" },
      { name: "Axis Bank", logo: "/logos/2560px-Axis_Bank_logo.svg.png" },
      { name: "Godrej", logo: "/logos/2560px-Godrej_Logo.svg.png" },
      { name: "Saint-Gobain", logo: "/logos/2560px-Saint-Gobain_logo.svg.png" },
      { name: "Zepto", logo: "/logos/64a285c0af324ae978642deb_Zepto.png" },
      { name: "Aavin", logo: "/logos/800px-Aavin_dairy_logo.svg.png" },
      { name: "Autoprint", logo: "/logos/autoprint-logo-high-res-520.png" },
      { name: "Bajaj", logo: "/logos/Bajaj-Logo.png" },
      { name: "Blue Star", logo: "/logos/blue-star-limited-logo-vector.png" },
      { name: "Britannia", logo: "/logos/britannia-logo-brandlogos.net_.png" },
    ],
    // Row 2 - 15 items
    [
      { name: "CavinKare", logo: "/logos/CavinKare-logo.png" },
      { name: "Fujifilm", logo: "/logos/Fujifilm-logo.png" },
      { name: "Google", logo: "/logos/google_PNG19644.png" },
      { name: "HCL", logo: "/logos/HCL_Technologies-Logo.wine.png" },
      { name: "Indian Oil", logo: "/logos/Indian_Oil_Corporation-Logo.wine.png" },
      { name: "JSW Steel", logo: "/logos/jsw-steel-vector-logo.png" },
      { name: "Jubilant FoodWorks", logo: "/logos/Jubilant_FoodWorks.png" },
      { name: "KCP Sugar", logo: "/logos/kcp-sugar-and-indust--600.png" },
      { name: "KFC", logo: "/logos/Kfc_logo.png" },
      { name: "Volvo", logo: "/logos/volvo.png" },
      { name: "K-Oil Engines", logo: "/logos/k-oil-engines-logo.png" },
      { name: "Kotak", logo: "/logos/kotak_mahindra_bank.png" },
      { name: "Lakshmi Machine Works", logo: "/logos/lakshmi-machine-works-limited-lmw-logo-vector.png" },
      { name: "L&T", logo: "/logos/Larsen__Toubro_png.jpg" },
      { name: "Lifestyle", logo: "/logos/Lifestyle_Stores_-_New.jpg" },
    ],
    // Row 3 - 15 items
    [
      { name: "TTK", logo: "/logos/logo_ttk.png" },
      { name: "TVS Motor", logo: "/logos/logo-tvs-motor-.png" },
      { name: "Michelin", logo: "/logos/Michelin_Logo_1997.png" },
      { name: "Asian Paints", logo: "/logos/asian-paints-1.png" },
      { name: "Tata Motors", logo: "/logos/tata-motors.png" },
      { name: "Newlord", logo: "/logos/Newlord_Logo.png" },
      { name: "Raymond", logo: "/logos/raymond.png" },
      { name: "RBL Bank", logo: "/logos/RBL.NS-5c0ffcf8.png" },
      { name: "Tata Coffee", logo: "/logos/tata-coffee.png" },
      { name: "Honda", logo: "/logos/honda.png" },
      { name: "V-Mart", logo: "/logos/v-mart.png" },
      { name: "ICICI", logo: "/logos/icic.png" },
      { name: "MRF", logo: "/logos/mrf.png" },
      { name: "Mahindra", logo: "/logos/mahindra.png" },
      { name: "ITC", logo: "/logos/itc.png" },
    ]
  ], []);

  // Memoized carousel configurations
  const carouselConfigs = useMemo(() => [
    { direction: 'right' as const },
    { direction: 'left' as const },
    { direction: 'right' as const }
  ], []);

  return (
    <div ref={ref} className={`py-12 sm:py-16 overflow-hidden w-full bg-gradient-to-br from-slate-50 via-gray-100 to-slate-200 ${className}`}>
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={finalIsInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
        transition={{ duration: 0.6 }}
        className="text-center container-responsive"
      >
        <div className="relative inline-block mb-6">
          <motion.h2
            className="text-responsive-2xl font-bold text-center text-blue-600 mb-4 font-['Open_Sans']"
            variants={{
              hidden: { opacity: 0, y: 20 },
              visible: { opacity: 1, y: 0 }
            }}
            initial="hidden"
            animate={controls}
            transition={{ duration: 0.5 }}
          >
            Your Success, Our Clients
          </motion.h2>
          {/* Decorative underline */}
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-blue-600 rounded-full"></div>
        </div>

        {/* Subtitle with decorative elements */}
        <div className="flex items-center justify-center gap-4 mb-8 sm:mb-12">
          <div className="h-px w-20 bg-blue-300"></div>
          <motion.p
            className="text-responsive-base text-blue-600 font-semibold tracking-wide uppercase font-['Open_Sans'] text-center"
            variants={{
              hidden: { opacity: 0 },
              visible: { opacity: 1 }
            }}
            initial="hidden"
            animate={controls}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Delivering Excellence to Leading Organizations Across Industries
          </motion.p>
          <div className="h-px w-20 bg-blue-300"></div>
        </div>
      </motion.div>

      {/* Modern performance-optimized multi-row carousels */}
      <div className="relative w-full px-0" role="region" aria-label="Client logos carousel">
        {clientsData.map((logos, index) => (
          <CarouselRow
            key={`carousel-row-${index}`}
            logos={logos}
            direction={carouselConfigs[index].direction}
            rowIndex={index}
            pauseOnHover={pauseOnHover}
          />
        ))}
      </div>

      {/* Optimized CSS for uniform sizing and seamless infinite animation */}
      <style>{`
        /* FORCE uniform logo container sizing - override all existing styles */
        .logo-container-uniform {
          /* Base mobile size: w-32 h-24 - FORCED */
          width: 8rem !important;
          height: 6rem !important;
          padding: 0.5rem !important;
          margin: 0 0.5rem !important;
          min-width: 8rem !important;
          max-width: 8rem !important;
          min-height: 6rem !important;
          max-height: 6rem !important;
          box-sizing: border-box !important;
        }

        /* Responsive adjustments - FORCED to override Tailwind */
        @media (min-width: 640px) {
          .logo-container-uniform {
            /* sm: w-40 h-28 - FORCED */
            width: 10rem !important;
            height: 7rem !important;
            padding: 0.75rem !important;
            margin: 0 0.75rem !important;
            min-width: 10rem !important;
            max-width: 10rem !important;
            min-height: 7rem !important;
            max-height: 7rem !important;
          }
        }

        @media (min-width: 768px) {
          .logo-container-uniform {
            /* md: w-48 h-32 - FORCED */
            width: 12rem !important;
            height: 8rem !important;
            padding: 0.75rem !important;
            margin: 0 1rem !important;
            min-width: 12rem !important;
            max-width: 12rem !important;
            min-height: 8rem !important;
            max-height: 8rem !important;
          }
        }

        @media (min-width: 1024px) {
          .logo-container-uniform {
            /* lg: w-52 h-36 - FORCED */
            width: 13rem !important;
            height: 9rem !important;
            padding: 1rem !important;
            margin: 0 1rem !important;
            min-width: 13rem !important;
            max-width: 13rem !important;
            min-height: 9rem !important;
            max-height: 9rem !important;
          }
        }

        .carousel-row {
          overflow: hidden;
          position: relative;
          width: 100%;
          contain: layout style paint;
        }

        .carousel-track {
          display: flex;
          align-items: center;
          will-change: transform;
          transform-style: preserve-3d;
          backface-visibility: hidden;
        }

        /* Seamless infinite animation - no restart/jump */
        .carousel-row-right .carousel-track {
          animation: seamless-scroll-right 120s linear infinite;
        }

        .carousel-row-left .carousel-track {
          animation: seamless-scroll-left 120s linear infinite;
        }

        @keyframes seamless-scroll-right {
          0% {
            transform: translate3d(0, 0, 0);
          }
          100% {
            transform: translate3d(-33.333%, 0, 0);
          }
        }

        @keyframes seamless-scroll-left {
          0% {
            transform: translate3d(-33.333%, 0, 0);
          }
          100% {
            transform: translate3d(0, 0, 0);
          }
        }

        /* Enhanced hover interactions with reduced motion support */
        @media (prefers-reduced-motion: no-preference) {
          .carousel-row:hover .carousel-track {
            animation-play-state: ${pauseOnHover ? 'paused' : 'running'};
          }
        }

        @media (prefers-reduced-motion: reduce) {
          .carousel-track {
            animation: none !important;
          }
        }

        /* Force uniform image sizing within containers */
        .logo-container-uniform img {
          max-width: 100% !important;
          max-height: 100% !important;
          width: auto !important;
          height: auto !important;
          object-fit: contain !important;
          object-position: center !important;
        }

        /* Performance optimizations */
        .logo-item {
          transform: translateZ(0);
          will-change: transform;
        }

        /* Focus management for accessibility */
        .logo-item:focus-within {
          outline: 2px solid rgba(59, 130, 246, 0.8);
          outline-offset: 2px;
          border-radius: 0.5rem;
        }
      `}</style>
    </div>
  );
};

// Set display name for better debugging
ClientLogosSection.displayName = 'ClientLogosSection';

// Error boundary wrapper for better error handling
class ClientLogosSectionErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): { hasError: boolean } {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ClientLogosSection Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="py-16 overflow-hidden w-full bg-gradient-to-br from-slate-50 via-gray-100 to-slate-200">
          <div className="text-center px-4 sm:px-6 lg:px-8">
            <h2 className="text-4xl font-bold text-center text-gray-800 mb-6">
              Your Success, <span className="bg-clip-text text-transparent bg-gradient-to-r from-gray-600 to-gray-800 font-extrabold">Our Clients</span>
            </h2>
            <p className="text-lg text-gray-700 max-w-2xl mx-auto mb-12 font-['Open_Sans']">
              Delivering excellence to leading organizations across industries
            </p>
            <div className="text-gray-600 text-sm">
              Client logos are temporarily unavailable. Please refresh the page.
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Enhanced component with error boundary
const ClientLogosSectionWithErrorBoundary: React.FC<ClientLogosSectionProps> = (props) => (
  <ClientLogosSectionErrorBoundary>
    <ClientLogosSection {...props} />
  </ClientLogosSectionErrorBoundary>
);

ClientLogosSectionWithErrorBoundary.displayName = 'ClientLogosSectionWithErrorBoundary';

export default ClientLogosSectionWithErrorBoundary;
export { ClientLogosSection as ClientLogosSectionBase };
export type { ClientLogosSectionProps, ClientLogo };