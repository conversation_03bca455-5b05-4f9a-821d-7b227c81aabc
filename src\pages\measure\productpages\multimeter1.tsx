import React from 'react';

const ProductPage: React.FC = () => {
  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Header */}
      <header className="bg-gray-100 py-4 border-b">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold">CHAUVIN ARNOUX</h1>
            <div className="flex space-x-1">
              <SocialButton icon="facebook" />
              <SocialButton icon="twitter" />
              <SocialButton icon="linkedin" />
              <SocialButton icon="youtube" />
              <SocialButton icon="message" />
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-grow">
        {/* AC Digital Multimeter Section */}
        <section className="py-12 border-b">
          <div className="container mx-auto px-4">
            <div className="flex flex-col lg:flex-row items-center gap-8">
              <div className="lg:w-1/3">
                <img 
                  src="/api/placeholder/400/500" 
                  alt="TRMS AC Digital Multimeter MTX 203" 
                  className="mx-auto"
                />
              </div>
              <div className="lg:w-2/3">
                <h2 className="text-5xl font-bold mb-6">
                  <span className="text-gray-700">TRMS AC </span>
                  <span className="text-yellow-500">DIGITAL MULTIMETERS</span>
                </h2>
                
                <ul className="space-y-4 mb-8">
                  <FeatureItem>
                    Models -MTX 203
                  </FeatureItem>
                  <FeatureItem>
                    TRMS AC current and voltage measurements and all the ranges programmed automatically for simpler use
                  </FeatureItem>
                  <FeatureItem>
                    NCV (no-contact voltage) detection for work in total safety
                  </FeatureItem>
                  <FeatureItem>
                    Convenient: backlit screen and built-in torch
                  </FeatureItem>
                  <FeatureItem>
                    Ergonomic: fit in one hand
                  </FeatureItem>
                  <FeatureItem>
                    Practical: thanks to its shockproof sheath with storage slots for leads which is also magnetized for easy mounting on electrical cabinets
                  </FeatureItem>
                </ul>

                <div className="flex gap-4">
                  <button className="px-8 py-2 bg-white text-yellow-500 border border-yellow-500 rounded-full hover:bg-yellow-50 transition">
                    ENQUIRE
                  </button>
                  <button className="px-8 py-2 bg-white text-yellow-500 border border-yellow-500 rounded-full hover:bg-yellow-50 transition">
                    BROCHURE
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Description Section for MTX 203 */}
        <section className="py-12 bg-yellow-100">
          <div className="container mx-auto px-4">
            <div className="flex flex-col lg:flex-row gap-8">
              <div className="lg:w-2/3">
                <p className="mb-4">
                  The patented Multifix mounting system is ideal for use with these multimeters, allowing you to hook it onto a cabinet door or your belt or suspend it... The two-position stand ensures easy reading however the multimeter is positioned. Readings are made even clearer by the blue backlighting of the display (4,000 or 6,000 counts depending on the model). The built-in torch means the multimeter can be used even in the dark.
                </p>
                <p className="mb-4">
                  The rotary switch offers one function per position. On the front panel, 3 keys are all you need to access all the various functions. The 600 V CAT III, IP54 double-well input terminals are easily accessible.
                </p>
                <p className="mb-4">
                  Electrical maintenance operations are optimized thanks to the VLowZ low-impedance voltage measurement function. It is also very easy to carry out initial troubleshooting on PCBs by measuring the resistance, capacitance, diode, etc.
                </p>
                <p className="mb-4">
                  In addition to the traditional measurements, the MTX200 models can measure temperature via a K thermocouple contact sensor delivered as standard. This means users can carry out:
                </p>
                <ul className="list-disc pl-6 mb-4">
                  <li>electrical maintenance</li>
                  <li>initial troubleshooting on PCBs</li>
                  <li>verification of radiator control, etc.</li>
                </ul>
              </div>
              <div className="lg:w-1/3">
                <img 
                  src="/api/placeholder/400/300" 
                  alt="TRMS AC Digital Multimeter in use" 
                  className="mx-auto rounded-lg shadow-lg"
                />
              </div>
            </div>
          </div>
        </section>

        {/* AC DC Digital Multimeter Section */}
        <section className="py-12 border-b">
          <div className="container mx-auto px-4">
            <div className="flex flex-col lg:flex-row items-center gap-8">
              <div className="lg:w-1/3">
                <img 
                  src="/api/placeholder/400/500" 
                  alt="TRMS AC DC Digital Multimeter C.A 5273" 
                  className="mx-auto"
                />
              </div>
              <div className="lg:w-2/3">
                <h2 className="text-5xl font-bold mb-6">
                  <span className="text-gray-700">TRMS AC DC </span>
                  <span className="text-yellow-500">DIGITAL MULTIMETERS</span>
                </h2>
                
                <ul className="space-y-4 mb-8">
                  <FeatureItem>
                    Models - C.A 5273, C.A 5275, C.A 5277
                  </FeatureItem>
                  <FeatureItem>
                    CAT IV 600V / CAT III 1,000V
                  </FeatureItem>
                  <FeatureItem>
                    TRMS measurements
                  </FeatureItem>
                  <FeatureItem>
                    Bi-mode double 6,000-count backlit display and 61+2-segment bargraph (full scale / central zero)
                  </FeatureItem>
                  <FeatureItem>
                    Auto AC/DC selection. Automatic or manual range selection
                  </FeatureItem>
                  <FeatureItem>
                    VLowZ low-impedance voltage measurement with low-pass filter
                  </FeatureItem>
                  <FeatureItem>
                    1,000 V, 10 A
                  </FeatureItem>
                  <FeatureItem>
                    Resistance / audible continuity, Temperature, Capacitance
                  </FeatureItem>
                  <FeatureItem>
                    Max / Min storage
                  </FeatureItem>
                  <FeatureItem>
                    3-year warranty
                  </FeatureItem>
                </ul>

                <div className="flex gap-4">
                  <button className="px-8 py-2 bg-white text-yellow-500 border border-yellow-500 rounded-full hover:bg-yellow-50 transition">
                    ENQUIRE
                  </button>
                  <button className="px-8 py-2 bg-white text-yellow-500 border border-yellow-500 rounded-full hover:bg-yellow-50 transition">
                    BROCHURE
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Description Section for C.A 5273 */}
        <section className="py-12 bg-yellow-100">
          <div className="container mx-auto px-4">
            <div className="flex flex-col lg:flex-row gap-8">
              <div className="lg:w-2/3">
                <p className="mb-4">
                  The C.A 5273 is a comprehensive multimeter for electrical maintenance of installations and small AC and DC machines, with a double 6,000-count backlit display and a 61+2-segment bargraph with remanent effect.
                </p>
                <p className="mb-4">
                  Its 600V CAT IV safety is backed by IP54 ingress protection.
                </p>
                <p className="mb-4">
                  Designed and manufactured in France by CHAUVIN ARNOUX, this multimeter comes with a 3-year warranty.
                </p>
              </div>
              <div className="lg:w-1/3">
                <img 
                  src="/api/placeholder/400/300" 
                  alt="Technician using C.A 5273 multimeter" 
                  className="mx-auto rounded-lg shadow-lg"
                />
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <p>© 2025 CHAUVIN ARNOUX. All rights reserved.</p>
            <div className="flex space-x-4">
              <SocialButton icon="facebook" light />
              <SocialButton icon="twitter" light />
              <SocialButton icon="linkedin" light />
              <SocialButton icon="youtube" light />
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

// Helper Components
const FeatureItem: React.FC<{children: React.ReactNode}> = ({ children }) => (
  <li className="flex items-start gap-2">
    <div className="mt-1 flex-shrink-0 w-5 h-5 rounded-full bg-yellow-500 flex items-center justify-center text-white">
      ✓
    </div>
    <span>{children}</span>
  </li>
);

const SocialButton: React.FC<{icon: string; light?: boolean}> = ({ icon, light }) => {
  const baseClasses = "w-8 h-8 flex items-center justify-center rounded-full";
  const lightClasses = light ? "bg-gray-700 hover:bg-gray-600" : "bg-gray-200 hover:bg-gray-300 text-gray-700";
  
  return (
    <a href="#" className={`${baseClasses} ${lightClasses} transition-colors duration-200`}>
      {icon === "facebook" && "f"}
      {icon === "twitter" && "t"}
      {icon === "linkedin" && "in"}
      {icon === "youtube" && "yt"}
      {icon === "message" && "msg"}
    </a>
  );
};

export default ProductPage;