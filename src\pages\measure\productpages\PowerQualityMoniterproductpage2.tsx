import React from 'react';
import { Link } from 'react-router-dom';
import PageLayout from '@/components/layout/PageLayout';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion } from 'framer-motion';
import { ChevronRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const PowerQualityMonitorProduct2 = () => {
  return (
    <PageLayout
      title="Dranetz PQ3K/5K Power Quality Monitor"
      subtitle="Advanced power quality monitoring with extensive analysis capabilities"
      category="measure"
    >
      {/* Secondary Navigation Bar */}
      <div className="bg-background border-b border-border">
        <div className="container mx-auto">
          <div className="flex items-center overflow-x-auto whitespace-nowrap py-2 px-4">
            <Link to="/" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
              Home
            </Link>
            <ChevronRight className="h-4 w-4 mx-1 text-muted-foreground flex-shrink-0" />
            <Link to="/measure" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
              Measure
            </Link>
            <ChevronRight className="h-4 w-4 mx-1 text-muted-foreground flex-shrink-0" />
            <Link to="/measure/power-quality-monitors" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
              Power Quality Monitors
            </Link>
            <ChevronRight className="h-4 w-4 mx-1 text-muted-foreground flex-shrink-0" />
            <span className="text-sm font-medium">Dranetz PQ3K/5K</span>
          </div>
        </div>
      </div>

      {/* Product Navigation Tabs */}
      <div className="bg-background border-b border-border mb-8 sticky top-16 z-30">
        <div className="container mx-auto">
          <div className="flex justify-between items-center overflow-x-auto whitespace-nowrap">
            <div className="flex">
              <Link 
                to="/measure/power-quality-monitors/dranetz-pq3k-5k" 
                className="py-3 px-4 border-b-2 border-primary font-medium text-sm"
              >
                Overview
              </Link>
              <Link 
                to="#" 
                className="py-3 px-4 border-b-2 border-transparent hover:border-primary/50 text-muted-foreground hover:text-foreground transition-colors text-sm"
              >
                One SDS
              </Link>
              <Link 
                to="#" 
                className="py-3 px-4 border-b-2 border-transparent hover:border-primary/50 text-muted-foreground hover:text-foreground transition-colors text-sm"
              >
                Greater PQM
              </Link>
              <Link 
                to="#" 
                className="py-3 px-4 border-b-2 border-transparent hover:border-primary/50 text-muted-foreground hover:text-foreground transition-colors text-sm"
              >
                Datasheet
              </Link>
            </div>
            <div className="hidden md:block">
              <Button size="sm" asChild className="mr-2">
                <Link to="/contact/sales">Contact Sales</Link>
              </Button>
              <Button size="sm" variant="outline">
                Download Brochure
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Product Section */}
      <section className="mb-16">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center"
        >
          <div className="flex justify-center bg-muted/30 p-8 rounded-lg">
            <img 
              src="/api/placeholder/400/360" 
              alt="Dranetz PQ3K/5K Power Quality Monitor" 
              className="w-full max-w-md object-contain"
            />
          </div>
          
          <div>
            <div className="flex items-center mb-4">
              <Badge variant="secondary" className="bg-yellow-100 text-yellow-700 hover:bg-yellow-200 mr-2">Premium</Badge>
              <Badge variant="secondary" className="bg-blue-100 text-blue-700 hover:bg-blue-200">Class A Certified</Badge>
            </div>
            
            <h2 className="text-3xl font-bold mb-4">POWER QUALITY <span className="text-primary">MONITOR</span></h2>
            <h3 className="text-xl text-muted-foreground mb-6">PQM/C/M Series</h3>
            
            <div className="mb-8">
              <ul className="space-y-3">
                <li className="flex items-start gap-2">
                  <span className="h-4 w-4 rounded-full bg-primary mt-1 flex-shrink-0"></span>
                  <span><strong>Model:</strong> PQM/C/M</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="h-4 w-4 rounded-full bg-primary mt-1 flex-shrink-0"></span>
                  <span>PQC installed via RS-D interface of the device</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="h-4 w-4 rounded-full bg-primary mt-1 flex-shrink-0"></span>
                  <span>Adjustable report period</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="h-4 w-4 rounded-full bg-primary mt-1 flex-shrink-0"></span>
                  <span>Adjustable report scope (extensive, standard, details, event overviews)</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="h-4 w-4 rounded-full bg-primary mt-1 flex-shrink-0"></span>
                  <span>Direct compliance assessment of standards EN 50160, IEC 61000-2-2/2-4/3-6/3-7, GOST</span>
                </li>
              </ul>
            </div>
            
            <div className="py-4 px-5 bg-yellow-50 rounded-lg mb-8">
              <p className="text-amber-800 text-sm">
                <strong>Limited Time Offer:</strong> Get a complimentary training session with purchase of the Dranetz PQ3K/5K series monitors.
              </p>
            </div>
            
            <div className="flex flex-wrap gap-4">
              <Button size="lg" asChild>
                <Link to="/contact/sales">Request Quote</Link>
              </Button>
              <Button size="lg" variant="outline">
                Download Brochure
              </Button>
              <Button size="lg" variant="ghost">
                View Demo
              </Button>
            </div>
          </div>
        </motion.div>
      </section>
      
      {/* Tabs Section */}
      <section className="mb-16">
        <Tabs defaultValue="monitoring" className="w-full">
          <TabsList className="w-full justify-start border-b rounded-none bg-transparent h-auto p-0">
            <TabsTrigger 
              value="monitoring" 
              className="rounded-none border-b-2 data-[state=active]:border-primary border-transparent px-6 py-3 h-auto"
            >
              Monitoring & Alarming
            </TabsTrigger>
            <TabsTrigger 
              value="description" 
              className="rounded-none border-b-2 data-[state=active]:border-primary border-transparent px-6 py-3 h-auto"
            >
              Description
            </TabsTrigger>
            <TabsTrigger 
              value="features" 
              className="rounded-none border-b-2 data-[state=active]:border-primary border-transparent px-6 py-3 h-auto"
            >
              Features
            </TabsTrigger>
            <TabsTrigger 
              value="parameters" 
              className="rounded-none border-b-2 data-[state=active]:border-primary border-transparent px-6 py-3 h-auto"
            >
              Parameters
            </TabsTrigger>
            <TabsTrigger 
              value="reports" 
              className="rounded-none border-b-2 data-[state=active]:border-primary border-transparent px-6 py-3 h-auto"
            >
              Reports
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="monitoring" className="pt-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="col-span-2">
                <h3 className="text-xl font-bold mb-4">Advanced Monitoring & Alarming System</h3>
                <p className="text-muted-foreground mb-6">
                  The PQ3K/5K series features a comprehensive monitoring and alarming system designed to detect and notify 
                  you of power quality issues before they cause equipment damage or operational disruptions.
                </p>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-8">
                  <div className="bg-white p-5 rounded-lg shadow-sm border border-border">
                    <h4 className="font-semibold mb-3">Limit Value Monitoring</h4>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                        <span>12 configurable limit values</span>
                      </li>
                      <li className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                        <span>User-defined thresholds</span>
                      </li>
                      <li className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                        <span>Combinable monitoring functions</span>
                      </li>
                    </ul>
                  </div>
                  
                  <div className="bg-white p-5 rounded-lg shadow-sm border border-border">
                    <h4 className="font-semibold mb-3">Contract Violation Monitoring</h4>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                        <span>4 contract violations with 5 graphs each</span>
                      </li>
                      <li className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                        <span>Track compliance with service agreements</span>
                      </li>
                      <li className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                        <span>Historical trend analysis</span>
                      </li>
                    </ul>
                  </div>
                </div>
                
                <div className="bg-muted/30 p-6 rounded-lg mb-6">
                  <h4 className="font-semibold mb-3">Operating Frequency Conditions</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    Configure up to 5 operating frequencies with infinitely variable conditions to tailor the monitoring
                    to your specific application requirements.
                  </p>
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <div className="bg-white p-3 rounded-md border border-border text-center">
                      <div className="text-xl font-semibold mb-1 text-primary">50 Hz</div>
                      <div className="text-xs text-muted-foreground">Standard grid</div>
                    </div>
                    <div className="bg-white p-3 rounded-md border border-border text-center">
                      <div className="text-xl font-semibold mb-1 text-primary">60 Hz</div>
                      <div className="text-xs text-muted-foreground">Americas grid</div>
                    </div>
                    <div className="bg-white p-3 rounded-md border border-border text-center">
                      <div className="text-xl font-semibold mb-1 text-primary">400 Hz</div>
                      <div className="text-xs text-muted-foreground">Aviation/Marine</div>
                    </div>
                  </div>
                </div>
                
                <div className="bg-muted/30 p-6 rounded-lg">
                  <h4 className="font-semibold mb-3">Alarm Notification System</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    Receive instant notifications when power quality issues are detected through multiple channels.
                  </p>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="bg-white p-3 rounded-md border border-border text-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mx-auto mb-2 text-primary"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/></svg>
                      <div className="text-xs">Web Interface</div>
                    </div>
                    <div className="bg-white p-3 rounded-md border border-border text-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mx-auto mb-2 text-primary"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/></svg>
                      <div className="text-xs">SMS</div>
                    </div>
                    <div className="bg-white p-3 rounded-md border border-border text-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mx-auto mb-2 text-primary"><path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/><polyline points="22,6 12,13 2,6"/></svg>
                      <div className="text-xs">Email</div>
                    </div>
                    <div className="bg-white p-3 rounded-md border border-border text-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mx-auto mb-2 text-primary"><path d="M18 11.03V8a6 6 0 0 0-12 0v3.03"/><path d="M10 2h4"/><path d="M7 22h10"/><path d="M7 14.5h10"/><path d="M7 18h10"/></svg>
                      <div className="text-xs">Relay Outputs</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="space-y-6">
                <div className="flex justify-center mb-4">
                  <img 
                    src="/api/placeholder/300/250" 
                    alt="Monitoring Interface" 
                    className="rounded-lg border border-border shadow-sm w-full h-auto"
                  />
                </div>
                
                <div className="bg-white p-6 rounded-lg shadow-sm border border-border">
                  <h4 className="font-semibold mb-4">Key Monitoring Features</h4>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <span className="text-primary font-bold mr-2">✓</span>
                      <span className="text-sm">Real-time measurement display</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-primary font-bold mr-2">✓</span>
                      <span className="text-sm">Configurable alarm thresholds</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-primary font-bold mr-2">✓</span>
                      <span className="text-sm">Automatic event capture</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-primary font-bold mr-2">✓</span>
                      <span className="text-sm">Advanced triggering options</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-primary font-bold mr-2">✓</span>
                      <span className="text-sm">Long-term trend recording</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-primary font-bold mr-2">✓</span>
                      <span className="text-sm">Multiple communication protocols</span>
                    </li>
                  </ul>
                </div>
                
                <div className="bg-yellow-50 p-6 rounded-lg">
                  <h4 className="font-semibold mb-4 text-amber-800">Featured Application</h4>
                  <p className="text-sm text-amber-700 mb-4">
                    Industrial monitoring for critical manufacturing processes where power quality 
                    issues can cause costly production delays.
                  </p>
                  <Button size="sm" variant="outline" className="border-amber-500 text-amber-700 hover:bg-amber-100">
                    Case Study
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="description" className="pt-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <h3 className="text-xl font-bold mb-6">Product Description</h3>
                
                <div className="prose prose-sm max-w-none text-muted-foreground mb-6">
                  <p>
                    The PQM is a cost-effective monitoring solution that combines general power/PQ detection with 
                    operation recording and adds energy monitoring in one instrument. Wide-ranging applications 
                    include utility, facility, manufacturing, semi-chemical, and mission-critical purposes.
                  </p>
                  
                  <p>
                    The PQM is mounted in a 144mm x 144mm panel-mounted enclosure with a high-resolution, color TFT display.
                  </p>
                  
                  <p>
                    Traditionally, power quality monitoring is only conducted as a reaction to visible issues such as blown fuses, 
                    plant malfunctions, process interruptions or server/computer breakdowns. However, all these problems 
                    cost money and nobody wants to experience an extended upset just to create a corresponding 
                    visual for analysis. Therefore, the biggest advantage of continuous power quality monitoring is that users 
                    put themselves in a position to proactively build up their knowledge, thus increasing system availability.
                  </p>
                  
                  <p>
                    Greater PQM™ / PQM helps to detect trouble before it can cause damage and provides data for the 
                    identification of root causes as well as more accurate reports.
                  </p>
                  
                  <p>
                    Greater PQM™ / PQM is a Class-A device according to the IEC 61000-4-30 A power quality standard. 
                    It can provide reliable and comparable information for regulatory agencies, negotiations with 
                    energy suppliers, or internal quality control.
                  </p>
                  
                  <p>
                    Conformity reports can be generated directly via the device's web interface.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
                  <div className="bg-muted/30 p-6 rounded-lg">
                    <h4 className="font-semibold mb-4">Applications</h4>
                    <ul className="space-y-2">
                      <li className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                        <span className="text-sm">Industrial facilities with sensitive equipment</span>
                      </li>
                      <li className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                        <span className="text-sm">Data centers and server rooms</span>
                      </li>
                      <li className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                        <span className="text-sm">Healthcare facilities with critical medical equipment</span>
                      </li>
                      <li className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                        <span className="text-sm">Utility power quality compliance monitoring</span>
                      </li>
                      <li className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                        <span className="text-sm">Manufacturing plants with sensitive processes</span>
                      </li>
                    </ul>
                  </div>
                  
                  <div className="bg-muted/30 p-6 rounded-lg">
                    <h4 className="font-semibold mb-4">Benefits</h4>
                    <ul className="space-y-2">
                      <li className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                        <span className="text-sm">Prevent costly equipment damage and downtime</span>
                      </li>
                      <li className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                        <span className="text-sm">Identify power quality issues before they become critical</span>
                      </li>
                      <li className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                        <span className="text-sm">Document compliance with power quality standards</span>
                      </li>
                      <li className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                        <span className="text-sm">Reduce troubleshooting time with detailed event data</span>
                      </li>
                      <li className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                        <span className="text-sm">Validate the effectiveness of power quality improvements</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
              
              <div className="space-y-6">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-border">
                  <h4 className="font-semibold mb-4">Device Specifications</h4>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <span className="text-primary font-bold mr-2">•</span>
                      <div>
                        <span className="text-sm font-medium">Dimensions</span>
                        <p className="text-xs text-muted-foreground">144 x 144 x 85 mm</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <span className="text-primary font-bold mr-2">•</span>
                      <div>
                        <span className="text-sm font-medium">Display</span>
                        <p className="text-xs text-muted-foreground">High-resolution color TFT</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <span className="text-primary font-bold mr-2">•</span>
                      <div>
                        <span className="text-sm font-medium">Power Supply</span>
                        <p className="text-xs text-muted-foreground">230V / 50 Hz and 120V / 60Hz</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <span className="text-primary font-bold mr-2">•</span>
                      <div>
                        <span className="text-sm font-medium">Installation</span>
                        <p className="text-xs text-muted-foreground">Panel-mounted enclosure</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <span className="text-primary font-bold mr-2">•</span>
                      <div>
                        <span className="text-sm font-medium">Measurement Channels</span>
                        <p className="text-xs text-muted-foreground">4 voltage, 4 current</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <span className="text-primary font-bold mr-2">•</span>
                      <div>
                        <span className="text-sm font-medium">Communication</span>
                        <p className="text-xs text-muted-foreground">Ethernet, RS-485, USB</p>
                      </div>
                    </li>
                  </ul>
                </div>
                
                <div className="bg-white p-6 rounded-lg shadow-sm border border-border">
                  <h4 className="font-semibold mb-4">Customer Testimonial</h4>
                  <blockquote className="text-sm italic text-muted-foreground mb-4">
                    "The Dranetz PQ3K/5K has been instrumental in helping us identify power quality issues that were causing 
                    intermittent equipment failures. The detailed reports and alarms allow us to take corrective action 
                    before problems affect our operations."
                  </blockquote>
                  <div className="text-sm font-medium">John Anderson</div>
                  <div className="text-xs text-muted-foreground">Senior Electrical Engineer, Global Manufacturing Inc.</div>
                </div>
                
                <div className="bg-primary/10 p-6 rounded-lg">
                  <h4 className="font-semibold mb-4">Need Technical Support?</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    Our team of experts is available to assist with installation, configuration, and troubleshooting.
                  </p>
                  <Button size="sm" className="w-full" asChild>
                    <Link to="/contact">Contact Support Team</Link>
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="features" className="pt-8">
            <h3 className="text-xl font-bold mb-6">Key Features</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-10">
              <Card className="border border-border overflow-hidden">
                <div className="bg-blue-50 p-4">
                  <h4 className="text-lg font-semibold text-blue-700 mb-1">CLEAR</h4>
                  <p className="text-sm text-blue-600">
                    Visual clarity for precise information
                  </p>
                </div>
                <CardContent className="pt-6">
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <span className="text-primary font-bold mr-2">✓</span>
                      <span className="text-sm">High resolution, colour TFT display for precise indication of measured values</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-primary font-bold mr-2">✓</span>
                      <span className="text-sm">Extensively visible status information/alarm in password protected, data recording, immediate</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-primary font-bold mr-2">✓</span>
                      <span className="text-sm">Clear design for intuitive operation</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
              
              <Card className="border border-border overflow-hidden">
                <div className="bg-green-50 p-4">
                  <h4 className="text-lg font-semibold text-green-700 mb-1">INTUITIVE</h4>
                  <p className="text-sm text-green-600">
                    User-friendly interface design
                  </p>
                </div>
                <CardContent className="pt-6">
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <span className="text-primary font-bold mr-2">✓</span>
                      <span className="text-sm">Immediate graphical data trend value visualization</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-primary font-bold mr-2">✓</span>
                      <span className="text-sm">Logical arrangement of measured value information for quick data access</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-primary font-bold mr-2">✓</span>
                      <span className="text-sm">Service area for maintenance and commissioning</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
              
              <Card className="border border-border overflow-hidden">
                <div className="bg-purple-50 p-4">
                  <h4 className="text-lg font-semibold text-purple-700 mb-1">MULTIFUNCTIONAL</h4>
                  <p className="text-sm text-purple-600">
                    Comprehensive monitoring capabilities
                  </p>
                </div>
                <CardContent className="pt-6">
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <span className="text-primary font-bold mr-2">✓</span>
                      <span className="text-sm">Certified power quality monitoring according to IEC 61000-4-30, Ed. 3, class A</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-primary font-bold mr-2">✓</span>
                      <span className="text-sm">Acquisition of energy consumption: Meters and load profiles</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-primary font-bold mr-2">✓</span>
                      <span className="text-sm">Plant condition monitoring with advanced analytics</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
              
              <Card className="border border-border overflow-hidden">
                <div className="bg-amber-50 p-4">
                  <h4 className="text-lg font-semibold text-amber-700 mb-1">FLEXIBLE</h4>
                  <p className="text-sm text-amber-600">
                    Adaptable to your specific needs
                  </p>
                </div>
                <CardContent className="pt-6">
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <span className="text-primary font-bold mr-2">✓</span>
                      <span className="text-sm">Application-full network configurations without hardware variance</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-primary font-bold mr-2">✓</span>
                      <span className="text-sm">Freely selectable measured variables for limit values and matrix</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-primary font-bold mr-2">✓</span>
                      <span className="text-sm">Freely selectable alarm conditions with universal alarm possibilities</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>
            
            <div className="mb-10">
              <Card className="border border-border overflow-hidden">
                <div className="bg-cyan-50 p-4">
                  <h4 className="text-lg font-semibold text-cyan-700 mb-1">SCALABLE</h4>
                  <p className="text-sm text-cyan-600">
                    Grow with your monitoring needs
                  </p>
                </div>
                <CardContent className="pt-6">
                  <p className="mb-6">Extendable driver design (functionality, interfaces, I/Os without limits)</p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-muted/20 p-4 rounded-lg text-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mx-auto mb-2 text-primary"><path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/><polyline points="7.5 4.21 12 6.81 16.5 4.21"/><polyline points="7.5 19.79 7.5 14.6 3 12"/><polyline points="21 12 16.5 14.6 16.5 19.79"/><polyline points="3.27 6.96 12 12.01 20.73 6.96"/><line x1="12" y1="22.08" x2="12" y2="12"/></svg>
                      <div className="text-sm font-medium">Modular Design</div>
                    </div>
                    <div className="bg-muted/20 p-4 rounded-lg text-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mx-auto mb-2 text-primary"><rect x="2" y="4" width="20" height="16" rx="2"/><path d="M2 8h20"/><circle cx="8" cy="14" r="2"/><path d="M8 12h8"/><path d="M16 14h.01"/></svg>
                      <div className="text-sm font-medium">Expandable I/O</div>
                    </div>
                    <div className="bg-muted/20 p-4 rounded-lg text-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mx-auto mb-2 text-primary"><path d="M12 2v1"/><path d="M12 21v1"/><path d="M4.22 4.22l.77.77"/><path d="M19.01 19.01l.77.77"/><path d="M2 12h1"/><path d="M21 12h1"/><path d="M4.22 19.78l.77-.77"/><path d="M19.01 4.99l.77-.77"/><circle cx="12" cy="12" r="7"/><path d="M12 9v3l1.5 1.5"/></svg>
                      <div className="text-sm font-medium">Software Updates</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h4 className="text-lg font-medium mb-6">Additional Features</h4>
                <div className="space-y-6">
                  <div className="flex items-start">
                    <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mr-4 flex-shrink-0">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary"><path d="M21 7v6h-6"/><path d="M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7"/></svg>
                    </div>
                    <div>
                      <h5 className="font-medium text-base mb-1">Continuous Monitoring</h5>
                      <p className="text-sm text-muted-foreground">
                        24/7 monitoring of all power quality parameters with configurable recording intervals.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mr-4 flex-shrink-0">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary"><path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"/><line x1="4" x2="4" y1="22" y2="15"/></svg>
                    </div>
                    <div>
                      <h5 className="font-medium text-base mb-1">Standards Compliance</h5>
                      <p className="text-sm text-muted-foreground">
                        Certified to IEC 61000-4-30 Class A for reliable and accurate measurements.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mr-4 flex-shrink-0">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary"><circle cx="12" cy="12" r="10"/><path d="m9 12 2 2 4-4"/></svg>
                    </div>
                    <div>
                      <h5 className="font-medium text-base mb-1">Reliability</h5>
                      <p className="text-sm text-muted-foreground">
                        Robust design with high-quality components for long-term operation in demanding environments.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div>
                <h4 className="text-lg font-medium mb-6">Unique Selling Points</h4>
                <div className="bg-gradient-to-r from-primary/5 to-primary/20 p-6 rounded-lg">
                  <ul className="space-y-4">
                    <li className="flex items-start">
                      <div className="w-8 h-8 rounded-full bg-white flex items-center justify-center mr-3 flex-shrink-0">
                        <span className="text-primary font-bold">1</span>
                      </div>
                      <div>
                        <h5 className="font-medium text-base">Class A Certification</h5>
                        <p className="text-sm text-muted-foreground">
                          Highest level of measurement accuracy per IEC 61000-4-30.
                        </p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <div className="w-8 h-8 rounded-full bg-white flex items-center justify-center mr-3 flex-shrink-0">
                        <span className="text-primary font-bold">2</span>
                      </div>
                      <div>
                        <h5 className="font-medium text-base">Comprehensive Analysis</h5>
                        <p className="text-sm text-muted-foreground">
                          Full suite of power quality measurements in a single device.
                        </p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <div className="w-8 h-8 rounded-full bg-white flex items-center justify-center mr-3 flex-shrink-0">
                        <span className="text-primary font-bold">3</span>
                      </div>
                      <div>
                        <h5 className="font-medium text-base">Web-based Reporting</h5>
                        <p className="text-sm text-muted-foreground">
                          Generate standards compliance reports directly from the device.
                        </p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <div className="w-8 h-8 rounded-full bg-white flex items-center justify-center mr-3 flex-shrink-0">
                        <span className="text-primary font-bold">4</span>
                      </div>
                      <div>
                        <h5 className="font-medium text-base">Flexible Alerting</h5>
                        <p className="text-sm text-muted-foreground">
                          Customizable thresholds and multiple notification methods.
                        </p>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="parameters" className="pt-8">
            <h3 className="text-xl font-bold mb-6">Parameters according to IEC 61000-4-30, Class A</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-6">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-border">
                  <h4 className="font-semibold mb-4">Voltage & Frequency Measurements</h4>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                      <div>
                        <span className="text-sm font-medium">Power frequency</span>
                        <p className="text-xs text-muted-foreground">Accurate measurement of system frequency</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                      <div>
                        <span className="text-sm font-medium">Magnitude of supply voltage</span>
                        <p className="text-xs text-muted-foreground">L-N, L-L voltage measurements</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                      <div>
                        <span className="text-sm font-medium">Flicker</span>
                        <p className="text-xs text-muted-foreground">Pst and Plt measurements per standard</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                      <div>
                        <span className="text-sm font-medium">Supply voltage dips / swells</span>
                        <p className="text-xs text-muted-foreground">Event detection with configurable thresholds</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                      <div>
                        <span className="text-sm font-medium">Voltage interruptions</span>
                        <p className="text-xs text-muted-foreground">Detection and classification of interruptions</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                      <div>
                        <span className="text-sm font-medium">Supply voltage unbalance</span>
                        <p className="text-xs text-muted-foreground">Measurement of negative and zero sequence components</p>
                      </div>
                    </li>
                  </ul>
                </div>
                
                <div className="bg-muted/20 p-6 rounded-lg">
                  <h4 className="font-semibold mb-4">Measurement Methodology</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    The PQ3K/5K employs advanced digital signal processing techniques to ensure the highest level of 
                    measurement accuracy according to IEC 61000-4-30 Class A standards.
                  </p>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="bg-white p-3 rounded-md border border-border text-center">
                      <div className="text-sm font-medium mb-1">10/12 Cycle Aggregation</div>
                      <div className="text-xs text-muted-foreground">Per IEC standard requirements</div>
                    </div>
                    <div className="bg-white p-3 rounded-md border border-border text-center">
                      <div className="text-sm font-medium mb-1">Flagging Concept</div>
                      <div className="text-xs text-muted-foreground">MultiPhase approach</div>
                    </div>
                    <div className="bg-white p-3 rounded-md border border-border text-center">
                      <div className="text-sm font-medium mb-1">Synchronized Measurements</div>
                      <div className="text-xs text-muted-foreground">Clock uncertainty ≤20ms</div>
                    </div>
                    <div className="bg-white p-3 rounded-md border border-border text-center">
                      <div className="text-sm font-medium mb-1">Gapless Recording</div>
                      <div className="text-xs text-muted-foreground">No missed measurement cycles</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="space-y-6">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-border">
                  <h4 className="font-semibold mb-4">Harmonics & Advanced Measurements</h4>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                      <div>
                        <span className="text-sm font-medium">Voltage harmonics</span>
                        <p className="text-xs text-muted-foreground">Measurement up to the 50th order</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                      <div>
                        <span className="text-sm font-medium">Voltage interharmonics</span>
                        <p className="text-xs text-muted-foreground">Measurement between harmonic orders</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                      <div>
                        <span className="text-sm font-medium">Mains signaling voltage</span>
                        <p className="text-xs text-muted-foreground">Detection of control signals on supply voltage</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                      <div>
                        <span className="text-sm font-medium">Rapid voltage changes (RVC)</span>
                        <p className="text-xs text-muted-foreground">Detection and measurement of rapid voltage changes</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                      <div>
                        <span className="text-sm font-medium">Current harmonics</span>
                        <p className="text-xs text-muted-foreground">Comprehensive current harmonic analysis</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <span className="h-2 w-2 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0"></span>
                      <div>
                        <span className="text-sm font-medium">Current measurements</span>
                        <p className="text-xs text-muted-foreground">Magnitude, harmonics, interharmonics</p>
                      </div>
                    </li>
                  </ul>
                </div>
                
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border border-blue-100">
                  <h4 className="font-semibold mb-4 text-blue-800">Standards Compliance</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h5 className="text-sm font-medium text-blue-700 mb-2">Power Quality Standards</h5>
                      <ul className="space-y-1">
                        <li className="text-xs text-blue-600">IEC 61000-4-30 Ed.3, Class A</li>
                        <li className="text-xs text-blue-600">IEC 61000-4-7:2002</li>
                        <li className="text-xs text-blue-600">IEC 61000-4-15:2010</li>
                        <li className="text-xs text-blue-600">EN 50160</li>
                      </ul>
                    </div>
                    <div>
                      <h5 className="text-sm font-medium text-blue-700 mb-2">Safety & EMC Standards</h5>
                      <ul className="space-y-1">
                        <li className="text-xs text-blue-600">IEC 61010-1</li>
                        <li className="text-xs text-blue-600">IEC 61326-1</li>
                        <li className="text-xs text-blue-600">IEC 60529</li>
                        <li className="text-xs text-blue-600">UL 61010-1 (optional)</li>
                      </ul>
                    </div>
                  </div>
                </div>
                
                <div className="relative overflow-hidden rounded-lg">
                  <img 
                    src="/api/placeholder/500/300" 
                    alt="Parameter Measurement Diagram" 
                    className="w-full h-auto rounded-lg"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end">
                    <div className="p-4 text-white">
                      <h5 className="font-medium mb-1">Comprehensive Parameter Measurement</h5>
                      <p className="text-xs text-white/80">
                        The PQ3K/5K monitors all parameters required by IEC 61000-4-30 Class A
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="reports" className="pt-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <h3 className="text-xl font-bold mb-6">Conformity Reports Via Browser Without Extra Software</h3>
                <p className="text-muted-foreground mb-8">
                  The PQ3K/5K provides a powerful web-based reporting system that allows you to generate comprehensive 
                  power quality reports directly from your browser without requiring any additional software installation.
                </p>
                
                <div className="bg-white p-6 rounded-lg shadow-sm border border-border mb-8">
                  <h4 className="font-semibold mb-4">Report Types</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="flex items-start">
                      <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3 mt-1 flex-shrink-0">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-700"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/><path d="M16 13H8"/><path d="M16 17H8"/><path d="M10 9H8"/></svg>
                      </div>
                      <div>
                        <h5 className="font-medium text-sm mb-1">EN 50160 Compliance Reports</h5>
                        <p className="text-xs text-muted-foreground">
                          Detailed reports showing compliance with European power quality standards, including statistics for voltage variations, 
                          harmonics, flicker, and more.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3 mt-1 flex-shrink-0">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-green-700"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/><path d="M16 13H8"/><path d="M16 17H8"/><path d="M10 9H8"/></svg>
                      </div>
                      <div>
                        <h5 className="font-medium text-sm mb-1">IEC 61000 Series Reports</h5>
                        <p className="text-xs text-muted-foreground">
                          Reports following IEC 61000-2-2/2-4/3-6/3-7 standards for electromagnetic compatibility in power systems, 
                          providing detailed analysis for industrial and utility applications.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-10 h-10 rounded-full bg-amber-100 flex items-center justify-center mr-3 mt-1 flex-shrink-0">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-amber-700"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/><path d="M16 13H8"/><path d="M16 17H8"/><path d="M10 9H8"/></svg>
                      </div>
                      <div>
                        <h5 className="font-medium text-sm mb-1">GOST Reports</h5>
                        <p className="text-xs text-muted-foreground">
                          Reports following the GOST standard for power quality, applicable in certain regions and 
                          providing comprehensive analysis of power system parameters.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mr-3 mt-1 flex-shrink-0">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-purple-700"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/><path d="M16 13H8"/><path d="M16 17H8"/><path d="M10 9H8"/></svg>
                      </div>
                      <div>
                        <h5 className="font-medium text-sm mb-1">Custom Reports</h5>
                        <p className="text-xs text-muted-foreground">
                          User-defined reports with customizable parameters and thresholds, allowing you to focus on the 
                          specific power quality aspects most relevant to your application.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-center mb-8">
                  <img 
                    src="/api/placeholder/700/400" 
                    alt="Conformity Report Sample" 
                    className="w-full max-w-2xl h-auto rounded-lg border border-border"
                  />
                </div>
                
                <div className="bg-muted/20 p-6 rounded-lg">
                  <h4 className="font-semibold mb-4">Report Features</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                    <div className="bg-white p-4 rounded-lg border border-border">
                      <h5 className="font-medium text-sm mb-2">Adjustable Report Period</h5>
                      <p className="text-xs text-muted-foreground">
                        Configure report duration from days to months for short-term or long-term analysis.
                      </p>
                    </div>
                    <div className="bg-white p-4 rounded-lg border border-border">
                      <h5 className="font-medium text-sm mb-2">Flexible Report Scope</h5>
                      <p className="text-xs text-muted-foreground">
                        Choose from extensive, standard, detailed, or event overview formats.
                      </p>
                    </div>
                    <div className="bg-white p-4 rounded-lg border border-border">
                      <h5 className="font-medium text-sm mb-2">Customized Forms</h5>
                      <p className="text-xs text-muted-foreground">
                        UTILB-PQ customized specific forms for specialized applications.
                      </p>
                    </div>
                    <div className="bg-white p-4 rounded-lg border border-border">
                      <h5 className="font-medium text-sm mb-2">Company Branding</h5>
                      <p className="text-xs text-muted-foreground">
                        Add your company logo to reports for professional presentation.
                      </p>
                    </div>
                    <div className="bg-white p-4 rounded-lg border border-border">
                      <h5 className="font-medium text-sm mb-2">Export Options</h5>
                      <p className="text-xs text-muted-foreground">
                        Save reports in multiple formats including PDF, CSV, and PQDIF.
                      </p>
                    </div>
                    <div className="bg-white p-4 rounded-lg border border-border">
                      <h5 className="font-medium text-sm mb-2">Scheduled Reports</h5>
                      <p className="text-xs text-muted-foreground">
                        Automate report generation on daily, weekly, or monthly schedules.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="space-y-6">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-border">
                  <h4 className="font-semibold mb-4">Report Examples</h4>
                  <div className="space-y-4">
                    <div className="group relative overflow-hidden rounded-md">
                      <img 
                        src="/api/placeholder/300/150" 
                        alt="Voltage Summary Report" 
                        className="w-full h-auto rounded-md"
                      />
                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                        <span className="text-white text-xs font-medium">Voltage Summary</span>
                      </div>
                    </div>
                    <div className="group relative overflow-hidden rounded-md">
                      <img 
                        src="/api/placeholder/300/150" 
                        alt="Harmonics Analysis Report" 
                        className="w-full h-auto rounded-md"
                      />
                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                        <span className="text-white text-xs font-medium">Harmonics Analysis</span>
                      </div>
                    </div>
                    <div className="group relative overflow-hidden rounded-md">
                      <img 
                        src="/api/placeholder/300/150" 
                        alt="Event Log Report" 
                        className="w-full h-auto rounded-md"
                      />
                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                        <span className="text-white text-xs font-medium">Event Log</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="bg-blue-50 p-6 rounded-lg">
                  <h4 className="font-semibold mb-4 text-blue-800">Integration Options</h4>
                  <p className="text-sm text-blue-700 mb-4">
                    In addition to the built-in web-based reporting, the PQ3K/5K also supports integration with third-party software and systems.
                  </p>
                  <ul className="space-y-2">
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      <span className="text-sm text-blue-700">SCADA system integration</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      <span className="text-sm text-blue-700">Enterprise energy management systems</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      <span className="text-sm text-blue-700">Building management systems</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      <span className="text-sm text-blue-700">Custom applications via API</span>
                    </li>
                  </ul>
                  <div className="mt-4">
                    <Button size="sm" variant="outline" className="border-blue-500 text-blue-700 hover:bg-blue-100">
                      Learn More
                    </Button>
                  </div>
                </div>
                
                <div className="bg-primary/10 p-6 rounded-lg">
                  <h4 className="font-semibold mb-4">Need a Demo?</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    Request a live demonstration of the PQ3K/5K's reporting capabilities to see how it can benefit your power quality monitoring efforts.
                  </p>
                  <Button size="sm" className="w-full" asChild>
                    <Link to="/contact">Schedule Demo</Link>
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </section>
      
      {/* CTA Section */}
      <section className="mt-16 bg-gradient-to-r from-amber-500 to-orange-600 text-white p-8 rounded-lg">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold mb-4">Ready to enhance your power quality monitoring?</h2>
          <p className="text-white/80 mb-6 max-w-2xl mx-auto">
            The Dranetz PQ3K/5K provides comprehensive power quality monitoring capabilities to help you identify and resolve 
            issues before they impact your operations.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Button size="lg" variant="outline" className="bg-white text-orange-600 border-white hover:bg-white/90 hover:text-orange-700" asChild>
              <Link to="/contact/sales">Request a Quote</Link>
            </Button>
            <Button size="lg" variant="ghost" className="text-white border-white hover:bg-white/20" asChild>
              <Link to="/measure/power-quality-monitors">Compare Models</Link>
            </Button>
          </div>
        </div>
      </section>
    </PageLayout>
  );
};

export default PowerQualityMonitorProduct2;