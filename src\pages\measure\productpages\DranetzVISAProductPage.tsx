import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { Link } from "react-router-dom";
import Layout from "@/components/layout/Layout";
import SubpageHeader from "@/components/SubPageHeader";
import ProductCategoryHeader from "@/components/ProductCategoryHeader";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
// import PdfViewer from "@/components/ui/pdf-viewer";

const DranetzVisaProductPage = () => {
  // Define tabs for the ProductCategoryHeader
  const productTabs = [
    { name: "Dranetz Xplorer", path: "/measure/class-a-analyzers/dranetz-xplorer" },
    { name: "Dranetz Guide", path: "/measure/class-a-analyzers/dranetz-guide" },
    { name: "Dranetz Visa", path: "/measure/class-a-analyzers/dranetz-visa" }
  ];

  // Technical specifications
  const specifications = {
    dcDifferentialVoltage: [
      "0-1000Vrms AC/DC, +/-0.1% reading +/-0.05% FS",
      "DC 0.001V to 1000V, & DC 1000mVrms, +/-1% of rdng, range of 10% - 150% of range",
      "Harmonics: 0-63 at 50, 60, and 400 Hz"
    ],
    dcCurrent: [
      "Range (in percentage dependent): AC/DC, 0–10 reading +/-0.02% FS",
      "Harmonics – range probe dependent"
    ],
    frequency: [
      "15-20Hz, 45-65Hz, +/-10mHz (15 sec windows)"
    ],
    physicalVisa: [
      "Size: (10\"w x 8\"h x 2.5\"d) (25.4cm x 20.3cm x 6.35cm)",
      "Weight: 4.2lbs, 1kg",
      "Operating temperature: 0 to 50 deg C (32 to 122 deg F)",
      "Storage temperature: -20 to 55 deg C (-4 to 131 deg F)",
      "Humidity: 10-90% non-condensing",
      "AC Adapter: CPS120508-16SPN (90V-265V)"
    ],
    physicalVisaSP: [
      "Size: (11\"w x 8.5\"h x 2.5\"d) (28cm x 21.6cm x 6.4cm)",
      "Weight: 5.6lbs, 2.5kg",
      "Operating temperature: -10 to 50 deg C (14 to 122 deg F)",
      "Storage temperature: -40 to 60 deg C (-40 to 140 deg F)",
      "Humidity: 10-90% non-condensing"
    ]
  };

  // Features for the 4 category cards
  const categories = [
    {
      title: "Communications",
      features: [
        "Ethernet, USB, Bluetooth + Optional",
        "VNC: No Windows needed direct browser access 2.0 or 3rd party App",
        "HDPQ App for Android OS and Apple iOS including direct instrument dashboard"
      ]
    },
    {
      title: "Safe & Rugged",
      features: [
        "Arc Flash Safety",
        "Rugged, fixed and IP65 approved",
        "Remote turn-on download data by Wi-Fi, direct-printing, measurement screen capture (HDPQ Visa only)",
        "Better, more stable display and are in management (Dranetz HDPQ Visa only)"
      ]
    },
    {
      title: "User Interface",
      features: [
        "7\" Widescreen touch display for easy HDPQ Visa only",
        "Intuitive GUI, that is consistent, has natural shortcuts, and a Dashboard is only available currently on the Dranetz HDPQ Visa"
      ]
    },
    {
      title: "Productivity",
      features: [
        "Mini-reports - Onsite snapshots. NB: No, easily viewable and email distribution, Instantaneous for real-time alarming",
        "Simple display for easy production and analysis (Dranetz HDPQ Visa only)"
      ]
    }
  ];

  return (
    <Layout>
      {/* Main Title Header with added top padding */}
      <AnimatedSection>
        <div className="pt-12">
          <SubpageHeader
            title="Dranetz HDPQ Visa"
            subtitle="Performance power quality analyzer with advanced monitoring capabilities"
          />
        </div>
      </AnimatedSection>

      {/* Product Category SubHeader */}
      <AnimatedSection>
        <ProductCategoryHeader
          tabs={productTabs}
          showCompareButton={true}
        />
      </AnimatedSection>

      {/* Main content */}
      <div className="container max-w-7xl mx-auto px-6 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16 items-center">
          {/* Product Image */}
          <AnimatedSection>
            <img
              src="https://firebasestorage.googleapis.com/v0/b/atandra.firebasestorage.app/o/Atandraimages%2FHDPQ_Visa.png?alt=media&token=7efe6f81-a161-4827-9fcd-b2c1bcf0f243?raw=true"
              alt="Dranetz HDPQ Visa"
              className="w-full h-auto shadow-lg rounded-lg"
            />
          </AnimatedSection>

          {/* Product Description */}
          <AnimatedSection>
            <h2 className="text-3xl font-bold mb-6">
              <span className="text-gray-800">Dranetz</span>
              <span className="text-yellow-500"> VISA</span>
            </h2>
            <p className="text-gray-600 mb-4">
              The Dranetz HDPQ Visa is a high-performance power quality analyzer designed for comprehensive monitoring and analysis with advanced capabilities for detailed investigation of electrical issues.
            </p>
            <p className="text-gray-600 mb-6">
              With high performance PQ & Energy Monitoring capabilities and an intuitive 7-inch widescreen interface, the HDPQ Visa provides exceptional insight into power quality events.
            </p>

            <h3 className="text-xl font-bold mb-4">Key Features</h3>
            <ul className="list-disc pl-5 mb-6 text-gray-600">
              <li>High Performance PQ & Energy Monitoring – 1000Vrms, AC/DC, 512 samples/cycle</li>
              <li>Eight Channels, Voltage & Current</li>
              <li>Advanced DC USB, Ethernet, Wi-Max & SD, USB compliant</li>
              <li>Removable SD, Micro SD - Over 32 GB/SDHC</li>
              <li>Intuitive capabilities – 7-in. Widescreen Xga</li>
              <li>On-board product trigger output</li>
              <li>EN 50160 v3.x</li>
              <li>Class A power Module</li>
            </ul>

            <div className="flex flex-wrap gap-4">
              <Button asChild>
                <Link to="/contact">Request a Quote</Link>
              </Button>
              <Button variant="outline" asChild>
                <a href="/downloads/dranetz-hdpq-visa-datasheet.pdf" target="_blank" rel="noopener noreferrer">
                  Download Brochure
                </a>
              </Button>
            </div>
          </AnimatedSection>
        </div>

        {/* Product Categories */}
        <AnimatedSection>
          <h2 className="text-2xl font-bold mb-8 text-center">Product Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {categories.map((category, index) => (
              <Card key={index} className="shadow-lg">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold mb-4">Dranetz HDPQ Visa - {category.title}</h3>
                  <ul className="space-y-2">
                    {category.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start">
                        <span className="text-yellow-500 mr-2">✓</span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </AnimatedSection>

        {/* Technical Specifications */}
        <AnimatedSection>
          <h2 className="text-2xl font-bold mb-8 text-center mt-16">Technical Specifications</h2>
          <Tabs defaultValue="dcDifferentialVoltage" className="w-full">
            <TabsList className="w-full justify-center mb-8">
              <TabsTrigger value="dcDifferentialVoltage">DC Differential Voltage</TabsTrigger>
              <TabsTrigger value="dcCurrent">DC Current</TabsTrigger>
              <TabsTrigger value="frequency">Frequency</TabsTrigger>
              <TabsTrigger value="physicalVisa">HDPQ Visa Physical</TabsTrigger>
              <TabsTrigger value="physicalVisaSP">HDPQ Visa SP Physical</TabsTrigger>
            </TabsList>

            <TabsContent value="dcDifferentialVoltage">
              <Card className="p-6 shadow-lg">
                <CardContent className="pt-6">
                  <h3 className="text-lg font-bold mb-4">DC Differential Voltage: 15 bit resolution</h3>
                  <ul className="space-y-2">
                    {specifications.dcDifferentialVoltage.map((spec, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-yellow-500 mr-2">✓</span>
                        {spec}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="dcCurrent">
              <Card className="p-6 shadow-lg">
                <CardContent className="pt-6">
                  <h3 className="text-lg font-bold mb-4">DC Current: 512 samples/cycle, 16 bit resolution</h3>
                  <ul className="space-y-2">
                    {specifications.dcCurrent.map((spec, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-yellow-500 mr-2">✓</span>
                        {spec}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="frequency">
              <Card className="p-6 shadow-lg">
                <CardContent className="pt-6">
                  <h3 className="text-lg font-bold mb-4">Frequency: 15 sec windows</h3>
                  <ul className="space-y-2">
                    {specifications.frequency.map((spec, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-yellow-500 mr-2">✓</span>
                        {spec}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="physicalVisa">
              <Card className="p-6 shadow-lg">
                <CardContent className="pt-6">
                  <h3 className="text-lg font-bold mb-4">Dranetz HDPQ Visa Physical Specifications</h3>
                  <ul className="space-y-2">
                    {specifications.physicalVisa.map((spec, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-yellow-500 mr-2">✓</span>
                        {spec}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="physicalVisaSP">
              <Card className="p-6 shadow-lg">
                <CardContent className="pt-6">
                  <h3 className="text-lg font-bold mb-4">Dranetz HDPQ Visa SP (IP65) Physical Specifications</h3>
                  <ul className="space-y-2">
                    {specifications.physicalVisaSP.map((spec, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-yellow-500 mr-2">✓</span>
                        {spec}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </AnimatedSection>

        {/* Applications */}
        <AnimatedSection>
          <h2 className="text-2xl font-bold mb-8 text-center mt-16">Applications</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {[
              {
                title: "Industrial Monitoring",
                description: "Monitor power quality in industrial settings to prevent equipment damage and production downtime."
              },
              {
                title: "Energy Management",
                description: "Track and analyze energy consumption patterns to identify opportunities for efficiency improvements."
              },
              {
                title: "Troubleshooting",
                description: "Identify and diagnose power quality issues in electrical systems that may affect sensitive equipment."
              },
              {
                title: "Compliance Verification",
                description: "Verify compliance with power quality standards and regulations including EN 50160."
              }
            ].map((application, index) => (
              <Card key={index} className="p-6 shadow-lg">
                <h3 className="text-xl font-bold mb-2">{application.title}</h3>
                <p className="text-gray-600">{application.description}</p>
              </Card>
            ))}
          </div>
        </AnimatedSection>

        {/* Call to Action */}
        <AnimatedSection>
          <div className="bg-gray-100 p-8 rounded-lg text-center shadow-lg mt-16">
            <h2 className="text-2xl font-bold mb-4">Need More Information?</h2>
            <p className="text-gray-600 mb-6">
              Contact our power quality experts for a personalized demonstration or quote for the Dranetz HDPQ Visa.
            </p>
            <Button size="lg" asChild>
              <Link to="/contact">Contact Our Experts</Link>
            </Button>
          </div>
        </AnimatedSection>
      </div>
    </Layout>
  );
};

const AnimatedSection = ({ children }) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 50 }}
      animate={inView ? { opacity: 1, y: 0 } : {}}
      transition={{ duration: 0.6 }}
    >
      {children}
    </motion.div>
  );
};

export default DranetzVisaProductPage;