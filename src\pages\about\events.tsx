import React, { useState, useEffect, useRef } from 'react';
import PageLayout from '@/components/layout/PageLayout';

// Type definitions
interface EventImage {
  url: string;
}

interface Event {
  id: number;
  title: string;
  description: string;
  images: EventImage[];
  category: string;
}

/**
 * Events Page Component - Completely rebuilt for stability
 */
const EventsPage: React.FC = () => {
  // State for gallery modal
  const [activeEventId, setActiveEventId] = useState<number | null>(null);
  const [isGalleryOpen, setIsGalleryOpen] = useState<boolean>(false);
  const [currentImageIndex, setCurrentImageIndex] = useState<number>(0);

  // State for carousel
  const [currentSlide, setCurrentSlide] = useState<number>(0);
  const carouselRef = useRef<HTMLDivElement>(null);

  // Carousel images - group photos from each event
  const carouselImages = [
    { url: "/Elecrama/WhatsApp Image 2025-04-05 at 2.29.59 PM.jpeg" }, // Group photo from Elecrama
    { url: "/EPS expo/WhatsApp Image 2025-04-18 at 5.17.02 PM.jpeg" }, // Group photo from EPS expo
    { url: "/IMS Expo/WhatsApp Image 2025-04-18 at 5.50.18 PM.jpeg" }, // Group photo from IMS Expo
    { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.00 PM.jpeg" }, // Group photo from IMTOF
    { url: "/DUM Expo/WhatsApp Image 2025-04-18 at 5.53.57 PM.jpeg" }, // Group photo from DUM Expo
    { url: "/Toplast/WhatsApp Image 2025-04-18 at 5.46.52 PM.jpeg" }, // Group photo from Toplast
    { url: "/Renewable energy/WhatsApp Image 2025-04-18 at 5.23.49 PM.jpeg" }, // Group photo from Renewable energy
    { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.14.56 PM.jpeg" } // Group photo from Printexpo
  ];

  // Auto-advance carousel
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % carouselImages.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [carouselImages.length]);

  // Carousel navigation
  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % carouselImages.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev === 0 ? carouselImages.length - 1 : prev - 1));
  };

  // Sample events data
  const events: Event[] = [
    {
      id: 1,
      title: "Elecrama",
      description: "A showcase of the latest innovations in electrical equipment and technology, bringing together industry leaders and innovators.",
      images: [
        { url: "/Elecrama/WhatsApp Image 2025-04-05 at 2.29.34 PM.jpeg" },
        { url: "/Elecrama/WhatsApp Image 2025-04-05 at 2.29.36 PM.jpeg" },
        { url: "/Elecrama/WhatsApp Image 2025-04-05 at 2.29.37 PM.jpeg" },
        { url: "/Elecrama/WhatsApp Image 2025-04-05 at 2.29.38 PM (1).jpeg" },
        { url: "/Elecrama/WhatsApp Image 2025-04-05 at 2.29.38 PM.jpeg" },
        { url: "/Elecrama/WhatsApp Image 2025-04-05 at 2.29.39 PM.jpeg" },
        { url: "/Elecrama/WhatsApp Image 2025-04-05 at 2.29.40 PM (1).jpeg" },
        { url: "/Elecrama/WhatsApp Image 2025-04-05 at 2.29.40 PM.jpeg" },
        { url: "/Elecrama/WhatsApp Image 2025-04-05 at 2.29.41 PM.jpeg" },
        { url: "/Elecrama/WhatsApp Image 2025-04-05 at 2.29.42 PM (1).jpeg" },
        { url: "/Elecrama/WhatsApp Image 2025-04-05 at 2.29.42 PM.jpeg" },
        { url: "/Elecrama/WhatsApp Image 2025-04-05 at 2.29.43 PM.jpeg" },
        { url: "/Elecrama/WhatsApp Image 2025-04-05 at 2.29.44 PM.jpeg" },
        { url: "/Elecrama/WhatsApp Image 2025-04-05 at 2.29.45 PM.jpeg" },
        { url: "/Elecrama/WhatsApp Image 2025-04-05 at 2.29.46 PM.jpeg" },
        { url: "/Elecrama/WhatsApp Image 2025-04-05 at 2.29.47 PM.jpeg" },
        { url: "/Elecrama/WhatsApp Image 2025-04-05 at 2.29.48 PM.jpeg" },
        { url: "/Elecrama/WhatsApp Image 2025-04-05 at 2.29.49 PM.jpeg" },
        { url: "/Elecrama/WhatsApp Image 2025-04-05 at 2.29.50 PM.jpeg" }
      ],
      category: "Elecrama"
    },
    {
      id: 2,
      title: "EPS Expo",
      description: "Showcasing the latest developments in energy power systems with industry-leading innovations and technologies.",
      images: [
        { url: "/EPS expo/WhatsApp Image 2025-04-18 at 5.16.55 PM.jpeg" },
        { url: "/EPS expo/WhatsApp Image 2025-04-18 at 5.16.56 PM.jpeg" },
        { url: "/EPS expo/WhatsApp Image 2025-04-18 at 5.16.57 PM (1).jpeg" },
        { url: "/EPS expo/WhatsApp Image 2025-04-18 at 5.16.57 PM.jpeg" },
        { url: "/EPS expo/WhatsApp Image 2025-04-18 at 5.16.58 PM.jpeg" },
        { url: "/EPS expo/WhatsApp Image 2025-04-18 at 5.16.59 PM (1).jpeg" },
        { url: "/EPS expo/WhatsApp Image 2025-04-18 at 5.16.59 PM.jpeg" },
        { url: "/EPS expo/WhatsApp Image 2025-04-18 at 5.17.00 PM (1).jpeg" },
        { url: "/EPS expo/WhatsApp Image 2025-04-18 at 5.17.00 PM.jpeg" },
        { url: "/EPS expo/WhatsApp Image 2025-04-18 at 5.17.01 PM (1).jpeg" },
        { url: "/EPS expo/WhatsApp Image 2025-04-18 at 5.17.01 PM.jpeg" },
        { url: "/EPS expo/WhatsApp Image 2025-04-18 at 5.17.02 PM (1).jpeg" },
        { url: "/EPS expo/WhatsApp Image 2025-04-18 at 5.17.02 PM (2).jpeg" },
        { url: "/EPS expo/WhatsApp Image 2025-04-18 at 5.17.02 PM.jpeg" }
      ],
      category: "EPS Expo"
    },
    {
      id: 3,
      title: "IMS expo",
      description: "Showcasing innovative measurement solutions and instrumentation technology for various industries and applications.",
      images: [
        { url: "/IMS Expo/WhatsApp Image 2025-04-18 at 5.50.13 PM.jpeg" },
        { url: "/IMS Expo/WhatsApp Image 2025-04-18 at 5.50.14 PM.jpeg" },
        { url: "/IMS Expo/WhatsApp Image 2025-04-18 at 5.50.15 PM (1).jpeg" },
        { url: "/IMS Expo/WhatsApp Image 2025-04-18 at 5.50.15 PM.jpeg" },
        { url: "/IMS Expo/WhatsApp Image 2025-04-18 at 5.50.16 PM.jpeg" },
        { url: "/IMS Expo/WhatsApp Image 2025-04-18 at 5.50.17 PM (1).jpeg" },
        { url: "/IMS Expo/WhatsApp Image 2025-04-18 at 5.50.17 PM.jpeg" },
        { url: "/IMS Expo/WhatsApp Image 2025-04-18 at 5.50.18 PM (1).jpeg" },
        { url: "/IMS Expo/WhatsApp Image 2025-04-18 at 5.50.18 PM (2).jpeg" },
        { url: "/IMS Expo/WhatsApp Image 2025-04-18 at 5.50.18 PM.jpeg" },
        { url: "/IMS Expo/WhatsApp Image 2025-04-18 at 5.50.19 PM (1).jpeg" },
        { url: "/IMS Expo/WhatsApp Image 2025-04-18 at 5.50.19 PM.jpeg" },
        { url: "/IMS Expo/WhatsApp Image 2025-04-18 at 5.50.20 PM.jpeg" }
      ],
      category: "IMS Expo"
    },
    {
      id: 4,
      title: "IMTOF",
      description: "A premier trade fair for industrial measurement technology, offering insights into the latest innovations for precision measurement.",
      images: [
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.28.47 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.28.47 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.28.48 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.28.49 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.28.49 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.28.50 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.28.50 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.28.51 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.28.51 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.28.52 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.28.52 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.28.53 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.28.53 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.28.54 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.28.54 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.28.55 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.28.56 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.28.56 PM (2).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.28.56 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.28.57 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.28.58 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.28.59 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.28.59 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.00 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.00 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.01 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.01 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.02 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.03 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.03 PM (2).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.03 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.04 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.04 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.05 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.05 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.06 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.06 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.07 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.07 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.08 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.08 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.09 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.09 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.10 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.10 PM (2).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.10 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.12 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.12 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.13 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.13 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.14 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.14 PM (2).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.14 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.16 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.17 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.17 PM (2).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.17 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.19 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.22 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.23 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.23 PM (2).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.23 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.24 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.24 PM (2).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.24 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.25 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.25 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.26 PM (1).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.26 PM (2).jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.26 PM.jpeg" },
        { url: "/IMTOF/WhatsApp Image 2025-04-18 at 5.29.27 PM.jpeg" }
      ],
      category: "IMTOF"
    },
    {
      id: 5,
      title: "DUM Expo",
      description: "Visitors witnessing cutting-edge Power Conditioning,T&M,Energy Managemenet,and industry 4.0 Solutions at DUM Expo Exhibition.",
      images: [
        { url: "/DUM Expo/WhatsApp Image 2025-04-18 at 5.53.55 PM.jpeg" },
        { url: "/DUM Expo/WhatsApp Image 2025-04-18 at 5.53.56 PM.jpeg" },
        { url: "/DUM Expo/WhatsApp Image 2025-04-18 at 5.53.57 PM (1).jpeg" },
        { url: "/DUM Expo/WhatsApp Image 2025-04-18 at 5.53.57 PM.jpeg" }
      ],
      category: "DUM Expo"
    },
    {
      id: 6,
      title: "Toplast",
      description: "A showcase of the latest innovations in electrical equipment and technology, bringing together industry leaders and innovators.",
      images: [
        { url: "/Toplast/WhatsApp Image 2025-04-18 at 5.46.30 PM.jpeg" },
        { url: "/Toplast/WhatsApp Image 2025-04-18 at 5.46.32 PM.jpeg" },
        { url: "/Toplast/WhatsApp Image 2025-04-18 at 5.46.35 PM.jpeg" },
        { url: "/Toplast/WhatsApp Image 2025-04-18 at 5.46.36 PM (1).jpeg" },
        { url: "/Toplast/WhatsApp Image 2025-04-18 at 5.46.36 PM.jpeg" },
        { url: "/Toplast/WhatsApp Image 2025-04-18 at 5.46.39 PM.jpeg" },
        { url: "/Toplast/WhatsApp Image 2025-04-18 at 5.46.41 PM.jpeg" },
        { url: "/Toplast/WhatsApp Image 2025-04-18 at 5.46.43 PM.jpeg" },
        { url: "/Toplast/WhatsApp Image 2025-04-18 at 5.46.46 PM (1).jpeg" },
        { url: "/Toplast/WhatsApp Image 2025-04-18 at 5.46.46 PM.jpeg" },
        { url: "/Toplast/WhatsApp Image 2025-04-18 at 5.46.48 PM.jpeg" },
        { url: "/Toplast/WhatsApp Image 2025-04-18 at 5.46.49 PM.jpeg" },
        { url: "/Toplast/WhatsApp Image 2025-04-18 at 5.46.50 PM (1).jpeg" },
        { url: "/Toplast/WhatsApp Image 2025-04-18 at 5.46.50 PM.jpeg" },
        { url: "/Toplast/WhatsApp Image 2025-04-18 at 5.46.51 PM.jpeg" },
        { url: "/Toplast/WhatsApp Image 2025-04-18 at 5.46.52 PM (1).jpeg" },
        { url: "/Toplast/WhatsApp Image 2025-04-18 at 5.46.52 PM.jpeg" },
        { url: "/Toplast/WhatsApp Image 2025-04-18 at 5.46.53 PM (1).jpeg" },
        { url: "/Toplast/WhatsApp Image 2025-04-18 at 5.46.53 PM.jpeg" }
      ],
      category: "showcase"
    },
    {
      id: 7,
      title: "Renewable Energy Summit",
      description: "Exploring sustainable energy solutions and green technologies for a better future with industry experts and thought leaders.",
      images: [
        { url: "/Renewable energy/WhatsApp Image 2025-04-18 at 5.21.00 PM.jpeg" },
        { url: "/Renewable energy/WhatsApp Image 2025-04-18 at 5.22.35 PM.jpeg" },
        { url: "/Renewable energy/WhatsApp Image 2025-04-18 at 5.22.36 PM (1).jpeg" },
        { url: "/Renewable energy/WhatsApp Image 2025-04-18 at 5.22.36 PM (2).jpeg" },
        { url: "/Renewable energy/WhatsApp Image 2025-04-18 at 5.22.36 PM.jpeg" },
        { url: "/Renewable energy/WhatsApp Image 2025-04-18 at 5.22.37 PM.jpeg" },
        { url: "/Renewable energy/WhatsApp Image 2025-04-18 at 5.22.59 PM (1).jpeg" },
        { url: "/Renewable energy/WhatsApp Image 2025-04-18 at 5.22.59 PM (2).jpeg" },
        { url: "/Renewable energy/WhatsApp Image 2025-04-18 at 5.22.59 PM.jpeg" },
        { url: "/Renewable energy/WhatsApp Image 2025-04-18 at 5.23.00 PM (1).jpeg" },
        { url: "/Renewable energy/WhatsApp Image 2025-04-18 at 5.23.00 PM.jpeg" },
        { url: "/Renewable energy/WhatsApp Image 2025-04-18 at 5.23.01 PM (1).jpeg" },
        { url: "/Renewable energy/WhatsApp Image 2025-04-18 at 5.23.01 PM.jpeg" },
        { url: "/Renewable energy/WhatsApp Image 2025-04-18 at 5.23.02 PM (1).jpeg" },
        { url: "/Renewable energy/WhatsApp Image 2025-04-18 at 5.23.02 PM (2).jpeg" },
        { url: "/Renewable energy/WhatsApp Image 2025-04-18 at 5.23.02 PM.jpeg" },
        { url: "/Renewable energy/WhatsApp Image 2025-04-18 at 5.23.03 PM.jpeg" },
        { url: "/Renewable energy/WhatsApp Image 2025-04-18 at 5.23.48 PM (1).jpeg" },
        { url: "/Renewable energy/WhatsApp Image 2025-04-18 at 5.23.48 PM.jpeg" },
        { url: "/Renewable energy/WhatsApp Image 2025-04-18 at 5.23.49 PM (1).jpeg" },
        { url: "/Renewable energy/WhatsApp Image 2025-04-18 at 5.23.49 PM (2).jpeg" },
        { url: "/Renewable energy/WhatsApp Image 2025-04-18 at 5.23.49 PM.jpeg" },
        { url: "/Renewable energy/WhatsApp Image 2025-04-18 at 5.23.50 PM.jpeg" }
      ],
      category: "education"
    },
    {
      id: 8,
      title: "PrintExpo Conference",
      description: "The premier event for printing technology innovations, featuring demonstrations of cutting-edge equipment and networking opportunities.",
      images: [
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.14.42 PM.jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.14.44 PM.jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.14.48 PM.jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.14.51 PM (1).jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.14.51 PM.jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.14.52 PM.jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.14.53 PM (1).jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.14.53 PM.jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.14.54 PM (1).jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.14.54 PM.jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.14.55 PM (1).jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.14.55 PM (2).jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.14.55 PM.jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.14.56 PM (1).jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.14.56 PM (2).jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.14.56 PM.jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.14.57 PM (1).jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.14.57 PM.jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.14.58 PM (1).jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.14.58 PM.jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.14.59 PM (1).jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.14.59 PM (2).jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.14.59 PM.jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.15.00 PM (1).jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.15.00 PM (2).jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.15.00 PM.jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.15.01 PM (1).jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.15.01 PM (2).jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.15.01 PM.jpeg" },
        { url: "/Printexpo/WhatsApp Image 2025-04-05 at 1.15.02 PM.jpeg" }
      ],
      category: "promotion"
    }
  ];

  // Gallery handlers
  const handleOpenGallery = (eventId: number) => {
    setActiveEventId(eventId);
    setCurrentImageIndex(0);
    setIsGalleryOpen(true);
    document.body.style.overflow = 'hidden'; // Prevent scrolling when modal is open
  };

  const handleCloseGallery = () => {
    setIsGalleryOpen(false);
    document.body.style.overflow = 'auto'; // Restore scrolling
  };

  // Find the active event
  const activeEvent = events.find(event => event.id === activeEventId);

  // Gallery navigation
  const nextImage = () => {
    if (!activeEvent) return;
    setCurrentImageIndex((prev) =>
      prev === activeEvent.images.length - 1 ? 0 : prev + 1
    );
  };

  const prevImage = () => {
    if (!activeEvent) return;
    setCurrentImageIndex((prev) =>
      prev === 0 ? activeEvent.images.length - 1 : prev - 1
    );
  };

  const goToImage = (index: number) => {
    setCurrentImageIndex(index);
  };

  // Handle key presses for gallery navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isGalleryOpen) return;

      if (e.key === 'ArrowRight') nextImage();
      else if (e.key === 'ArrowLeft') prevImage();
      else if (e.key === 'Escape') handleCloseGallery();
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isGalleryOpen]);

  // Compact Event card component with Open Sans font
  const EventCard = ({ event, isEven }: { event: Event, isEven: boolean }) => {
    return (
      <div className={`flex flex-col ${isEven ? 'md:flex-row-reverse' : 'md:flex-row'} group w-full gap-2 sm:gap-3 md:gap-4 lg:gap-6`} style={{ fontFamily: '"Open Sans", sans-serif' }}>
        {/* Compact Event image */}
        <div className="w-full md:w-64 lg:w-72 xl:w-80 h-32 sm:h-40 md:h-48 lg:h-56 flex-shrink-0 rounded-lg overflow-hidden relative shadow-lg group-hover:shadow-xl transition-all duration-300">
          <img
            src={event.images[0].url}
            alt={`${event.title} event image`}
            className="w-full h-full object-cover object-center group-hover:scale-105 transition-transform duration-500"
            loading="lazy"
          />
        </div>

        {/* Compact Event details */}
        <div className={`p-2 sm:p-3 md:p-4 lg:p-6 flex-grow flex flex-col ${isEven ? 'md:pr-6 lg:pr-8 md:items-end md:text-right' : 'md:pl-6 lg:pl-8'} justify-center w-full`}>
          <h3 className="text-base sm:text-lg md:text-xl lg:text-2xl xl:text-3xl font-bold mb-1 sm:mb-2 md:mb-3 lg:mb-4 text-gray-800 leading-tight" style={{ fontFamily: '"Open Sans", sans-serif' }}>
            {event.title}
          </h3>

          <p className="text-gray-600 mb-2 sm:mb-3 md:mb-4 lg:mb-6 text-xs sm:text-sm md:text-base lg:text-lg leading-relaxed text-justify" style={{ fontFamily: '"Open Sans", sans-serif', lineHeight: '1.5' }}>
            {event.description}
          </p>

          <div className={`${isEven ? 'md:flex md:justify-end' : ''}`}>
            <button
              onClick={() => handleOpenGallery(event.id)}
              className={`px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 md:py-2.5 lg:py-3 rounded-lg transition-all duration-300 text-xs sm:text-sm md:text-base lg:text-lg font-medium shadow-md hover:shadow-lg transform hover:scale-105 w-full sm:w-auto min-h-[32px] sm:min-h-[36px] md:min-h-[40px] lg:min-h-[44px] ${
                isEven
                  ? 'bg-gradient-to-r from-blue-600 to-green-600 text-white hover:shadow-blue-500/20'
                  : 'bg-gradient-to-r from-green-600 to-yellow-500 text-white hover:shadow-green-500/20'
              }`}
              style={{ fontFamily: '"Open Sans", sans-serif' }}
            >
              VIEW GALLERY
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Compact Gallery modal component with Open Sans font
  const GalleryModal = () => {
    if (!activeEvent || !isGalleryOpen) return null;

    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center p-1 sm:p-2 md:p-3" style={{ fontFamily: '"Open Sans", sans-serif' }}>
        {/* Compact Backdrop overlay */}
        <div
          className="absolute inset-0 bg-black bg-opacity-70 backdrop-blur-sm"
          onClick={handleCloseGallery}
        ></div>

        {/* Compact Modal content */}
        <div className="relative bg-white rounded-lg p-2 sm:p-3 md:p-4 lg:p-6 w-full max-w-[98vw] sm:max-w-4xl md:max-w-5xl lg:max-w-6xl max-h-[98vh] sm:max-h-[95vh] overflow-hidden flex flex-col">
          {/* Compact Close button */}
          <button
            className="absolute top-1 right-1 sm:top-2 sm:right-2 md:top-3 md:right-3 text-gray-500 hover:text-gray-800 p-1 rounded-full hover:bg-gray-100 transition-all duration-200 z-10"
            onClick={handleCloseGallery}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 sm:h-4 sm:w-4 md:h-5 md:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          {/* Compact Title */}
          <div className="mb-1 sm:mb-2 md:mb-3 border-b border-gray-200 pb-1 sm:pb-2 md:pb-3">
            <h3 className="text-sm sm:text-base md:text-lg lg:text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-green-600 to-yellow-500 leading-tight" style={{ fontFamily: '"Open Sans", sans-serif' }}>
              {activeEvent.title} - Gallery
            </h3>
          </div>

          {/* Enhanced Gallery content for better image visibility */}
          <div className="w-full h-[60vh] sm:h-[65vh] md:h-[70vh] lg:h-[75vh] overflow-y-auto">
            <div className="flex flex-col items-center w-full">
              {/* Enhanced Main image for full visibility */}
              <div className="relative mb-2 sm:mb-3 md:mb-4 w-full max-w-full sm:max-w-4xl md:max-w-5xl lg:max-w-6xl mx-auto">
                <img
                  src={activeEvent.images[currentImageIndex].url}
                  alt={`${activeEvent.title} - Image ${currentImageIndex + 1}`}
                  className="w-full h-auto object-contain rounded-lg shadow-md max-h-[40vh] sm:max-h-[45vh] md:max-h-[50vh] lg:max-h-[55vh] bg-gray-50"
                  loading="lazy"
                />

                {/* Compact Image caption */}
                <div className="mt-1 text-center">
                  <p className="text-gray-700 text-xs sm:text-sm md:text-base" style={{ fontFamily: '"Open Sans", sans-serif' }}>
                    {activeEvent.title}
                  </p>
                </div>

                {/* Compact Navigation controls */}
                <div className="absolute top-1/2 left-0 right-0 transform -translate-y-1/2 flex justify-between px-1 sm:px-2 md:px-3">
                  <button
                    onClick={prevImage}
                    className="bg-blue-500/90 hover:bg-blue-600 text-white p-1 sm:p-1.5 md:p-2 rounded-full shadow-md transition-all duration-300 hover:scale-110 min-w-[24px] min-h-[24px] sm:min-w-[28px] sm:min-h-[28px] md:min-w-[32px] md:min-h-[32px]"
                    aria-label="Previous image"
                  >
                    <svg className="w-2.5 h-2.5 sm:w-3 sm:h-3 md:w-4 md:h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M15 19l-7-7 7-7" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </button>
                  <button
                    onClick={nextImage}
                    className="bg-green-500/90 hover:bg-green-600 text-white p-1 sm:p-1.5 md:p-2 rounded-full shadow-md transition-all duration-300 hover:scale-110 min-w-[24px] min-h-[24px] sm:min-w-[28px] sm:min-h-[28px] md:min-w-[32px] md:min-h-[32px]"
                    aria-label="Next image"
                  >
                    <svg className="w-2.5 h-2.5 sm:w-3 sm:h-3 md:w-4 md:h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M9 5l7 7-7 7" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Compact Image counter */}
              <div className="mb-1 sm:mb-2 md:mb-3 text-center">
                <span className="bg-gradient-to-r from-yellow-400 to-yellow-500 px-2 sm:px-3 py-0.5 sm:py-1 rounded-full text-xs sm:text-sm font-medium text-white shadow-sm" style={{ fontFamily: '"Open Sans", sans-serif' }}>
                  {currentImageIndex + 1} of {activeEvent.images.length}
                </span>
              </div>

              {/* Compact Thumbnails */}
              <div className="flex justify-center flex-wrap gap-1 sm:gap-1.5 md:gap-2 w-full max-w-full sm:max-w-3xl md:max-w-4xl mx-auto px-1 sm:px-2">
                {activeEvent.images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => goToImage(index)}
                    className={`cursor-pointer transition-all rounded-md overflow-hidden hover:scale-105 ${
                      index === currentImageIndex
                        ? index % 3 === 0
                          ? "ring-2 ring-blue-500 shadow-md"
                          : index % 3 === 1
                            ? "ring-2 ring-green-500 shadow-md"
                            : "ring-2 ring-yellow-500 shadow-md"
                        : "ring-1 ring-gray-200 hover:ring-blue-300"
                    }`}
                    aria-label={`View image ${index + 1}`}
                  >
                    <img
                      src={image.url}
                      alt={`${activeEvent.title} thumbnail ${index + 1}`}
                      className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 lg:w-14 lg:h-14 object-cover"
                      loading="lazy"
                    />
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <PageLayout
      title="Our Events"
      subtitle="Join us at our upcoming events to connect, learn, and grow with industry professionals."
      category="about"
    >
      {/* Compact page design with Open Sans font */}
      <div className="w-full min-h-screen bg-gradient-to-b from-blue-50 via-green-50 to-yellow-50 py-0" style={{ fontFamily: '"Open Sans", sans-serif' }}>
        {/* Compact Title Above Carousel - No side spacing */}
        <div className="w-full max-w-none px-0 mb-2 sm:mb-3 md:mb-4 lg:mb-6 text-center">
          <h2 className="inline-block text-base sm:text-lg md:text-xl lg:text-2xl xl:text-3xl font-bold relative leading-tight" style={{ fontFamily: '"Open Sans", sans-serif' }}>
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-green-600 to-yellow-500">Capturing Moments That Matter</span>
            <div className="absolute -bottom-1 sm:-bottom-2 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 via-green-500 to-yellow-500 rounded-full transform scale-x-75"></div>
          </h2>
          <p className="mt-1 sm:mt-2 md:mt-3 text-gray-700 text-xs sm:text-sm md:text-base lg:text-lg max-w-full sm:max-w-2xl md:max-w-3xl mx-auto text-center leading-relaxed" style={{ fontFamily: '"Open Sans", sans-serif', lineHeight: '1.5' }}>
            Discover the highlights from our most impactful gatherings and preview upcoming opportunities to connect.
          </p>
        </div>

        {/* Compact Hero Carousel */}
        <div className="mb-4 sm:mb-6 md:mb-8 lg:mb-10 relative w-full">
          {/* Smaller background decorative elements */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            <div className="absolute -top-5 sm:-top-8 -left-5 sm:-left-8 w-20 h-20 sm:w-30 sm:h-30 md:w-40 md:h-40 rounded-full bg-blue-500/10 blur-2xl"></div>
            <div className="absolute top-1/4 -right-5 sm:-right-8 w-24 h-24 sm:w-36 sm:h-36 md:w-48 md:h-48 rounded-full bg-green-500/10 blur-2xl"></div>
            <div className="absolute bottom-0 left-1/4 w-20 h-20 sm:w-30 sm:h-30 md:w-40 md:h-40 rounded-full bg-yellow-500/10 blur-2xl"></div>
          </div>

          <div className="w-full max-w-none px-0">
            <div className="relative h-[250px] sm:h-[300px] md:h-[350px] lg:h-[450px] xl:h-[500px] overflow-hidden w-full">
              {/* Full width image with complete visibility and no spacing */}
              <img
                src={carouselImages[currentSlide].url}
                alt={`Event carousel image ${currentSlide + 1}`}
                className="w-full h-full object-contain object-center bg-white"
                loading="lazy"
              />

              {/* Navigation buttons at edges - No spacing */}
              <button
                onClick={prevSlide}
                className="absolute left-0 top-1/2 transform -translate-y-1/2 bg-white shadow-md text-blue-600 p-1 sm:p-1.5 md:p-2 rounded-full hover:bg-blue-50 z-10 transition-all duration-300 hover:scale-110 min-w-[24px] min-h-[24px] sm:min-w-[28px] sm:min-h-[28px] md:min-w-[32px] md:min-h-[32px]"
                aria-label="Previous slide"
              >
                <svg className="w-2.5 h-2.5 sm:w-3 sm:h-3 md:w-4 md:h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M15 19l-7-7 7-7" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              </button>
              <button
                onClick={nextSlide}
                className="absolute right-0 top-1/2 transform -translate-y-1/2 bg-white shadow-md text-green-600 p-1 sm:p-1.5 md:p-2 rounded-full hover:bg-green-50 z-10 transition-all duration-300 hover:scale-110 min-w-[24px] min-h-[24px] sm:min-w-[28px] sm:min-h-[28px] md:min-w-[32px] md:min-h-[32px]"
                aria-label="Next slide"
              >
                <svg className="w-2.5 h-2.5 sm:w-3 sm:h-3 md:w-4 md:h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M9 5l7 7-7 7" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              </button>
            </div>

            {/* Compact indicators */}
            <div className="flex justify-center mt-2 sm:mt-3 md:mt-4">
              <div className="flex items-center space-x-1 sm:space-x-1.5 md:space-x-2 bg-white px-2 sm:px-3 md:px-4 py-1.5 sm:py-2 md:py-2.5 rounded-full shadow-md">
                {carouselImages.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => goToSlide(index)}
                    className={`w-1.5 h-1.5 sm:w-2 sm:h-2 md:w-2.5 md:h-2.5 rounded-full transition-all duration-300 ${
                      index === currentSlide
                        ? index % 3 === 0 ? 'bg-blue-500' : index % 3 === 1 ? 'bg-green-500' : 'bg-yellow-500'
                        : 'bg-gray-200 hover:bg-gray-300'
                    }`}
                    aria-label={`Go to slide ${index + 1}`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Compact Page Header */}
        <div className="w-full max-w-none px-2 sm:px-3 md:px-4 lg:px-6 mb-4 sm:mb-6 md:mb-8 lg:mb-10">
          <div className="text-center w-full max-w-none sm:max-w-3xl md:max-w-4xl mx-auto">
            <div className="inline-block mb-1 sm:mb-2 md:mb-3">
              <h1 className="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl font-bold text-blue-600 mb-1 relative leading-tight" style={{ fontFamily: '"Open Sans", sans-serif' }}>
                Latest Events
                <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 via-green-500 to-yellow-500 rounded-full"></div>
              </h1>
            </div>
            <p className="text-gray-700 text-xs sm:text-sm md:text-base lg:text-lg leading-relaxed text-center" style={{ fontFamily: '"Open Sans", sans-serif', lineHeight: '1.5' }}>
              Explore our past and upcoming events designed to connect, inspire, and transform your professional journey.
            </p>
          </div>
        </div>

        {/* Compact Events List */}
        <div className="w-full max-w-none px-2 sm:px-3 md:px-4 lg:px-6">
          <div className="flex flex-col space-y-4 sm:space-y-6 md:space-y-8 lg:space-y-10">
            {events.map((event, index) => (
              <div key={event.id} className="w-full max-w-none sm:max-w-5xl md:max-w-6xl mx-auto">
                <EventCard event={event} isEven={index % 2 === 1} />
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Gallery Modal */}
      <GalleryModal />
    </PageLayout>
  );
};

export default EventsPage;
