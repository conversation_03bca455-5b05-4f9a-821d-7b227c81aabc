import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import PageLayout from "@/components/layout/PageLayout";
import { motion, AnimatePresence } from 'framer-motion';
// import PdfViewer from "@/components/ui/pdf-viewer";

interface Specification {
  parameter: string;
  values: string[] | { [key: string]: string };
}

interface Product {
  id: string;
  model: string;
  image: string;
  pdf?: string;
  features: string[];
  specifications: Specification[];
  hasDualColumns?: boolean;
  dualColumnHeaders?: string[];
}

const PowerHarmonicClampsPage: React.FC = () => {
  const location = useLocation();
  const [selectedProduct, setSelectedProduct] = useState<string>('model1');
  const [isSpecsVisible, setIsSpecsVisible] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<'features' | 'specs'>('features');
  
  // Extract model parameter from URL if present
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const modelParam = params.get('model');
    if (modelParam) {
      setSelectedProduct(modelParam);
    }
    
    // Animation timing for specs section
    setTimeout(() => {
      setIsSpecsVisible(true);
    }, 500);
    
    // Add the float animation styles
    const styleSheet = document.createElement("style");
    styleSheet.textContent = `
      @keyframes float {
        0% { transform: translateY(0px) translateX(0px); }
        25% { transform: translateY(-10px) translateX(5px); }
        50% { transform: translateY(0px) translateX(10px); }
        75% { transform: translateY(10px) translateX(5px); }
        100% { transform: translateY(0px) translateX(0px); }
      }
      .animate-float {
        animation: float 10s ease-in-out infinite;
      }
      .bg-grid-pattern {
        background-image: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
        background-size: 20px 20px;
      }
    `;
    document.head.appendChild(styleSheet);
    
    return () => {
      document.head.removeChild(styleSheet);
    };
  }, [location]);

  const products: Product[] = [
    {
      id: 'model1',
      model: 'F403',
      image: '/images/power-clamp-1.png',
      pdf: '/brochures/F403-brochure.pdf',
      features: [
        'Models – F403',
        'True RMS reading on AC and AC+DC',
        '0.01A resolution',
        'AC and DC voltage up to 1,000 V',
        'Current: 1,000 A AC / 1,500 A DC',
        'Maximum Jaw opening: ø 48 mm',
        '10,000 Count large display',
        'Power and Power factor measurement',
        'Inrush Current',
        'Continuity Beeper',
        'Temperature',
        'Display Hold, Peak Hold (10ms)',
        'Min/Max function',
        'Frequency Counter',
        'Safety standard CAT IV 1000V'
      ],
      specifications: [
        { parameter: 'AC Current', values: ['0.25 to 1000 A (1500A peak)'] },
        { parameter: 'DC Current', values: ['0.25 to 1500 A'] },
        { parameter: 'DC Voltage', values: ['0.15 to 1,000 V'] },
        { parameter: 'AC Voltage', values: ['0.15 to 1,000 V'] },
        { parameter: 'Frequency', values: ['Current: 5.0 Hz to 2,000 Hz\nVoltage: 5.0 Hz to 20.00 kHz'] },
        { parameter: 'Resistance', values: ['0.1 Ω to 99.99 kΩ'] },
        { parameter: 'Temperature', values: ['-60.0 to +1,000.0 °C'] },
        { parameter: 'Accuracy %', values: ['1', '1', '1', '1', '0.4', '1', '1'] }
      ]
    },
    {
      id: 'model2',
      model: 'F205/F405',
      image: '/images/power-clamp-2.png',
      pdf: '/brochures/F205-F405-brochure.pdf',
      features: [
        'Models – F205 F405',
        'True RMS reading on AC and AC+DC',
        '0.01A resolution',
        'AC and DC voltage up to 1,000 V',
        '10,000 Count large display',
        'Power and Power factor measurement',
        'Inrush Current',
        'Resistance & Diode function',
        'V & I THD',
        'ΔREL',
        'Min/Max/PeakMin/Max function',
        'Frequency Counter',
        'Safety standard CAT IV 1000V'
      ],
      hasDualColumns: true,
      dualColumnHeaders: ['F205', 'F405'],
      specifications: [
        { parameter: 'Clamping dia', values: { 'F205': '34 mm', 'F405': '48 mm' } },
        { parameter: 'AC Current', values: { 'F205': '0.25 to 600 A', 'F405': '0.25 to 1,000 A' } },
        { parameter: 'DC Current', values: { 'F205': '0.25 to 900 A', 'F405': '0.25 to 1500 A' } },
        { parameter: 'DC Voltage', values: { 'F205': '0.15 to 1,000 V', 'F405': '0.15 to 1,000 V' } },
        { parameter: 'AC Voltage', values: { 'F205': '0.15 to 1,000 V', 'F405': '0.15 to 1,000 V' } },
        { parameter: 'Resistance', values: { 'F205': '0.1 Ω to 59.99 kΩ', 'F405': '0.1 Ω to 99.99 kΩ' } },
        { parameter: 'Harmonic', values: { 'F205': '–', 'F405': '25th' } },
        { parameter: 'KW/Kvar/kVA', values: { 'F205': '1 to 600', 'F405': '1 to 1000' } }
      ]
    },
    {
      id: 'model3',
      model: 'F407/F607',
      image: '/images/power-clamp-3.png',
      pdf: '/brochures/F407-F607-brochure.pdf',
      features: [
        'Models – F407 F607',
        'True RMS reading on AC and AC+DC',
        '0.01A resolution',
        'AC and DC voltage up to 1,000 V',
        '10,000 Count large display',
        'Power and Power factor measurement',
        'Inrush Current',
        'Resistance & Diode function',
        'V & I THD',
        'ΔREL',
        'Min/Max/PeakMin/Max function',
        'Bluetooth Communication',
        'Frequency Counter',
        'Safety standard CAT IV 1000V'
      ],
      hasDualColumns: true,
      dualColumnHeaders: ['F407', 'F607'],
      specifications: [
        { parameter: 'Clamping dia', values: { 'F407': '48 mm', 'F607': '60 mm' } },
        { parameter: 'AC Current', values: { 'F407': '0.25 to 1000 A', 'F607': '0.25 to 2000 A' } },
        { parameter: 'DC Current', values: { 'F407': '0.25 to 1500 A', 'F607': '0.25 to 3000 A' } },
        { parameter: 'DC Voltage', values: { 'F407': '0.15 to 1,000 V', 'F607': '0.15 to 1,000 V' } },
        { parameter: 'AC Voltage', values: { 'F407': '0.15 to 1,000 V', 'F607': '0.15 to 1,000 V' } },
        { parameter: 'Resistance', values: { 'F407': '0.1 Ω to 99.99 kΩ', 'F607': '0.1 Ω to 99.99 kΩ' } },
        { parameter: 'Harmonic', values: { 'F407': '25th', 'F607': '25th' } },
        { parameter: 'KW/Kvar/kVA', values: { 'F407': '1 to 1000', 'F607': '1 to 2000' } }
      ]
    }
  ];
  
  const selectedProductData = products.find(product => product.id === selectedProduct) || products[0];

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }
  };
  
  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };
  
  const itemVariant = {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.4 } }
  };
  
  const productCardVariants = {
    initial: { scale: 0.95, opacity: 0 },
    animate: { scale: 1, opacity: 1, transition: { duration: 0.5 } },
    exit: { scale: 0.95, opacity: 0, transition: { duration: 0.3 } }
  };

  // Download PDF function
  const downloadPDF = (pdfUrl: string | undefined, model: string) => {
    if (!pdfUrl) return;
    
    // Create an anchor element and set attributes
    const link = document.createElement('a');
    link.href = pdfUrl;
    link.download = `${model}-brochure.pdf`;
    link.target = '_blank';
    
    // Append to the document, click, and remove
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  return (
    <PageLayout
      title="Power & Harmonic Clamps"
      subtitle="High-quality clamp meters for accurate power and harmonic measurements"
      category="measure"
    >
      {/* Hero Section with Gradient Background */}
      <div className="relative overflow-hidden bg-gradient-to-br from-cyan-800 via-teal-700 to-cyan-700 -mt-4 -mx-4 mb-12 py-16 px-4">
        <div className="absolute inset-0">
          <svg className="w-full h-full opacity-10" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                <path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" strokeWidth="0.5"/>
              </pattern>
              <linearGradient id="heroGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#0891B2" stopOpacity="0.3" />
                <stop offset="100%" stopColor="#0E7490" stopOpacity="0.3" />
              </linearGradient>
            </defs>
            <rect width="100" height="100" fill="url(#grid)" />
          </svg>
        </div>
        
        {/* Animated particles */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute h-4 w-4 rounded-full bg-white/20 top-1/4 left-1/4 animate-float"></div>
          <div className="absolute h-6 w-6 rounded-full bg-white/10 top-3/4 left-1/3 animate-float" style={{animationDelay: "1s", animationDuration: "7s"}}></div>
          <div className="absolute h-3 w-3 rounded-full bg-white/15 top-1/2 right-1/4 animate-float" style={{animationDelay: "2s", animationDuration: "9s"}}></div>
          <div className="absolute h-5 w-5 rounded-full bg-white/10 bottom-1/4 right-1/3 animate-float" style={{animationDelay: "3s", animationDuration: "8s"}}></div>
        </div>
        
        <motion.div 
          className="relative z-10 max-w-4xl mx-auto text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="inline-block mb-3 bg-white/10 backdrop-blur-lg px-4 py-1 rounded-full text-cyan-200 text-sm font-medium">
            Professional Measurement Solutions
          </div>
          <h1 className="text-5xl font-bold mb-4 text-white bg-clip-text text-transparent bg-gradient-to-r from-white to-cyan-100">
            POWER & HARMONIC <span className="bg-clip-text text-transparent bg-gradient-to-r from-cyan-300 to-teal-300">CLAMPS</span>
          </h1>
          <p className="text-cyan-100 text-xl mb-8 max-w-2xl mx-auto leading-relaxed">
            Precision measurement tools designed for professionals who demand accuracy and reliability
          </p>
          
          {/* Navigation tabs with animated indicator */}
          <div className="inline-flex bg-white/10 backdrop-blur-lg rounded-full p-1 mx-auto border border-white/20 shadow-xl">
            <Link to="/measure/clamp-meters" className="px-6 py-2.5 text-white/80 hover:text-white rounded-full transition-colors">
              Overview
            </Link>
            <div className="px-6 py-2.5 text-white font-medium rounded-full bg-white/20 shadow-inner">
              Power & Harmonic
            </div>
            <Link to="/measure/clamp-meters/leakage" className="px-6 py-2.5 text-white/80 hover:text-white rounded-full transition-colors">
              Leakage Clamps
            </Link>
          </div>
        </motion.div>
        
        {/* Animated Wave Bottom */}
        <div className="absolute bottom-0 left-0 right-0">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 120" className="w-full h-20">
            <path 
              fill="#ffffff" 
              fillOpacity="1" 
              d="M0,64L40,69.3C80,75,160,85,240,80C320,75,400,53,480,48C560,43,640,53,720,69.3C800,85,880,107,960,101.3C1040,96,1120,64,1200,48C1280,32,1360,32,1400,32L1440,32L1440,120L1400,120C1360,120,1280,120,1200,120C1120,120,1040,120,960,120C880,120,800,120,720,120C640,120,560,120,480,120C400,120,320,120,240,120C160,120,80,120,40,120L0,120Z"
            ></path>
          </svg>
        </div>
      </div>
      
      {/* Modern Product Selection Cards */}
      <motion.div 
        className="flex flex-wrap justify-center gap-6 mb-16"
        variants={staggerContainer}
        initial="hidden"
        animate="visible"
      >
        {products.map((product) => (
          <motion.div
            key={product.id}
            variants={itemVariant}
            className={`relative cursor-pointer overflow-hidden rounded-2xl transition-all duration-300 ${
              selectedProduct === product.id 
                ? 'transform scale-105 bg-gradient-to-b from-cyan-100 to-white shadow-xl shadow-cyan-200/50 border border-cyan-200' 
                : 'bg-white hover:shadow-xl shadow-md hover:shadow-cyan-100/50 border border-gray-100'
            }`}
            onClick={() => setSelectedProduct(product.id)}
            whileHover={{ y: -5 }}
          >
            <div className="p-6 w-64 h-64 flex flex-col items-center justify-center relative">
              {/* Decorative elements */}
              <div className="absolute top-0 left-0 w-full h-24 bg-gradient-to-b from-cyan-50 to-transparent opacity-80"></div>
              <div className="absolute bottom-0 right-0 w-full h-24 bg-gradient-to-t from-cyan-50 to-transparent opacity-50"></div>
              
              {selectedProduct === product.id && (
                <div className="absolute top-3 right-3 z-20">
                  <span className="bg-gradient-to-r from-cyan-600 to-teal-600 text-white text-xs font-medium px-3 py-1 rounded-full shadow-md">
                    Selected
                  </span>
                </div>
              )}
              
              <div className="relative w-36 h-36 mb-4 flex items-center justify-center">
                <img
                  src={product.image}
                  alt={`Model ${product.model}`}
                  className="max-w-full max-h-full object-contain transition-all z-10"
                />
                
                {/* Background elements */}
                <div className={`absolute inset-0 rounded-full ${
                  selectedProduct === product.id 
                    ? 'bg-gradient-to-r from-cyan-100 via-teal-100 to-cyan-100 animate-pulse' 
                    : 'bg-gray-50'
                }`}
                style={{ zIndex: 1 }}></div>
                
                {/* Decorative rings */}
                <div className={`absolute inset-2 rounded-full border ${
                  selectedProduct === product.id ? 'border-cyan-200' : 'border-gray-200'
                } opacity-50`}></div>
              </div>
              
              <h3 className="font-bold text-lg text-center text-gray-800 relative z-10">
                Model {product.model}
              </h3>
              
              <p className="text-sm text-center text-gray-500 mt-2 relative z-10">
                {product.features[0]}
              </p>
            </div>
          </motion.div>
        ))}
      </motion.div>
      
      {/* Product Details with 3D-like effect */}
      <AnimatePresence mode="wait">
        <motion.div
          key={selectedProduct}
          variants={productCardVariants}
          initial="initial"
          animate="animate"
          exit="exit"
          className="mb-16"
        >
          <div className="bg-white rounded-2xl shadow-xl overflow-hidden relative transform perspective-1000">
            {/* Glass-like header */}
            <div className="bg-gradient-to-r from-cyan-700 via-teal-600 to-cyan-700 p-8 relative overflow-hidden">
              <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
              <div className="absolute inset-0">
                <svg width="100%" height="100%" className="opacity-20">
                  <pattern id="product-grid" width="20" height="20" patternUnits="userSpaceOnUse">
                    <circle cx="10" cy="10" r="1" fill="white" fillOpacity="0.4" />
                  </pattern>
                  <rect width="100%" height="100%" fill="url(#product-grid)" />
                </svg>
              </div>
              <div className="relative z-10">
                <div className="flex items-center justify-between">
                  <h2 className="text-3xl font-bold text-white flex items-center mb-2">
                    <span className="mr-3">Model {selectedProductData.model}</span>
                  </h2>
                </div>
                <p className="text-cyan-100">
                  High-precision power measurement solutions for industry professionals
                </p>
              </div>
            </div>
            
            <div className="flex flex-col lg:flex-row">
              {/* Product Image with hover effect */}
              <div className="lg:w-1/3 p-8 bg-gradient-to-b from-cyan-50 via-teal-50 to-cyan-50 relative overflow-hidden">
                <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
                <div className="relative group">
                  <div className="absolute -inset-1 bg-gradient-to-r from-teal-600 to-cyan-600 rounded-full opacity-0 group-hover:opacity-10 transition-opacity duration-300 blur-xl"></div>
                  
                  <motion.div
                    whileHover={{ rotate: [0, -1, 1, -1, 0], scale: 1.08 }}
                    transition={{ duration: 0.7 }}
                    className="relative z-10 flex items-center justify-center p-8"
                  >
                    <img
                      src={selectedProductData.image}
                      alt={`Power & Harmonic Clamp Model ${selectedProductData.model}`}
                      className="max-w-full h-auto max-h-96 drop-shadow-2xl"
                    />
                  </motion.div>
                  
                  {/* Decorative elements */}
                  <div className="absolute top-1/4 left-0 w-3 h-3 bg-cyan-500 rounded-full animate-ping" style={{ animationDuration: '3s' }}></div>
                  <div className="absolute bottom-1/3 right-0 w-2 h-2 bg-teal-400 rounded-full animate-ping" style={{ animationDuration: '4s' }}></div>
                  <div className="absolute bottom-1/4 left-1/4 w-1.5 h-1.5 bg-cyan-400 rounded-full animate-ping" style={{ animationDuration: '5s' }}></div>
                </div>
                
                {/* Preview button below the image */}
                <div className="flex justify-center mt-6">
                  <button className="bg-white/90 hover:bg-white text-cyan-600 border border-cyan-200 px-6 py-2.5 rounded-full transition-all shadow-lg backdrop-blur-sm flex items-center hover:scale-105">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                      <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                    </svg>
                    PREVIEW
                  </button>
                </div>
              </div>
              
              {/* Product Details with tabs */}
              <div className="lg:w-2/3 p-8">
                {/* Tabs for Features/Specs */}
                <div className="flex mb-8 border-b border-cyan-100">
                  <button
                    className={`px-6 py-3 text-lg font-medium transition-all ${
                      activeTab === 'features' 
                        ? 'text-cyan-600 border-b-2 border-cyan-600' 
                        : 'text-gray-500 hover:text-gray-700'
                    }`}
                    onClick={() => setActiveTab('features')}
                  >
                    <div className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      Features
                    </div>
                  </button>
                  <button
                    className={`px-6 py-3 text-lg font-medium transition-all ${
                      activeTab === 'specs' 
                        ? 'text-cyan-600 border-b-2 border-cyan-600' 
                        : 'text-gray-500 hover:text-gray-700'
                    }`}
                    onClick={() => setActiveTab('specs')}
                  >
                    <div className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                      </svg>
                      Specifications
                    </div>
                  </button>
                </div>
                
                {/* Content based on active tab */}
                <AnimatePresence mode="wait">
                  {activeTab === 'features' ? (
                    <motion.div
                      key="features"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ duration: 0.3 }}
                    >
                      <motion.div 
                        className="grid grid-cols-1 md:grid-cols-2 gap-4"
                        variants={staggerContainer}
                        initial="hidden"
                        animate="visible"
                      >
                        {selectedProductData.features.map((feature, index) => (
                          <motion.div 
                            key={index} 
                            className="flex items-start group"
                            variants={itemVariant}
                          >
                            <div className="flex-shrink-0 mr-3 mt-1">
                              <div className="w-6 h-6 rounded-full bg-gradient-to-br from-cyan-400 to-teal-500 flex items-center justify-center shadow-lg shadow-cyan-500/20 group-hover:shadow-cyan-500/40 transition-all">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-white" viewBox=" 0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                              </div>
                            </div>
                            <div>
                              <p className="text-gray-800 font-medium group-hover:text-gray-900 transition-colors">
                                {feature}
                              </p>
                            </div>
                          </motion.div>
                        ))}
                      </motion.div>
                    </motion.div>
                  ) : (
                    <motion.div
                      key="specifications"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ duration: 0.3 }}
                      className="overflow-x-auto"
                    >
                      {selectedProductData.hasDualColumns ? (
                        <div className="overflow-hidden rounded-xl shadow-md">
                          <table className="w-full border-collapse">
                            <thead>
                              <tr className="bg-gradient-to-r from-cyan-600 to-teal-600 text-white">
                                <th className="p-3 text-left rounded-tl-xl font-bold text-base">PARAMETER</th>
                                {selectedProductData.dualColumnHeaders?.map((header, index) => (
                                  <th 
                                    key={index} 
                                    className={`p-3 text-left font-bold text-base ${index === selectedProductData.dualColumnHeaders!.length - 1 ? 'rounded-tr-xl' : ''}`}
                                  >
                                    {header}
                                  </th>
                                ))}
                              </tr>
                            </thead>
                            <tbody>
                              {selectedProductData.specifications.map((spec, index) => (
                                <motion.tr 
                                  key={index} 
                                  className={`${index % 2 === 0 ? 'bg-cyan-50' : 'bg-white'} hover:bg-cyan-100/50 transition-colors`}
                                  initial={{ opacity: 0, y: 10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  transition={{ delay: index * 0.05, duration: 0.3 }}
                                >
                                  <td className="p-3 border-t border-cyan-100 font-semibold text-gray-700">{spec.parameter}</td>
                                  {selectedProductData.dualColumnHeaders?.map((header, headerIndex) => (
                                    <td key={headerIndex} className="p-3 border-t border-cyan-100 text-gray-800">
                                      {typeof spec.values === 'object' ? spec.values[header] : ''}
                                    </td>
                                  ))}
                                </motion.tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      ) : (
                        <div className="overflow-hidden rounded-xl shadow-md">
                          <table className="w-full border-collapse">
                            <thead>
                              <tr className="bg-gradient-to-r from-cyan-600 to-teal-600 text-white">
                                <th className="p-3 text-left rounded-tl-xl font-bold text-base">PARAMETER</th>
                                <th className="p-3 text-left font-bold text-base">RANGE</th>
                                <th className="p-3 text-left rounded-tr-xl font-bold text-base">ACCURACY %</th>
                              </tr>
                            </thead>
                            <tbody>
                              {selectedProductData.specifications.map((spec, index) => (
                                <motion.tr 
                                  key={index} 
                                  className={`${index % 2 === 0 ? 'bg-cyan-50' : 'bg-white'} hover:bg-cyan-100/50 transition-colors`}
                                  initial={{ opacity: 0, y: 10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  transition={{ delay: index * 0.05, duration: 0.3 }}
                                >
                                  <td className="p-3 border-t border-cyan-100 font-semibold text-gray-700">{spec.parameter}</td>
                                  <td className="p-3 border-t border-cyan-100 whitespace-pre-line text-gray-800">
                                    {Array.isArray(spec.values) ? spec.values[0] : ''}
                                  </td>
                                  <td className="p-3 border-t border-cyan-100 text-gray-800">
                                    {Array.isArray(spec.values) && spec.values[1] ? spec.values[spec.values.length - 1] : ''}
                                  </td>
                                </motion.tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      )}
                    </motion.div>
                  )}
                </AnimatePresence>
                
                {/* CTA Section with both buttons next to each other */}
                <motion.div 
                  className="mt-10 flex flex-wrap gap-4"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3, duration: 0.5 }}
                >
                  <button className="bg-gradient-to-r from-cyan-600 via-teal-600 to-cyan-600 hover:from-cyan-700 hover:via-teal-700 hover:to-cyan-700 text-white px-8 py-3.5 rounded-full transition-all shadow-lg shadow-cyan-500/30 hover:shadow-cyan-500/50 font-medium flex items-center group">
                    <span>ENQUIRE NOW</span>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2 transform group-hover:translate-x-1 transition-transform" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L13.586 10l-3.293-3.293a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                  
                  {/* PDF Brochure button moved here, next to ENQUIRE NOW button */}
                  <button 
                    className="bg-gradient-to-r from-cyan-600 to-teal-600 hover:from-cyan-700 hover:to-teal-700 text-white px-8 py-3.5 rounded-full transition-all shadow-lg shadow-cyan-500/30 flex items-center"
                    onClick={() => downloadPDF(selectedProductData.pdf, selectedProductData.model)}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clipRule="evenodd" />
                    </svg>
                    PDF BROCHURE
                  </button>
                </motion.div>
              </div>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>
      
      {/* Features Highlight Cards */}
      <motion.div 
        className="mb-16"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <h2 className="text-3xl font-bold mb-8 text-center text-gray-800">
          Key Features & <span className="text-cyan-600">Capabilities</span>
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Feature Card 1 */}
          <motion.div 
            className="bg-white rounded-xl shadow-lg overflow-hidden group hover:shadow-xl transition-all"
            whileHover={{ y: -5 }}
          >
            <div className="h-3 bg-cyan-500"></div>
            <div className="p-6">
              <div className="w-12 h-12 rounded-lg bg-cyan-100 text-cyan-600 flex items-center justify-center mb-4 group-hover:bg-cyan-500 group-hover:text-white transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2 text-gray-800">High Accuracy Measurement</h3>
              <p className="text-gray-600">
                Precision-engineered for industry professionals with true RMS reading on AC and AC+DC for reliable results.
              </p>
            </div>
          </motion.div>
          
          {/* Feature Card 2 */}
          <motion.div 
            className="bg-white rounded-xl shadow-lg overflow-hidden group hover:shadow-xl transition-all"
            whileHover={{ y: -5 }}
          >
            <div className="h-3 bg-teal-500"></div>
            <div className="p-6">
              <div className="w-12 h-12 rounded-lg bg-teal-100 text-teal-600 flex items-center justify-center mb-4 group-hover:bg-teal-500 group-hover:text-white transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2 text-gray-800">Advanced Power Analysis</h3>
              <p className="text-gray-600">
                Comprehensive power factor measurement and harmonic analysis capabilities for detailed power quality assessment.
              </p>
            </div>
          </motion.div>
          
          {/* Feature Card 3 */}
          <motion.div 
            className="bg-white rounded-xl shadow-lg overflow-hidden group hover:shadow-xl transition-all"
            whileHover={{ y: -5 }}
          >
            <div className="h-3 bg-cyan-500"></div>
            <div className="p-6">
              <div className="w-12 h-12 rounded-lg bg-cyan-100 text-cyan-600 flex items-center justify-center mb-4 group-hover:bg-cyan-500 group-hover:text-white transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2 text-gray-800">Safety Certified</h3>
              <p className="text-gray-600">
                Built to CAT IV 1000V safety standards, ensuring protection in the most demanding electrical environments.
              </p>
            </div>
          </motion.div>
        </div>
      </motion.div>

      {/* Related Products Section with Enhanced Gradients */}
      <motion.div 
        className="mb-16"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 1.0 }}
      >
        <h2 className="text-3xl font-bold mb-8 text-center text-gray-800">
          Related <span className="text-cyan-600">Products</span>
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Related Product 1 */}
          <motion.div 
            className="bg-white rounded-xl shadow-lg overflow-hidden group hover:shadow-xl transition-all border border-cyan-100"
            whileHover={{ y: -5, scale: 1.02 }}
          >
            <div className="h-48 bg-gradient-to-br from-cyan-400/10 via-cyan-200/10 to-teal-400/10 flex items-center justify-center p-6 relative overflow-hidden">
              <div className="absolute inset-0 bg-grid-pattern opacity-20"></div>
              
              {/* Animated background elements */}
              <div className="absolute h-16 w-16 -top-6 -right-6 bg-cyan-400/20 rounded-full blur-xl animate-float" style={{animationDelay: "0s", animationDuration: "8s"}}></div>
              <div className="absolute h-20 w-20 -bottom-10 -left-10 bg-teal-400/20 rounded-full blur-xl animate-float" style={{animationDelay: "2s", animationDuration: "10s"}}></div>
              
              <img 
                src="/images/leakage-clamp-1.png" 
                alt="Leakage Clamp" 
                className="max-h-full max-w-full object-contain group-hover:scale-110 transition-transform duration-700 relative z-10"
              />
              
              {/* Glowing effect on hover */}
              <div className="absolute inset-0 bg-gradient-to-br from-cyan-400/0 to-teal-400/0 group-hover:from-cyan-400/10 group-hover:to-teal-400/10 transition-all duration-500"></div>
            </div>
            <div className="p-6 relative">
              {/* Decorative accent */}
              <div className="absolute w-1/3 h-1 top-0 left-0 bg-gradient-to-r from-cyan-500 to-transparent"></div>
              
              <h3 className="text-xl font-semibold mb-2 text-gray-800">Leakage Clamp Meters</h3>
              <p className="text-gray-600 mb-4">
                High-sensitivity clamp meters designed for leakage current measurement in various applications.
              </p>
              <Link to="/measure/clamp-meters/leakage" className="text-cyan-600 font-medium hover:text-cyan-700 transition-colors flex items-center">
                <span>View Details</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1 group-hover:translate-x-1 transition-transform" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L13.586 10l-3.293-3.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </Link>
            </div>
          </motion.div>
          
          {/* Related Product 2 */}
          <motion.div 
            className="bg-white rounded-xl shadow-lg overflow-hidden group hover:shadow-xl transition-all border border-teal-100"
            whileHover={{ y: -5, scale: 1.02 }}
          >
            <div className="h-48 bg-gradient-to-br from-teal-400/10 via-cyan-200/10 to-cyan-400/10 flex items-center justify-center p-6 relative overflow-hidden">
              <div className="absolute inset-0 bg-grid-pattern opacity-20"></div>
              
              {/* Animated background elements */}
              <div className="absolute h-16 w-16 -bottom-6 -right-6 bg-teal-400/20 rounded-full blur-xl animate-float" style={{animationDelay: "1s", animationDuration: "9s"}}></div>
              <div className="absolute h-20 w-20 -top-10 -left-10 bg-cyan-400/20 rounded-full blur-xl animate-float" style={{animationDelay: "3s", animationDuration: "11s"}}></div>
              
              <img 
                src="/images/multimeter-1.png" 
                alt="Digital Multimeter" 
                className="max-h-full max-w-full object-contain group-hover:scale-110 transition-transform duration-700 relative z-10"
              />
              
              {/* Glowing effect on hover */}
              <div className="absolute inset-0 bg-gradient-to-br from-teal-400/0 to-cyan-400/0 group-hover:from-teal-400/10 group-hover:to-cyan-400/10 transition-all duration-500"></div>
            </div>
            <div className="p-6 relative">
              {/* Decorative accent */}
              <div className="absolute w-1/3 h-1 top-0 left-0 bg-gradient-to-r from-teal-500 to-transparent"></div>
              
              <h3 className="text-xl font-semibold mb-2 text-gray-800">Digital Multimeters</h3>
              <p className="text-gray-600 mb-4">
                Precision measurement tools for voltage, current, resistance, and more in a compact form factor.
              </p>
              <Link to="/measure/multimeters" className="text-teal-600 font-medium hover:text-teal-700 transition-colors flex items-center">
                <span>View Details</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1 group-hover:translate-x-1 transition-transform" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L13.586 10l-3.293-3.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </Link>
            </div>
          </motion.div>
          
          {/* Related Product 3 */}
          <motion.div 
            className="bg-white rounded-xl shadow-lg overflow-hidden group hover:shadow-xl transition-all border border-cyan-100"
            whileHover={{ y: -5, scale: 1.02 }}
          >
            <div className="h-48 bg-gradient-to-br from-cyan-400/10 via-teal-200/10 to-cyan-400/10 flex items-center justify-center p-6 relative overflow-hidden">
              <div className="absolute inset-0 bg-grid-pattern opacity-20"></div>
              
              {/* Animated background elements */}
              <div className="absolute h-16 w-16 -top-6 -left-6 bg-cyan-400/20 rounded-full blur-xl animate-float" style={{animationDelay: "2s", animationDuration: "10s"}}></div>
              <div className="absolute h-20 w-20 -bottom-10 -right-10 bg-teal-400/20 rounded-full blur-xl animate-float" style={{animationDelay: "4s", animationDuration: "12s"}}></div>
              
              <img 
                src="/images/power-analyzer-1.png" 
                alt="Power Analyzer" 
                className="max-h-full max-w-full object-contain group-hover:scale-110 transition-transform duration-700 relative z-10"
              />
              
              {/* Glowing effect on hover */}
              <div className="absolute inset-0 bg-gradient-to-br from-cyan-400/0 to-teal-400/0 group-hover:from-cyan-400/10 group-hover:to-teal-400/10 transition-all duration-500"></div>
            </div>
            <div className="p-6 relative">
              {/* Decorative accent */}
              <div className="absolute w-1/3 h-1 top-0 left-0 bg-gradient-to-r from-cyan-500 to-transparent"></div>
              
              <h3 className="text-xl font-semibold mb-2 text-gray-800">Power Analyzers</h3>
              <p className="text-gray-600 mb-4">
                Advanced instruments for comprehensive power quality analysis and energy efficiency assessments.
              </p>
              <Link to="/measure/power-analyzers" className="text-cyan-600 font-medium hover:text-cyan-700 transition-colors flex items-center">
                <span>View Details</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1 group-hover:translate-x-1 transition-transform" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L13.586 10l-3.293-3.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </Link>
            </div>
          </motion.div>
        </div>
      </motion.div>
      
      {/* FAQ Section with Accordion */}
      <motion.div 
        className="mb-16"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 1.2 }}
      >
        <h2 className="text-3xl font-bold mb-8 text-center text-gray-800">
          Frequently Asked <span className="text-cyan-600">Questions</span>
        </h2>
        
        <div className="max-w-3xl mx-auto">
          <Accordion />
        </div>
      </motion.div>
    </PageLayout>
  );
};

// Accordion Component for FAQ Section
const Accordion = () => {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  
  const faqs = [
    {
      question: "What is the difference between the F403, F405, and F407 models?",
      answer: "The F403 is our standard model with essential power measurement capabilities. The F405 adds harmonic analysis up to the 25th order. The F407 further enhances capabilities with Bluetooth communication for data transfer to mobile devices and computers."
    },
    {
      question: "Are these clamp meters suitable for three-phase power systems?",
      answer: "Yes, all our power and harmonic clamp meters are designed to work with both single-phase and three-phase power systems. They can measure voltage, current, and power parameters in balanced and unbalanced three-phase configurations."
    },
    {
      question: "What safety certifications do these clamp meters have?",
      answer: "All our power and harmonic clamp meters are certified to CAT IV 1000V safety standards, ensuring they are suitable for measurements at the primary supply level and all low-voltage installations, including the most demanding industrial environments."
    },
    {
      question: "Can I measure harmonic distortion with these clamp meters?",
      answer: "Yes, models F405, F407, and F607 have built-in capabilities to measure Total Harmonic Distortion (THD) for both voltage and current up to the 25th harmonic order, making them ideal for power quality assessment."
    },
    {
      question: "What is the warranty period for these products?",
      answer: "All our power and harmonic clamp meters come with a standard 3-year warranty that covers manufacturing defects and component failures under normal use conditions. Extended warranty options are also available."
    }
  ];
  
  const toggleAccordion = (index: number) => {
    setActiveIndex(activeIndex === index ? null : index);
  };
  
  return (
    <div className="space-y-4">
      {faqs.map((faq, index) => (
        <div 
          key={index} 
          className="border border-cyan-100 rounded-xl overflow-hidden transition-all shadow-sm"
        >
          <button
            className={`w-full p-4 text-left font-medium flex justify-between items-center ${
              activeIndex === index ? 'bg-cyan-50 text-cyan-700' : 'bg-white text-gray-800'
            }`}
            onClick={() => toggleAccordion(index)}
          >
            <span>{faq.question}</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`h-5 w-5 transition-transform ${activeIndex === index ? 'transform rotate-180 text-cyan-600' : 'text-gray-500'}`}
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
          <div
            className={`bg-white transition-all duration-300 overflow-hidden ${
              activeIndex === index ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
            }`}
          >
            <div className="p-4 border-t border-cyan-100">
              <p className="text-gray-600">{faq.answer}</p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default PowerHarmonicClampsPage;