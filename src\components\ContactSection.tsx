import React from "react";
import { motion, useInView } from "framer-motion";
import { useRef } from "react";
import {
  Mail,
  Phone,
  MapPin,
  ChevronRight,
  Navigation,
  ArrowRight,
  FileText
} from "lucide-react";

const ContactSection = () => {
  const footerRef = useRef(null);
  const isInView = useInView(footerRef, { once: false, amount: 0.1 });

  // City data organized by columns as in the original image
  const cityColumns = [
    ["GUNTUR", "VIZAG", "GUWAHATI", "KOLKATA", "JABALPUR"],
    ["PATNA", "DELH<PERSON>", "AHMEDABAD", "LUCK<PERSON><PERSON>", "MANG<PERSON><PERSON><PERSON>"],
    ["RAJKOT", "SURAT", "FARIDABAD", "BHU<PERSON>NESHWAR", "MORADABAD"],
    ["COCHIN", "RAIPUR", "BANGALORE", "<PERSON><PERSON><PERSON>", "PONDICHERRY"],
    ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"],
    ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "IN<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "TRIVANDRUM"],
    ["HYDERABAD", "CHANDIGARH", "LUDHIANA", "DINDIGUL", "VISAKAPATNAM"],
    ["JAIPUR", "CHENNAI", "COIMBATORE", "FAIZABAD"],
    ["ERODE", "TIRUPUR", "MADURAI", "GOA"]
  ];

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 }
  };

  // Color classes for city items
  const getColorClass = (colIndex, cityIndex) => {
    const colors = [
      "hover:text-yellow-300 hover:bg-yellow-900/20",
      "hover:text-blue-300 hover:bg-blue-900/20",
      "hover:text-green-300 hover:bg-green-900/20"
    ];
    return colors[(colIndex + cityIndex) % 3];
  };

  return (
    <section
      ref={footerRef}
      className="py-12 bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white"
    >
      <div className="container mx-auto px-4">
        {/* Title with decorative elements */}
        <div className="flex items-center justify-center mb-8">
          <div className="h-1 w-12 bg-yellow-400 rounded mr-4"></div>
          <h2 className="text-2xl md:text-3xl font-bold text-center bg-clip-text text-transparent bg-gradient-to-r from-yellow-400 via-blue-400 to-green-400">
            Our Nationwide Presence
          </h2>
          <div className="h-1 w-12 bg-green-400 rounded ml-4"></div>
        </div>

        <motion.div
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={containerVariants}
          className="grid grid-cols-3 md:grid-cols-5 lg:grid-cols-9 gap-4 md:gap-2 max-w-5xl mx-auto"
        >
          {cityColumns.map((column, colIndex) => (
            <div key={`col-${colIndex}`} className="flex flex-col space-y-3">
              {column.map((city, cityIndex) => (
                <motion.div
                  key={`city-${colIndex}-${cityIndex}`}
                  variants={itemVariants}
                  whileHover={{ scale: 1.05, transition: { duration: 0.15 } }}
                  className="text-center"
                >
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      try {
                        // Use React Router navigation
                        window.location.href = "/contact/sales";
                      } catch (error) {
                        console.error("Navigation error:", error);
                      }
                    }}
                    className={`text-xs sm:text-sm font-medium py-1 px-2 rounded-md transition-all duration-300 cursor-pointer ${getColorClass(colIndex, cityIndex)}`}
                  >
                    {city}
                  </button>
                </motion.div>
              ))}
            </div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default ContactSection;