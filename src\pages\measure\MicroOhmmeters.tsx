import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { motion } from "framer-motion";
import {
  ArrowRight,
  Check,
  ChevronRight,
  Zap,
  Shield,
  Gauge,
  FileText,
  ExternalLink
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import PageLayout from "@/components/layout/PageLayout";

// Adding CSS for complete status indicators
const completeStyles = {
  complete: {
    backgroundColor: "#FFFF00", // Pure yellow
    color: "#000000", // Black text for contrast
  }
};

// PDF URL for brochure
const PDF_URL = "/T&M April 2025.pdf";

// No PDF Viewer Component - Using direct link instead

// Enhanced Hero Section
const HeroSection = ({ onRequestDemo, onViewBrochure }: { onRequestDemo: () => void; onViewBrochure: () => void }) => {
  return (
    <div className="relative py-6 md:py-12 overflow-hidden font-['Open_Sans']">
      {/* Hero Background Elements - Mobile optimized */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 right-0 w-1/2 md:w-3/4 h-full bg-yellow-50 rounded-bl-[50px] md:rounded-bl-[100px] transform -skew-x-6 md:-skew-x-12"></div>
        <div className="absolute bottom-20 left-0 w-32 h-32 md:w-64 md:h-64 bg-yellow-400 rounded-full opacity-10"></div>
      </div>

      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 items-center">
          {/* Text Content */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-4 text-center lg:text-left"
          >
            <div className="inline-block bg-yellow-400 py-1 px-3 rounded-full mb-2">
              <span className="text-sm md:text-base font-semibold text-gray-900 font-['Open_Sans']">KRYKARD Precision Instruments</span>
            </div>
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight font-['Open_Sans']">
              MICRO <span className="text-yellow-400">OHMMETERS</span>
            </h1>

            <p className="text-base md:text-lg lg:text-xl text-gray-900 leading-relaxed font-medium text-center lg:text-justify max-w-lg mx-auto lg:mx-0 font-['Open_Sans']">
              High-precision instruments for measuring very low resistances with exceptional accuracy and reliability
            </p>

            <div className="pt-4 flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center lg:justify-start max-w-md sm:max-w-none mx-auto lg:mx-0">
              <Button
                className="w-full sm:w-auto px-4 sm:px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-semibold rounded-lg shadow-md transition duration-300 flex items-center justify-center space-x-2 text-sm sm:text-base"
                onClick={onRequestDemo}
              >
                <span>Request Demo</span>
                <ArrowRight className="ml-2 h-4 sm:h-5 w-4 sm:w-5" />
              </Button>
              <Button
                className="w-full sm:w-auto px-4 sm:px-6 py-3 bg-white border-2 border-yellow-400 text-gray-900 font-semibold rounded-lg shadow-sm transition duration-300 hover:bg-gray-50 flex items-center justify-center space-x-2 text-sm sm:text-base"
                onClick={onViewBrochure}
              >
                <span>View Brochure</span>
                <FileText className="ml-2 h-4 sm:h-5 w-4 sm:w-5" />
              </Button>
            </div>
          </motion.div>

          {/* Product Image */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative order-first lg:order-last"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-200 to-yellow-50 rounded-full opacity-20 blur-xl transform scale-90"></div>
            <motion.div
              animate={{
                y: [0, -10, 0],
              }}
              transition={{
                repeat: Infinity,
                duration: 3,
                ease: "easeInOut"
              }}
              className="relative z-10 flex justify-center"
            >
              <img
                src="/Ohm meters\images__1_-removebg-preview.png"
                alt="Micro-Ohmmeter"
                className="max-h-[300px] sm:max-h-[400px] md:max-h-[500px] lg:max-h-[600px] w-auto object-contain drop-shadow-2xl"
              />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

// Feature Highlight Component
const FeatureHighlight = ({ icon, title, description }: { icon: React.ReactNode; title: string; description: string }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
      whileHover={{ y: -8, boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)" }}
      className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 p-4 h-full border-b-4 border-yellow-400 font-['Open_Sans']"
    >
      <div className="flex flex-col h-full text-center md:text-left">
        <div className="bg-gradient-to-br from-yellow-400 to-yellow-300 w-12 h-12 rounded-lg flex items-center justify-center mb-3 shadow-md mx-auto md:mx-0">
          {icon}
        </div>
        <h3 className="text-base md:text-lg lg:text-xl font-bold text-gray-900 mb-2 font-['Open_Sans']">{title}</h3>
        <p className="text-gray-900 flex-grow font-medium text-sm md:text-base text-center md:text-justify font-['Open_Sans']">{description}</p>
      </div>
    </motion.div>
  );
};

// Feature component for product cards
const Feature = ({ children }: { children: React.ReactNode }) => (
  <div className="flex items-start mb-3 group">
    <Check className="h-4 w-4 text-yellow-400 mt-1 mr-2 flex-shrink-0" />
    <span className="text-gray-900 font-medium text-sm md:text-base font-['Open_Sans']">{children}</span>
  </div>
);

// Product Card Component
const ProductCard = ({ title, subtitle, image, features, specs, onRequestDemo }: {
  title: string;
  subtitle: string;
  image: string;
  features: string[];
  specs: string[];
  onRequestDemo: () => void
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="bg-white shadow-xl overflow-hidden mb-6 hover:shadow-2xl transition-all duration-300 rounded-xl group font-['Open_Sans']"
    >
      <div className="bg-gradient-to-r from-yellow-400 to-yellow-400 py-3 md:py-4 px-4 md:px-6">
        <div className="flex flex-col sm:flex-row justify-between items-center gap-2">
          <h3 className="text-lg md:text-xl lg:text-2xl font-bold text-white text-center sm:text-left font-['Open_Sans']">{title}</h3>
          <div className="bg-white bg-opacity-20 py-1 px-3 rounded-full">
            <span className="text-white font-medium text-sm md:text-base">{subtitle}</span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 md:gap-6 p-4 md:p-6">
        {/* Left side - Image with enhanced styling */}
        <div className="relative flex items-center justify-center p-4 rounded-xl h-[300px] md:h-[400px] md:col-span-3 bg-gradient-to-br from-yellow-100 to-yellow-50 overflow-hidden group-hover:bg-opacity-80 transition-all duration-700">
          {/* Background decoration */}
          <div className="absolute inset-0 bg-gradient-to-br from-yellow-200 to-yellow-50 rounded-full opacity-30 blur-xl transform scale-90"></div>

          <motion.div
            animate={{ y: [0, -8, 0] }}
            transition={{ repeat: Infinity, duration: 3, ease: "easeInOut" }}
            className="relative z-10 flex justify-center items-center"
          >
            <motion.img
              src={image}
              alt={title}
              className="max-h-[280px] md:max-h-[380px] w-auto object-contain drop-shadow-2xl"
              whileHover={{ rotate: [-1, 1, -1], transition: { repeat: Infinity, duration: 2 } }}
              transition={{ duration: 0.3 }}
            />
          </motion.div>
        </div>

        {/* Right side - Content */}
        <div className="flex flex-col justify-between md:col-span-2">
          <div>
            <h4 className="font-semibold text-gray-900 mb-3 pb-2 border-b border-yellow-200 text-base md:text-lg font-['Open_Sans']">Key Features</h4>
            <ul className="space-y-2 mb-4">
              {features.slice(0, 4).map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -10 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  viewport={{ once: true }}
                >
                  <Feature>{feature}</Feature>
                </motion.div>
              ))}
            </ul>

            <h4 className="font-semibold text-gray-900 mb-3 pb-2 border-b border-yellow-200 text-base md:text-lg font-['Open_Sans']">Specifications</h4>
            <ul className="space-y-2">
              {specs.map((spec, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -10 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  viewport={{ once: true }}
                >
                  <Feature>{spec}</Feature>
                </motion.div>
              ))}
            </ul>
          </div>

          <div className="mt-4">
            <Button
              className="w-full bg-gradient-to-r from-yellow-400 to-yellow-400 hover:from-yellow-500 hover:to-yellow-500 text-gray-900 font-semibold py-3 flex items-center justify-center gap-2 rounded-lg transform transition-transform duration-300 group-hover:-translate-y-1 text-sm md:text-base"
              onClick={onRequestDemo}
            >
              Request Product Info
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// Modern comparison table component
const ComparisonTable = () => (
  <div className="bg-white rounded-2xl shadow-lg overflow-hidden mt-6 font-['Open_Sans']">
    <div className="bg-gradient-to-r from-yellow-400 to-yellow-400 p-3">
      <h3 className="text-xl md:text-2xl font-bold text-center text-white font-['Open_Sans']">Model Comparison</h3>
    </div>
    <div className="p-4 overflow-x-auto">
      <table className="min-w-full divide-y divide-yellow-200">
        <thead>
          <tr>
            <th className="px-4 py-3 bg-yellow-50 text-left text-xs font-medium text-yellow-800 uppercase tracking-wider rounded-tl-lg">Feature</th>
            <th className="px-4 py-3 bg-yellow-50 text-center text-xs font-medium text-yellow-800 uppercase tracking-wider">CA 6240</th>
            <th className="px-4 py-3 bg-yellow-50 text-center text-xs font-medium text-yellow-800 uppercase tracking-wider">CA 6255</th>
            <th className="px-4 py-3 bg-yellow-50 text-center text-xs font-medium text-yellow-800 uppercase tracking-wider rounded-tr-lg">CA 6292</th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-yellow-100">
          {[
            { name: "Resistance Range", ca6240: "5μΩ to 399.9Ω", ca6255: "5mΩ to 2,500Ω", ca6292: "0.1μΩ to 1Ω" },
            { name: "Accuracy", ca6240: "±0.25% ± 2 counts", ca6255: "±0.25% ± 2 counts", ca6292: "±1%" },
            { name: "Test Current", ca6240: "Up to 10A", ca6255: "Up to 10A", ca6292: "Up to 200A" },
            { name: "Memory", ca6240: "100 measurements", ca6255: "1,500 measurements", ca6292: "8,000 measurements" },
            { name: "Communication", ca6240: "Optical/USB link", ca6255: "RS 232 link", ca6292: "USB" }
          ].map((feature, idx) => (
            <motion.tr
              key={idx}
              initial={{ opacity: 0, y: 5 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: idx * 0.05 }}
              viewport={{ once: true }}
              className={idx % 2 === 0 ? 'bg-white hover:bg-yellow-50 transition-colors duration-200' : 'bg-gray-50 hover:bg-yellow-50 transition-colors duration-200'}
            >
              <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 font-['Open_Sans']">{feature.name}</td>
              <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 text-center font-medium font-['Open_Sans']">{feature.ca6240}</td>
              <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 text-center font-medium font-['Open_Sans']">{feature.ca6255}</td>
              <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 text-center font-medium font-['Open_Sans']">{feature.ca6292}</td>
            </motion.tr>
          ))}
        </tbody>
      </table>
    </div>
  </div>
);

// Product Overview Card
const ProductOverviewCard = ({ model, current, features, image, onViewDetails }: {
  model: string;
  current: string;
  features: string[];
  image: string;
  onViewDetails: () => void
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="group h-full font-['Open_Sans']"
    >
      <div className="h-full rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 bg-white border border-gray-100">
        {/* Header */}
        <div className="bg-gradient-to-r from-yellow-400 to-yellow-400 p-3 text-white font-bold text-center">
          <span className="text-base md:text-lg font-['Open_Sans']">{model}</span>
        </div>
        {/* Product Image with enhanced styling */}
        <div className="p-4 flex flex-col items-center">
          <div className="relative rounded-xl p-3 mb-4 w-full flex justify-center h-48 md:h-56 bg-gradient-to-br from-yellow-100 to-yellow-50 overflow-hidden">
            {/* Background glow effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-200 to-yellow-50 rounded-full opacity-30 blur-xl transform scale-90"></div>

            <motion.div
              animate={{ y: [0, -6, 0] }}
              transition={{ repeat: Infinity, duration: 3, ease: "easeInOut" }}
              className="relative z-10 flex justify-center items-center"
            >
              <motion.img
                src={image}
                alt={model}
                className="h-40 md:h-48 w-auto object-contain drop-shadow-xl"
                whileHover={{ rotate: [-1, 1, -1], transition: { repeat: Infinity, duration: 2 } }}
                transition={{ duration: 0.3 }}
              />
            </motion.div>
          </div>

          {/* Product Content */}
          <div className="text-center mb-3">
            <div className="inline-block bg-yellow-100 text-yellow-700 px-3 py-1 rounded-full text-sm font-medium mb-3 shadow-sm">
              {current} Test Current
            </div>
            <ul className="text-sm text-gray-900 space-y-1">
              {features.map((feature: string, i: number) => (
                <motion.li
                  key={i}
                  className="flex items-center mb-1 text-center md:text-left"
                  initial={{ opacity: 0, x: -5 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: i * 0.05 }}
                  viewport={{ once: true }}
                >
                  <Check className="h-4 w-4 text-yellow-400 mr-2 flex-shrink-0" />
                  <span className="font-medium font-['Open_Sans']">{feature}</span>
                </motion.li>
              ))}
            </ul>
          </div>

          {/* View Details Button */}
          <Button
            onClick={onViewDetails}
            className="w-full mt-3 py-3 px-4 bg-gradient-to-r from-yellow-400 to-yellow-400 hover:from-yellow-500 hover:to-yellow-500 text-center font-semibold text-gray-900 rounded-lg transition-all duration-300 transform group-hover:-translate-y-1 flex items-center justify-center shadow-md text-sm md:text-base"
          >
            <span>View Details</span>
            <ChevronRight className="ml-1 h-4 w-4" />
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

// Contact Section Component
const ContactSection = ({ onContactClick }: { onContactClick: () => void }) => {
  return (
    <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 py-6 px-4 rounded-2xl shadow-lg font-['Open_Sans']">
      <div className="max-w-full mx-auto text-center">
        <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-black mb-3 font-['Open_Sans']">Need Expert Advice?</h2>
        <p className="text-black mb-6 max-w-4xl mx-auto font-semibold text-sm md:text-base lg:text-lg text-center font-['Open_Sans']">
          Our specialists provide comprehensive guidance on electrical measurement solutions for your testing needs
        </p>
        <Button
          className="inline-flex items-center px-6 md:px-8 py-3 md:py-4 bg-yellow-400 hover:bg-yellow-500 text-black font-semibold rounded-lg shadow-md transition duration-300 transform hover:-translate-y-1 text-sm md:text-base"
          onClick={onContactClick}
        >
          Contact Sales
          <ArrowRight className="ml-2 h-4 md:h-5 w-4 md:w-5" />
        </Button>
      </div>
    </div>
  );
};

// Combined Tab Component for Features and Measurements
const ProductTabContent = ({ activeProductType, activeTab }: { activeProductType: string; activeTab: string }) => {
  // Features data based on product type
  const features = {
    "CA 6240": [
      "Resistance range: 5μΩ to 399.9Ω",
      "Accuracy: ±0.25% ± 2 counts",
      "Test current: Up to 10A",
      "Large backlit LCD display",
      "Auto measurement mode",
      "Automatic \"on the fly\" or manual recording mode",
      "Auto power-off",
      "Memory: 100 measurements",
      "Communication: Optical/USB link",
      "PC interface"
    ],
    "CA 6255": [
      "Resistance range: 5mΩ to 2,500Ω",
      "Accuracy: ±0.25% ± 2 counts",
      "Test current: Up to 10A",
      "Large backlit LCD display",
      "Auto measurement mode",
      "Automatic discharge",
      "Auto power-off",
      "Memory: 1,500 measurements",
      "Communication: RS 232 link",
      "PC interface"
    ],
    "CA 6292": [
      "Resistance range: 0.1μΩ to 1Ω",
      "Accuracy: ±1%",
      "Test current: Up to 200A",
      "Display: Backlit LCD screen (4 lines of 20 characters)",
      "Internal cooling system",
      "Test mode: Normal or BSG (Both Sides Grounded)",
      "Memory: 8,000 measurements",
      "Communication: USB",
      "PC interface",
      "Protection: voltage surges, short-circuits, overheating & overvoltages"
    ]
  };

  // Measurements data based on product type
  const measurements = {
    "CA 6240": [
      { label: "Resistance Range", value: "5μΩ to 399.9Ω" },
      { label: "Accuracy", value: "±0.25% ± 2 counts" },
      { label: "Test Current", value: "Up to 10A" },
      { label: "Memory", value: "100 measurements" },
      { label: "Communication", value: "Optical/USB link" }
    ],
    "CA 6255": [
      { label: "Resistance Range", value: "5mΩ to 2,500Ω" },
      { label: "Accuracy", value: "±0.25% ± 2 counts" },
      { label: "Test Current", value: "Up to 10A" },
      { label: "Memory", value: "1,500 measurements" },
      { label: "Communication", value: "RS 232 link" }
    ],
    "CA 6292": [
      { label: "Resistance Range", value: "0.1μΩ to 1Ω" },
      { label: "Accuracy", value: "±1%" },
      { label: "Test Current", value: "Up to 200A" },
      { label: "Current (BSG mode)", value: "Range up to 50A (with optional MR6292 clamp)" },
      { label: "Memory", value: "8,000 measurements" }
    ]
  };

  if (activeTab === "features") {
    return (
      <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
        <div className="bg-gradient-to-r from-yellow-500 to-yellow-400 p-4">
          <h3 className="text-2xl font-bold text-center text-white">Salient Features</h3>
        </div>
        <div className="p-6">
          <div className="space-y-2">
            {features[activeProductType].map((feature, idx) => (
              <motion.div
                key={idx}
                initial={{ opacity: 0, x: -10 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: idx * 0.05 }}
                viewport={{ once: true }}
                className="flex items-start p-3 hover:bg-yellow-50 rounded-lg transition-colors duration-300"
              >
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-yellow-100 text-yellow-600 flex items-center justify-center mr-3">
                  <Check className="h-4 w-4" />
                </div>
                <span className="text-gray-800">{feature}</span>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    );
  } else {
    return (
      <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
        <div className="bg-gradient-to-r from-yellow-500 to-yellow-400 p-4">
          <h3 className="text-2xl font-bold text-center text-white">Measurements</h3>
        </div>
        <div className="p-6">
          <div className="grid md:grid-cols-2 gap-6">
            {measurements[activeProductType].map((item, idx) => (
              <motion.div
                key={idx}
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: idx * 0.05 }}
                viewport={{ once: true }}
                className="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow duration-300 border border-yellow-100"
              >
                <h4 className="font-semibold text-gray-900 mb-2 border-b border-yellow-200 pb-2">{item.label}</h4>
                <p className="text-gray-800 whitespace-pre-line">{item.value}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    );
  }
};

// Main MicroOhmmeters Component
const MicroOhmmeters = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState("overview");
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeProductType, setActiveProductType] = useState("CA 6240"); // Add this line

  // Effect to check URL parameters for initial tab
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const tab = params.get('tab');
    if (tab) setActiveTab(tab);
  }, [location]);

  // Handler for demo button
  const handleRequestDemo = () => {
    navigate("/contact/sales");
  };

  // Handler for View Brochure button
  const handleViewBrochure = () => {
    window.open(PDF_URL, '_blank');
  };

  // Handler for View Details button
  const handleViewDetails = (productType: string) => {
    setActiveTab('products');
    navigate(`?tab=products`, { replace: true });
    window.scrollTo(0, 0);
  };

  // Product data
  const productData = {
    "CA 6240": {
      model: "CA 6240",
      current: "10A",
      image: "/Ohm meters/6240-removebg-preview.png",
      clampingDiameter: "N/A",
      displayInfo: "Backlit LCD"
    },
    "CA 6255": {
      model: "CA 6255",
      current: "10A",
      image: "/Ohm meters/6240-removebg-preview.png",
      clampingDiameter: "N/A",
      displayInfo: "Backlit LCD"
    },
    "CA 6292": {
      model: "CA 6292",
      current: "200A",
      image: "/Ohm meters/6292-removebg-preview.png",
      clampingDiameter: "N/A",
      displayInfo: "Backlit LCD (4 lines of 20 characters)"
    }
  };

  // Get active product data
  const getActiveProductData = () => {
    return productData[activeProductType];
  };

  // Navigation tabs data
  const navTabs = [
    { id: "overview", label: "Overview", icon: <Gauge className="h-5 w-5" /> },
    { id: "products", label: "Products", icon: <Shield className="h-5 w-5" /> }
  ];

  return (
    <PageLayout
      title="Micro-Ohmmeters"
      subtitle=""
      category="measure"
    >
      <style>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
      <div className="font-['Open_Sans']">
      {/* Premium Modern Navigation Tabs - Responsive Design */}
      <div className="sticky top-0 z-30 bg-white shadow-lg border-b border-gray-100 font-['Open_Sans']">
        <div className="max-w-full mx-auto px-2">
          {/* Desktop Navigation */}
          <div className="hidden md:flex justify-center py-2">
            <div className="bg-gray-50 p-1.5 rounded-full flex shadow-sm">
              {navTabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => {
                    setActiveTab(tab.id);
                    navigate(`?tab=${tab.id}`, { replace: true });
                    setIsMobileMenuOpen(false);
                  }}
                  className={cn(
                    "relative px-6 py-2.5 font-medium rounded-full transition-all duration-300 flex items-center mx-1 overflow-hidden text-base",
                    activeTab === tab.id
                      ? "bg-gradient-to-r from-yellow-400 to-yellow-400 text-black shadow-md transform -translate-y-0.5"
                      : "text-gray-700 hover:bg-yellow-50 hover:text-yellow-500"
                  )}
                >
                  <div className="flex items-center relative z-10">
                    <span className="mr-2">{tab.icon}</span>
                    <span>{tab.label}</span>
                  </div>
                  {activeTab === tab.id && (
                    <motion.div
                      layoutId="navIndicator"
                      className="absolute inset-0 bg-gradient-to-r from-yellow-400 to-yellow-400 -z-0"
                      initial={false}
                      transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                    />
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Mobile Navigation */}
          <div className="md:hidden py-2 flex justify-between items-center">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="text-gray-700 hover:text-yellow-500 focus:outline-none"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            <span className="font-semibold text-gray-900 text-lg">
              {navTabs.find(tab => tab.id === activeTab)?.label}
            </span>

            <div className="w-6"></div> {/* Spacer for balanced layout */}
          </div>

          {/* Mobile Menu Dropdown */}
          <div className={`md:hidden overflow-hidden transition-all duration-300 ease-in-out ${isMobileMenuOpen ? 'max-h-60' : 'max-h-0'}`}>
            <div className="bg-white rounded-lg shadow-lg p-2 mb-2">
              {navTabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => {
                    setActiveTab(tab.id);
                    navigate(`?tab=${tab.id}`, { replace: true });
                    setIsMobileMenuOpen(false);
                  }}
                  className={`w-full text-left px-4 py-3 rounded-lg my-1 flex items-center ${
                    activeTab === tab.id
                      ? "bg-yellow-50 text-yellow-600 font-medium"
                      : "text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  <span className="mr-3">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <HeroSection
        onRequestDemo={handleRequestDemo}
        onViewBrochure={handleViewBrochure}
      />

      {/* No PDF Viewer Modal - Using direct link instead */}

      {/* Featured Points */}
      <div className="py-6 md:py-8 bg-gradient-to-br from-yellow-50 to-white font-['Open_Sans']">
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-6 md:mb-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-yellow-600 mb-3 font-['Open_Sans']">Why Choose Our Micro-Ohmmeters?</h2>
              <p className="mt-3 text-sm md:text-base lg:text-lg text-gray-800 max-w-4xl mx-auto font-medium text-center font-['Open_Sans']">
                Precision-engineered instruments that combine accuracy, durability, and advanced features for low resistance measurements
              </p>
            </motion.div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
            <FeatureHighlight
              icon={<Zap className="h-6 w-6 text-white" />}
              title="High Precision"
              description="Industry-leading measurement accuracy with ranges down to 0.1μΩ for the most demanding applications"
            />

            <FeatureHighlight
              icon={<Shield className="h-6 w-6 text-white" />}
              title="Robust Design"
              description="Built for reliability in field and laboratory environments with advanced protection features"
            />

            <FeatureHighlight
              icon={<Gauge className="h-6 w-6 text-white" />}
              title="Advanced Features"
              description="Temperature compensation, data storage capabilities, and versatile connectivity options"
            />
          </div>
        </div>
      </div>

      {activeTab === "overview" && (
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6 md:py-8 font-['Open_Sans']">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-6 md:mb-8"
          >
            <span className="inline-block bg-yellow-100 text-yellow-800 px-4 py-1 rounded-full text-sm md:text-base font-semibold mb-3 font-['Open_Sans']">
              PROFESSIONAL SERIES
            </span>
            <h2 className="text-xl md:text-2xl lg:text-4xl font-bold mb-4 text-gray-900 font-['Open_Sans']">
              Our Micro-Ohmmeter Range
            </h2>
            <p className="max-w-4xl mx-auto text-gray-800 text-sm md:text-base lg:text-lg font-medium text-center font-['Open_Sans']">
              Precision instruments designed for accurate low resistance measurements across various industrial applications
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
            {[
              {
                model: "CA 6240",
                current: "10A",
                features: ["5μΩ to 399.9Ω range", "Auto/manual mode", "100 measurements memory"],
                image: "/Ohm meters/6240-removebg-preview.png ",
              },
              {
                model: "CA 6255",
                current: "10A",
                features: ["5mΩ to 2,500Ω range", "Automatic discharge", "1,500 measurements memory"],
                image: "/Ohm meters/6240-removebg-preview.png",
              },
              {
                model: "CA 6292",
                current: "200A",
                features: ["0.1μΩ to 1Ω range", "Internal cooling system", "8,000 measurements memory"],
                image: "/Ohm meters/6292-removebg-preview.png",
              },
            ].map((product, index) => (
              <ProductOverviewCard
                key={index}
                model={product.model}
                current={product.current}
                features={product.features}
                image={product.image}
                onViewDetails={() => {
                  setActiveTab("products");
                  navigate(`?tab=products`, { replace: true });
                }}
              />
            ))}
          </div>          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="mt-16 relative bg-white rounded-3xl overflow-hidden"
          >
            {/* Background Elements */}
            <div className="absolute inset-0 bg-[#FFFF00]/5"></div>
            <div className="absolute top-0 left-0 w-32 h-32 bg-[#FFFF00]/10 rounded-full blur-3xl"></div>
            <div className="absolute bottom-0 right-0 w-32 h-32 bg-[#FFFF00]/10 rounded-full blur-3xl"></div>

            <div className="relative px-8 py-12">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="text-center mb-12"
              >
                <h3 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-gray-900 to-gray-600 mb-4">
                  Key Applications
                </h3>
                <div className="w-24 h-1 bg-[#FFFF00] mx-auto rounded-full mb-2"></div>
              </motion.div>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 relative">
                {/* Gradient background */}
                <div className="absolute inset-0 bg-gradient-to-br from-[#FFFF00]/5 to-transparent rounded-3xl"></div>
                  {[
                  {
                    title: "Circuit Breaker Testing",
                    description: "Accurate resistance measurements for circuit breaker contacts and connections",
                    icon: "⚡"
                  },
                  {
                    title: "Transformer Testing",
                    description: "Precise winding resistance measurements for transformer maintenance",
                    icon: "🔌"
                  },
                  {
                    title: "Motor Winding Analysis",
                    description: "Detailed analysis of motor winding resistance and connections",
                    icon: "⚙️"
                  },
                  {
                    title: "Rail & Cable Testing",
                    description: "Quality assessment of rail bonds and cable joint resistance",
                    icon: "🔧"
                  },
                  {
                    title: "Welding Quality Control",
                    description: "Verify weld quality through resistance measurements",
                    icon: "🛠️"
                  },
                  {
                    title: "Power Connections",
                    description: "Ensure optimal conductivity in power distribution systems",
                    icon: "🔋"
                  }
                ].map((application, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="relative bg-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group"
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-[#FFFF00]/5 via-transparent to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative z-10">
                    <span className="text-4xl mb-4 block">{application.icon}</span>
                    <h4 className="text-xl font-semibold text-gray-900 mb-2">{application.title}</h4>
                    <p className="text-gray-600">{application.description}</p>
                  </div>
                </motion.div>
              ))}
              </div>
            </div>
          </motion.div>

          {/* Contact Section at the bottom of overview */}
          <div className="mt-8 md:mt-12">
            <ContactSection onContactClick={handleRequestDemo} />
          </div>
        </div>
      )}

      {activeTab === "products" && (
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6 md:py-8 font-['Open_Sans']">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-6 md:mb-8"
          >
            <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-gray-900 mb-3 font-['Open_Sans']">
              Detailed Product Specifications
            </h2>
            <p className="text-gray-800 max-w-4xl mx-auto mb-6 font-medium text-sm md:text-base lg:text-lg text-center font-['Open_Sans']">
              Explore our precision micro-ohmmeters with industry-leading features and specifications
            </p>
          </motion.div>

          <div className="space-y-12">
            <ProductCard              title="10A Micro-Ohmmeters"
              subtitle="CA 6240/CA 6255"
              image="/Ohm meters/6240-removebg-preview.png"
              features={[
                "Large backlit LCD display",
                "Auto measurement mode",
                "Automatic \"on the fly\" or manual recording mode",
                "Auto power-off",
                "Automatic discharge (CA 6255)",
                "Memory: 100 measurements (CA 6240)",
                "Memory: 1,500 measurements (CA 6255)",
                "Communication: Optical/USB link (CA 6240)",
                "Communication: RS 232 link (CA 6255)",
                "PC interface"
              ]}
              specs={[
                "Resistance: 5μΩ to 399.9Ω (CA 6240)",
                "Resistance: 5mΩ to 2,500Ω (CA 6255)",
                "Accuracy: ±0.25% ±2 counts",
                "Test current: Range up to 10A"
              ]}
              onRequestDemo={handleRequestDemo}
            />

            <ProductCard
              title="200A Micro-Ohmmeters"
              subtitle="CA 6292"
              image="/Ohm meters/6292-removebg-preview.png"
              features={[
                "Display: Backlit LCD screen (4 lines of 20 characters)",
                "Internal cooling system",
                "Test mode: Normal or BSG (Both Sides Grounded)",
                "Memory: 8,000 measurements",
                "Communication: USB",
                "PC interface",
                "Protection: voltage surges, short-circuits, overheating & overvoltages on the o/p terminals"
              ]}
              specs={[
                "Resistance: Range 0.1 μΩ to 1 Ω",
                "Accuracy: ±1%",
                "Test Current: Range up to 200A",
                "Current (optional MR6292 clamp (BSG mode)): Range up to 50A"
              ]}
              onRequestDemo={handleRequestDemo}
            />

            <ComparisonTable />
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            viewport={{ once: true }}
            className="mt-20"
          >
            <ContactSection onContactClick={handleRequestDemo} />
          </motion.div>
        </div>
      )}
      </div>
    </PageLayout>
  );
};

export default MicroOhmmeters;