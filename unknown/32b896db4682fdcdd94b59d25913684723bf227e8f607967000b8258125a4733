import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ChevronRight, Download, MessageCircle } from 'lucide-react';

// Import components
import PageLayout from '@/components/layout/PageLayout';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
// import PdfViewer from "@/components/ui/pdf-viewer";

const PowerQualityMonitorProduct1 = () => {
  return (
    <PageLayout
      title="DIGI 820 Power Quality Monitor"
      subtitle="Class A certified power quality monitor with advanced measurement capabilities"
      category="measure"
    >
      {/* Secondary Navigation Bar */}
      <div className="bg-background border-b border-border">
        <div className="container mx-auto">
          <div className="flex items-center overflow-x-auto whitespace-nowrap py-2 px-4">
            <Link to="/" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
              Home
            </Link>
            <ChevronRight className="h-4 w-4 mx-1 text-muted-foreground flex-shrink-0" />
            <Link to="/measure" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
              Measure
            </Link>
            <ChevronRight className="h-4 w-4 mx-1 text-muted-foreground flex-shrink-0" />
            <Link to="/measure/power-quality-monitors" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
              Power Quality Monitors
            </Link>
            <ChevronRight className="h-4 w-4 mx-1 text-muted-foreground flex-shrink-0" />
            <span className="text-sm font-medium">DIGI 820</span>
          </div>
        </div>
      </div>

      {/* Product Navigation Tabs */}
      <div className="bg-background border-b border-border mb-8 sticky top-16 z-30">
        <div className="container mx-auto">
          <div className="flex justify-between items-center overflow-x-auto whitespace-nowrap">
            <div className="flex">
              <Link 
                to="/measure/power-quality-monitors/digi-820" 
                className="py-3 px-4 border-b-2 border-primary font-medium text-sm"
              >
                Overview
              </Link>
              <Link 
                to="#" 
                className="py-3 px-4 border-b-2 border-transparent hover:border-primary/50 text-muted-foreground hover:text-foreground transition-colors text-sm"
              >
                Datasheet
              </Link>
              <Link 
                to="#" 
                className="py-3 px-4 border-b-2 border-transparent hover:border-primary/50 text-muted-foreground hover:text-foreground transition-colors text-sm"
              >
                Applications
              </Link>
              <Link 
                to="#" 
                className="py-3 px-4 border-b-2 border-transparent hover:border-primary/50 text-muted-foreground hover:text-foreground transition-colors text-sm"
              >
                Support
              </Link>
            </div>
            <div className="hidden md:flex space-x-3">
              <Button size="sm" variant="default" asChild className="bg-blue-600 hover:bg-blue-700">
                <Link to="/contact" className="flex items-center space-x-2">
                  <MessageCircle size={16} />
                  <span>Contact Sales</span>
                </Link>
              </Button>
              <Button size="sm" variant="outline" className="border-blue-200 text-blue-700 hover:bg-blue-50" asChild>
                <Link to="#" className="flex items-center space-x-2">
                  <Download size={16} />
                  <span>Download Brochure</span>
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Product Section */}
      <section className="mb-16">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center"
        >
          <div className="bg-gradient-to-b from-blue-50 to-white p-8 rounded-2xl shadow-lg overflow-hidden">
            <motion.div
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 300 }}
              className="flex justify-center"
            >
              <img 
                src="/api/placeholder/400/400" 
                alt="DIGI 820 Power Quality Monitor" 
                className="w-full max-w-md object-contain"
              />
            </motion.div>
          </div>
          
          <div className="space-y-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <div className="flex flex-wrap gap-3 mb-4">
                <span className="px-3 py-1 rounded-full bg-blue-100 text-blue-700 text-xs font-medium shadow-sm">Class A Certified</span>
                <span className="px-3 py-1 rounded-full bg-green-100 text-green-700 text-xs font-medium shadow-sm">In Stock</span>
                <span className="px-3 py-1 rounded-full bg-purple-100 text-purple-700 text-xs font-medium shadow-sm">New Release</span>
              </div>
              
              <h2 className="text-3xl font-bold mb-2 text-gray-900">
                DIGI 820 Power Quality Analyzer
              </h2>
              
              <div className="text-sm text-gray-500 mb-6">
                <p>Model: DIGI-820-01 | Reference: PQA-D820-01</p>
              </div>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="bg-blue-50 p-6 rounded-xl border border-blue-100"
            >
              <h3 className="text-lg font-semibold mb-4 text-blue-800">KEY FEATURES:</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-3">
                {[
                  "Advanced Harmonic Analysis", 
                  "Multi-Channel Voltage Monitor", 
                  "3-Phase Current Analysis",
                  "Comprehensive Power Quality", 
                  "Disturbance Capture & Analysis", 
                  "Transient Detection (IEC Compliant)",
                  "High-Speed RMS Recording", 
                  "Voltage/Current Unbalance", 
                  "Flicker Measurement",
                  "Energy Consumption Analysis"
                ].map((feature, index) => (
                  <div key={index} className="flex items-center">
                    <span className="h-2 w-2 rounded-full bg-blue-500 mr-2 flex-shrink-0"></span>
                    <span className="text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>
            </motion.div>
            
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
              className="py-4 px-5 bg-blue-50 rounded-lg border-l-4 border-blue-500"
            >
              <p className="text-blue-800 text-sm">
                <strong>Limited Time Offer:</strong> Free calibration, setup, and 1-year extended warranty with purchase before end of quarter. Contact sales for details.
              </p>
            </motion.div>
            
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.8 }}
              className="flex flex-wrap gap-4"
            >
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700" asChild>
                <Link to="/contact">Request Quote</Link>
              </Button>
              <Button size="lg" variant="outline" className="border-blue-200 text-blue-700 hover:bg-blue-50">
                Download Specifications
              </Button>
              <Button size="lg" variant="ghost" className="text-blue-600 hover:bg-blue-50" asChild>
                <Link to="/contact/sales">Schedule Demo</Link>
              </Button>
            </motion.div>
          </div>
        </motion.div>
      </section>
      
      {/* Key Metrics Section */}
      <section className="mb-16">
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-2xl shadow-xl overflow-hidden text-white p-8 mb-12">
          <h2 className="text-2xl font-bold mb-8 text-center">Industry-Leading Performance</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-4xl font-bold text-white">99.9%</div>
              <div className="text-sm text-blue-100 mt-2">Measurement Reliability</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-white">1024</div>
              <div className="text-sm text-blue-100 mt-2">Samples per Cycle</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-white">±0.1%</div>
              <div className="text-sm text-blue-100 mt-2">Reading Accuracy</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-white">50</div>
              <div className="text-sm text-blue-100 mt-2">Harmonic Orders</div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Tabs Section */}
      <section className="mb-16">
        <Tabs defaultValue="measurements" className="w-full">
          <TabsList className="w-full justify-start border-b rounded-none bg-transparent h-auto p-0">
            <TabsTrigger 
              value="measurements" 
              className="rounded-none border-b-2 data-[state=active]:border-primary border-transparent px-6 py-3 h-auto"
            >
              Measurements
            </TabsTrigger>
            <TabsTrigger 
              value="functions" 
              className="rounded-none border-b-2 data-[state=active]:border-primary border-transparent px-6 py-3 h-auto"
            >
              Functions
            </TabsTrigger>
            <TabsTrigger 
              value="interface" 
              className="rounded-none border-b-2 data-[state=active]:border-primary border-transparent px-6 py-3 h-auto"
            >
              Interface
            </TabsTrigger>
            <TabsTrigger 
              value="specifications" 
              className="rounded-none border-b-2 data-[state=active]:border-primary border-transparent px-6 py-3 h-auto"
            >
              Specifications
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="measurements" className="pt-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="col-span-2">
                <h3 className="text-xl font-bold mb-6 text-blue-800">
                  <span style={{ color: "#FFFF00" }}>Complete</span> Measurement Capabilities
                </h3>
                <p className="text-gray-600 mb-6">
                  The DIGI 820 provides comprehensive measurement capabilities designed to identify and analyze 
                  a wide range of power quality issues in electrical systems.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-white shadow-md p-5 rounded-lg border border-blue-100">
                    <h4 className="font-semibold mb-3 text-blue-700">Voltage & Current</h4>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-blue-500 mt-1.5 mr-2 flex-shrink-0"></span>
                        <span className="text-gray-700">Voltage (L-N, L-L range/resolution)</span>
                      </li>
                      <li className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-blue-500 mt-1.5 mr-2 flex-shrink-0"></span>
                        <span className="text-gray-700">Current (range/resolution)</span>
                      </li>
                      <li className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-blue-500 mt-1.5 mr-2 flex-shrink-0"></span>
                        <span className="text-gray-700">Frequency</span>
                      </li>
                    </ul>
                  </div>
                  <div className="bg-white shadow-md p-5 rounded-lg border border-blue-100">
                    <h4 className="font-semibold mb-3 text-blue-700">Power Measurements</h4>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-blue-500 mt-1.5 mr-2 flex-shrink-0"></span>
                        <span className="text-gray-700">Active, Reactive & Apparent with 4 quadrants</span>
                      </li>
                      <li className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-blue-500 mt-1.5 mr-2 flex-shrink-0"></span>
                        <span className="text-gray-700">Power factor</span>
                      </li>
                    </ul>
                  </div>
                  <div className="bg-white shadow-md p-5 rounded-lg border border-blue-100">
                    <h4 className="font-semibold mb-3 text-blue-700">Harmonics & Quality</h4>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-blue-500 mt-1.5 mr-2 flex-shrink-0"></span>
                        <span className="text-gray-700">Total Harmonic Distortion (THD, K-factor, CF)</span>
                      </li>
                      <li className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-blue-500 mt-1.5 mr-2 flex-shrink-0"></span>
                        <span className="text-gray-700">Harmonics (up to 50th order)</span>
                      </li>
                    </ul>
                  </div>
                  <div className="bg-white shadow-md p-5 rounded-lg border border-blue-100">
                    <h4 className="font-semibold mb-3 text-blue-700">Events & Disturbances</h4>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-blue-500 mt-1.5 mr-2 flex-shrink-0"></span>
                        <span className="text-gray-700">Sag/swell detection (per IEC limits)</span>
                      </li>
                      <li className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-blue-500 mt-1.5 mr-2 flex-shrink-0"></span>
                        <span className="text-gray-700">Unbalance (Voltage, Current, & Symmetrical components)</span>
                      </li>
                      <li className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-blue-500 mt-1.5 mr-2 flex-shrink-0"></span>
                        <span className="text-gray-700">Flicker (Per IEC Standards)</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
              <div className="flex flex-col items-center space-y-6">
                <div className="bg-white p-4 rounded-lg shadow-lg w-full border border-blue-100">
                  <img 
                    src="/api/placeholder/300/200" 
                    alt="Measurement Diagram" 
                    className="w-full h-auto mb-4 rounded"
                  />
                  <h4 className="text-center font-medium text-sm text-blue-700">Advanced Measurement Technology</h4>
                </div>
                <div className="bg-blue-50 p-5 rounded-lg w-full">
                  <h4 className="font-semibold mb-2 text-blue-800">Key Advantages</h4>
                  <ul className="space-y-3 text-sm">
                    <li className="flex items-start">
                      <span className="text-blue-600 font-bold mr-2">✓</span>
                      <span className="text-gray-700">High accuracy: ±0.1% reading</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-600 font-bold mr-2">✓</span>
                      <span className="text-gray-700">Fast sampling: 1024 samples/cycle</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-600 font-bold mr-2">✓</span>
                      <span className="text-gray-700">IEC 61000-4-30 Class A certified</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-600 font-bold mr-2">✓</span>
                      <span className="text-gray-700">Wide measurement range</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="functions" className="pt-8">
            <h3 className="text-xl font-bold mb-6 text-blue-800">Advanced Functionality</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-10">
              <div className="bg-white rounded-lg shadow-lg border border-blue-100 p-6 hover:shadow-xl transition-shadow duration-300">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4 flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-700"><rect width="18" height="18" x="3" y="3" rx="2"/><circle cx="9" cy="9" r="2"/><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/></svg>
                  </div>
                  <h4 className="text-lg font-semibold text-gray-800">Class A Power Quality Measurement</h4>
                </div>
                <p className="text-gray-600">
                  Complies with international standards including IEC 61000-4-30 Class A for voltage, current, 
                  harmonics, flicker, frequency, and power, providing industry-standard measurement.
                </p>
                <div className="h-1 w-full bg-blue-400 rounded-full mt-4"></div>
              </div>
              
              <div className="bg-white rounded-lg shadow-lg border border-blue-100 p-6 hover:shadow-xl transition-shadow duration-300">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mr-4 flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-green-700"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"/><path d="m9 12 2 2 4-4"/></svg>
                  </div>
                  <h4 className="text-lg font-semibold text-gray-800">Class 0.2s Accuracy</h4>
                </div>
                <p className="text-gray-600">
                  Achieves high accuracy meeting IEC 62053-22 Class 0.2s performance ratings, ensuring reliable
                  and precise measurements for critical applications.
                </p>
                <div className="h-1 w-full bg-green-400 rounded-full mt-4"></div>
              </div>
              
              <div className="bg-white rounded-lg shadow-lg border border-blue-100 p-6 hover:shadow-xl transition-shadow duration-300">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mr-4 flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-purple-700"><path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/><path d="M3 3v5h5"/><path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16"/><path d="M16 16h5v5"/></svg>
                  </div>
                  <h4 className="text-lg font-semibold text-gray-800">High Resolution Failure Record</h4>
                </div>
                <p className="text-gray-600">
                  Captures critical power quality information at microsecond-level time resolution for specific data,
                  allowing for detailed analysis of transient events.
                </p>
                <div className="h-1 w-full bg-purple-400 rounded-full mt-4"></div>
              </div>
              
              <div className="bg-white rounded-lg shadow-lg border border-blue-100 p-6 hover:shadow-xl transition-shadow duration-300">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 rounded-full bg-amber-100 flex items-center justify-center mr-4 flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-amber-700"><path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"/><path d="M5 3v4"/><path d="M19 17v4"/><path d="M3 5h4"/><path d="M17 19h4"/></svg>
                  </div>
                  <h4 className="text-lg font-semibold text-gray-800">Failure Diagnosis Location</h4>
                </div>
                <p className="text-gray-600">
                  Enables precise root cause identification by detailed waveform capture and event tracking with high time resolution,
                  reducing troubleshooting time and effort.
                </p>
                <div className="h-1 w-full bg-amber-400 rounded-full mt-4"></div>
              </div>
            </div>
            
            <div className="bg-blue-50 p-8 rounded-xl shadow-lg border border-blue-100">
              <h4 className="font-semibold mb-6 text-blue-800 text-lg">Real-time Measurement & Analysis</h4>
              <p className="mb-8 text-gray-700">
                The DIGI 820 continuously monitors all power quality parameters in real-time, providing instant feedback
                on the status of your electrical system. This allows for immediate detection of issues before they can
                cause equipment damage or operational disruptions.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-white p-6 rounded-xl shadow-md border border-blue-100 hover:shadow-lg transition-shadow duration-300">
                  <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-600">
                      <circle cx="12" cy="12" r="10"/><polyline points="12 6 12 12 16 14"/>
                    </svg>
                  </div>
                  <h5 className="font-medium text-blue-800 mb-2">Continuous Monitoring</h5>
                  <p className="text-sm text-gray-600">
                    24/7 monitoring with no interruptions, ensuring complete coverage of all power events with high-resolution capture.
                  </p>
                </div>
                
                <div className="bg-white p-6 rounded-xl shadow-md border border-blue-100 hover:shadow-lg transition-shadow duration-300">
                  <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-600">
                      <path d="M10.29 3.86 1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                      <line x1="12" y1="9" x2="12" y2="13"/><line x1="12" y1="17" x2="12.01" y2="17"/>
                    </svg>
                  </div>
                  <h5 className="font-medium text-blue-800 mb-2">Instant Alerts</h5>
                  <p className="text-sm text-gray-600">
                    Configurable alarm thresholds with multiple notification options for immediate response to critical events.
                  </p>
                </div>
                
                <div className="bg-white p-6 rounded-xl shadow-md border border-blue-100 hover:shadow-lg transition-shadow duration-300">
                  <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-600">
                      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><polyline points="14 2 14 8 20 8"/>
                      <line x1="16" y1="13" x2="8" y2="13"/><line x1="16" y1="17" x2="8" y2="17"/><polyline points="10 9 9 9 8 9"/>
                    </svg>
                  </div>
                  <h5 className="font-medium text-blue-800 mb-2">Comprehensive Logging</h5>
                  <p className="text-sm text-gray-600">
                    Detailed data logging with configurable intervals for trend analysis,reporting, and regulatory compliance.
                  </p>
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="interface" className="pt-8">
            <h3 className="text-xl font-bold mb-6 text-blue-800">Intuitive User Interface</h3>
            <p className="text-gray-600 mb-8">
              The DIGI 820 features a user-friendly interface designed for easy navigation and quick access to critical information.
              The touch-enabled display provides clear visualization of measurement data and system status.
            </p>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 mb-12">
              {[1, 2, 3, 4, 5, 6].map((item) => (
                <motion.div 
                  key={item} 
                  className="bg-white border border-blue-100 rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300"
                  whileHover={{ y: -5 }}
                >
                  <div className="relative overflow-hidden">
                    <img 
                      src="/api/placeholder/400/300" 
                      alt={`Interface Screenshot ${item}`} 
                      className="w-full h-auto"
                    />
                    <div className="absolute inset-0 bg-gradient-to-tr from-blue-600/0 to-blue-600/0 hover:from-blue-600/20 hover:to-blue-600/20 transition-all duration-300">
                      <div className="flex items-center justify-center h-full text-white font-medium opacity-0 hover:opacity-100 transition-opacity duration-300">
                        <span>View Details</span>
                      </div>
                    </div>
                  </div>
                  <div className="p-4">
                    <h4 className="font-medium text-blue-800 mb-1">{
                      item === 1 ? "Main Dashboard" :
                      item === 2 ? "Harmonic Analysis" :
                      item === 3 ? "Event Log" :
                      item === 4 ? "Waveform Capture" :
                      item === 5 ? "Settings Panel" :
                      "Report Generation"
                    }</h4>
                    <p className="text-xs text-gray-600">
                      {item === 1 && "Real-time measurements and overview of system status"}
                      {item === 2 && "Detailed harmonic analysis with interactive graphical representation"}
                      {item === 3 && "Comprehensive event log with detailed timestamps and values"}
                      {item === 4 && "High-resolution waveform capture with advanced zoom capabilities"}
                      {item === 5 && "Intuitive settings and configuration interface"}
                      {item === 6 && "Customizable report generation and export options"}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
            
            <div className="bg-blue-50 p-8 rounded-xl shadow-lg border border-blue-100">
              <h4 className="text-lg font-semibold mb-6 text-blue-800">Software Integration</h4>
              <p className="mb-8 text-gray-700">
                The DIGI 820 comes with powerful software that extends its capabilities beyond the device itself,
                allowing for comprehensive data analysis and reporting on your computer or cloud infrastructure.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="flex items-start">
                  <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-700"><path d="M3 3v18h18"/><path d="m19 9-5 5-4-4-3 3"/></svg>
                  </div>
                  <div>
                    <h5 className="font-medium text-blue-800 mb-2">Advanced Data Analysis</h5>
                    <p className="text-gray-600">
                      Perform detailed trend analysis, statistical calculations, and create customizable graphs for comprehensive understanding of your power quality situation.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-700"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/></svg>
                  </div>
                  <div>
                    <h5 className="font-medium text-blue-800 mb-2">Customizable Reports</h5>
                    <p className="text-gray-600">
                      Generate professional reports with your company branding for clients or regulatory compliance, with flexible templates and formatting options.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-700"><path d="M12 12V5"/><path d="m9 8 3-3 3 3"/><path d="M7 20h1a3 3 0 0 0 3-3v-2a3 3 0 0 1 3-3h1"/><path d="M5 16a4 4 0 1 0 0-8"/><path d="M19 8a4 4 0 1 0-8 0"/></svg>
                  </div>
                  <div>
                    <h5 className="font-medium text-blue-800 mb-2">Versatile Data Export</h5>
                    <p className="text-gray-600">
                      Export data in multiple industry-standard formats (CSV, PQDIF, Excel) for use with other analysis tools and enterprise systems.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-700"><path d="M13 3H4a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-3"/><path d="M8 21h8"/><path d="M12 17v4"/><path d="m17 8 5-5"/><path d="M17 3v5h5"/></svg>
                  </div>
                  <div>
                    <h5 className="font-medium text-blue-800 mb-2">Seamless Remote Access</h5>
                    <p className="text-gray-600">
                      Access your device remotely via Ethernet, Wi-Fi, or cellular connections for configuration, real-time monitoring, and data retrieval.
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="mt-8 flex justify-center">
                <Button className="bg-blue-600 hover:bg-blue-700">
                  Request Software Demo
                </Button>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="specifications" className="pt-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2 space-y-8">
                <div>
                  <h3 className="text-xl font-bold mb-6 text-blue-800">Technical Specifications</h3>
                  <p className="text-gray-600 mb-8">
                    The DIGI 820 Power Quality Monitor offers industry-leading specifications that ensure accurate measurement and analysis of power quality issues.
                  </p>
                </div>
                
                <div className="bg-white rounded-xl shadow-lg border border-blue-100 overflow-hidden">
                  <div className="p-6">
                    <h4 className="text-lg font-medium mb-4 pb-2 border-b border-blue-100 text-blue-800">Input Specifications</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-2">
                      <div className="py-2 grid grid-cols-2">
                        <div className="font-medium text-sm text-gray-800">Voltage inputs</div>
                        <div className="text-sm text-gray-600">4 channels, 1-1000V AC</div>
                      </div>
                      <div className="py-2 grid grid-cols-2">
                        <div className="font-medium text-sm text-gray-800">Current inputs</div>
                        <div className="text-sm text-gray-600">4 channels, flexible</div>
                      </div>
                      <div className="py-2 grid grid-cols-2">
                        <div className="font-medium text-sm text-gray-800">Frequency range</div>
                        <div className="text-sm text-gray-600">42.5 - 69 Hz</div>
                      </div>
                      <div className="py-2 grid grid-cols-2">
                        <div className="font-medium text-sm text-gray-800">Sampling rate</div>
                        <div className="text-sm text-gray-600">1024 samples/cycle</div>
                      </div>
                      <div className="py-2 grid grid-cols-2">
                        <div className="font-medium text-sm text-gray-800">Resolution</div>
                        <div className="text-sm text-gray-600">24-bit ADC</div>
                      </div>
                      <div className="py-2 grid grid-cols-2">
                        <div className="font-medium text-sm text-gray-800">Accuracy</div>
                        <div className="text-sm text-gray-600">±0.1% reading + 0.02% range</div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="bg-white rounded-xl shadow-lg border border-blue-100 overflow-hidden">
                  <div className="p-6">
                    <h4 className="text-lg font-medium mb-4 pb-2 border-b border-blue-100 text-blue-800">Measurement Capabilities</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-2">
                      <div className="py-2 grid grid-cols-2">
                        <div className="font-medium text-sm text-gray-800">Harmonics</div>
                        <div className="text-sm text-gray-600">Up to 50th order</div>
                      </div>
                      <div className="py-2 grid grid-cols-2">
                        <div className="font-medium text-sm text-gray-800">Interharmonics</div>
                        <div className="text-sm text-gray-600">Up to 49th order</div>
                      </div>
                      <div className="py-2 grid grid-cols-2">
                        <div className="font-medium text-sm text-gray-800">Transient capture</div>
                        <div className="text-sm text-gray-600">Down to 20 µs</div>
                      </div>
                      <div className="py-2 grid grid-cols-2">
                        <div className="font-medium text-sm text-gray-800">Flicker</div>
                        <div className="text-sm text-gray-600">Pst, Plt per IEC 61000-4-15</div>
                      </div>
                      <div className="py-2 grid grid-cols-2">
                        <div className="font-medium text-sm text-gray-800">Unbalance</div>
                        <div className="text-sm text-gray-600">Per IEC 61000-4-30</div>
                      </div>
                      <div className="py-2 grid grid-cols-2">
                        <div className="font-medium text-sm text-gray-800">Event detection</div>
                        <div className="text-sm text-gray-600">Dips, swells, interruptions</div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="bg-white rounded-xl shadow-lg border border-blue-100 overflow-hidden">
                  <div className="p-6">
                    <h4 className="text-lg font-medium mb-4 pb-2 border-b border-blue-100 text-blue-800">General Specifications</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-2">
                      <div className="py-2 grid grid-cols-2">
                        <div className="font-medium text-sm text-gray-800">Display</div>
                        <div className="text-sm text-gray-600">7" color touchscreen</div>
                      </div>
                      <div className="py-2 grid grid-cols-2">
                        <div className="font-medium text-sm text-gray-800">Memory</div>
                        <div className="text-sm text-gray-600">32GB internal flash</div>
                      </div>
                      <div className="py-2 grid grid-cols-2">
                        <div className="font-medium text-sm text-gray-800">Communication</div>
                        <div className="text-sm text-gray-600">Ethernet, USB, RS-485</div>
                      </div>
                      <div className="py-2 grid grid-cols-2">
                        <div className="font-medium text-sm text-gray-800">Protocols</div>
                        <div className="text-sm text-gray-600">Modbus-TCP, PQDIF, HTTP</div>
                      </div>
                      <div className="py-2 grid grid-cols-2">
                        <div className="font-medium text-sm text-gray-800">Power supply</div>
                        <div className="text-sm text-gray-600">90-264 VAC, 47-63 Hz</div>
                      </div>
                      <div className="py-2 grid grid-cols-2">
                        <div className="font-medium text-sm text-gray-800">Dimensions</div>
                        <div className="text-sm text-gray-600">292 x 232 x 74 mm</div>
                      </div>
                      <div className="py-2 grid grid-cols-2">
                        <div className="font-medium text-sm text-gray-800">Weight</div>
                        <div className="text-sm text-gray-600">2.2 kg</div>
                      </div>
                      <div className="py-2 grid grid-cols-2">
                        <div className="font-medium text-sm text-gray-800">Operating temp</div>
                        <div className="text-sm text-gray-600">-10°C to +55°C</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="space-y-6">
                <div className="bg-white p-6 rounded-xl shadow-lg border border-blue-100">
                  <h4 className="text-lg font-semibold mb-4 text-blue-800">What's Included</h4>
                  <ul className="space-y-3">
                    {[
                      "DIGI 820 Power Quality Analyzer",
                      "4 Voltage test leads with clips",
                      "4 Flexible current probes (3000A)",
                      "Power supply adapter",
                      "USB cable and Ethernet cable",
                      "Analysis software license",
                      "Rugged carrying case",
                      "Calibration certificate",
                      "User manual and quick start guide"
                    ].map((item, index) => (
                      <motion.li 
                        key={index}
                        className="flex items-start"
                        initial={{ opacity: 0, x: -5 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3, delay: 0.05 * index }}
                      >
                        <span className="text-blue-600 font-bold mr-2">✓</span>
                        <span className="text-gray-700 text-sm">{item}</span>
                      </motion.li>
                    ))}
                  </ul>
                </div>
                
                <div className="bg-blue-50 p-6 rounded-xl shadow-lg border border-blue-100">
                  <h4 className="font-semibold mb-4 text-blue-800">Optional Accessories</h4>
                  <ul className="space-y-3">
                    {[
                      "High precision current clamps",
                      "Extended warranty package",
                      "Advanced software module",
                      "GPS time synchronization module",
                      "Wi-Fi connectivity module"
                    ].map((item, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-blue-600 mr-2">+</span>
                        <span className="text-gray-700 text-sm">{item}</span>
                      </li>
                    ))}
                  </ul>
                  <div className="mt-4">
                    <Button variant="link" size="sm" className="p-0 text-blue-600 hover:text-blue-800">
                      View all accessories →
                    </Button>
                  </div>
                </div>
                
                <div className="bg-blue-600 p-6 rounded-xl shadow-lg text-white">
                  <h4 className="font-semibold mb-4">Need help choosing?</h4>
                  <p className="text-sm text-blue-100 mb-4">
                    Our product specialists can help you select the right power quality monitor for your specific requirements.
                  </p>
                  <Button size="sm" className="w-full bg-white text-blue-700 hover:bg-blue-50" asChild>
                    <Link to="/contact">Contact Sales Team</Link>
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </section>
      
      {/* CTA Section */}
      <section>
        <div className="relative overflow-hidden rounded-2xl shadow-2xl">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-indigo-700"></div>
          <div className="absolute inset-0 bg-[url('/api/placeholder/1920/600')] bg-cover bg-center mix-blend-overlay"></div>
          
          <div className="relative z-10 text-center px-6 py-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">Ready to improve your power quality?</h2>
              <p className="text-blue-100 mb-8 max-w-2xl mx-auto">
                The DIGI 820 Power Quality Monitor provides industry-leading analysis capabilities to help you 
                identify and resolve power quality issues before they cause costly downtime.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <Button size="lg" variant="outline" className="bg-white text-blue-700 border-white hover:bg-blue-50 hover:text-blue-800" asChild>
                  <Link to="/contact">Request a Quote</Link>
                </Button>
                <Button size="lg" variant="ghost" className="text-white border-white hover:bg-white/20" asChild>
                  <Link to="/measure/power-quality-monitors">Compare Models</Link>
                </Button>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </PageLayout>
  );
};

export default PowerQualityMonitorProduct1;