import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import PageLayout from "@/components/layout/PageLayout";
import { motion, AnimatePresence } from 'framer-motion';

interface Specification {
  parameter: string;
  value: string;
}

const LeakageClampsPage: React.FC = () => {
  const location = useLocation();
  const [isSpecsVisible, setIsSpecsVisible] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<'features' | 'specs'>('features');
  
  // Setup animation effects
  useEffect(() => {
    // Animation timing for specs section
    setTimeout(() => {
      setIsSpecsVisible(true);
    }, 500);
    
    // Add the float animation styles
    const styleSheet = document.createElement("style");
    styleSheet.textContent = `
      @keyframes float {
        0% { transform: translateY(0px) translateX(0px); }
        25% { transform: translateY(-10px) translateX(5px); }
        50% { transform: translateY(0px) translateX(10px); }
        75% { transform: translateY(10px) translateX(5px); }
        100% { transform: translateY(0px) translateX(0px); }
      }
      .animate-float {
        animation: float 10s ease-in-out infinite;
      }
      .bg-grid-pattern {
        background-image: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
        background-size: 20px 20px;
      }
    `;
    document.head.appendChild(styleSheet);
    
    return () => {
      document.head.removeChild(styleSheet);
    };
  }, [location]);

  const features = [
    'Models - C.A 6416/C.A 6417',
    'Large measurement range: 1500 Ω / 600 µA / 40A',
    'Alarms on Ω, A and V',
    'Automatic Pre-Hold mode when the Clamp is opened',
    '300 storage locations(C.A 6416)',
    'Real-time clock for time/date-stamping of measurements',
    'Automatic calibration of jaw opening distance at start-up',
    'Buzzer and automatic power-off',
    '2000 storage locations(C.A 6417)',
    'Bluetooth communication(C.A 6417)',
    'GTC data transfer software (C.A 6417)',
    'Safety standard CAT IV 600V'
  ];

  const specifications: Specification[] = [
    { parameter: 'Display', value: 'Backlit LCD' },
    { parameter: 'Loop ohmmeter', value: '0.010 to 1500' },
    { parameter: 'measurement Frequency', value: '2083 Hz' },
    { parameter: 'Transposition Frequency', value: '50, 60, 128 or 2083 Hz' },
    { parameter: 'Loop inductance', value: '10µH to 500µH' },
    { parameter: 'Ground Voltage', value: '0.1V to 75V' },
    { parameter: 'Ammeter', value: '0.200mA to 39.99A' },
    { parameter: 'Others', value: 'Alarm/Buzzer/Hold/Automatic power-off' },
    { parameter: 'Weight', value: '55 x 95 x 262 mm / 935gm' }
  ];
  
  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }
  };
  
  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };
  
  const itemVariant = {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.4 } }
  };

  // Download PDF function
  const downloadPDF = () => {
    // Create an anchor element and set attributes
    const link = document.createElement('a');
    link.href = '/brochures/CA6416-CA6417-brochure.pdf';
    link.download = 'LeakageClamp-brochure.pdf';
    link.target = '_blank';
    
    // Append to the document, click, and remove
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <PageLayout
      title="Leakage Clamps"
      subtitle="High-precision leakage current measurement tools for electrical troubleshooting"
      category="measure"
    >
      {/* Improved Hero Section with Fluid Design and Enhanced Animations */}
      <div className="relative overflow-hidden bg-gradient-to-br from-cyan-800 via-cyan-700 to-blue-700 -mt-4 -mx-4 mb-12 py-20 px-4">
        {/* Animated background overlay */}
        <div className="absolute inset-0 bg-cyan-900 opacity-20">
          <div className="w-full h-full" style={{
            backgroundImage: "radial-gradient(circle at 20% 50%, rgba(255,255,255,0.03) 0%, transparent 25%), radial-gradient(circle at 80% 30%, rgba(255,255,255,0.05) 0%, transparent 25%)"
          }}></div>
        </div>
        
        {/* Animated floating particles */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(12)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute rounded-full bg-white/10"
              style={{
                width: Math.random() * 8 + 2 + 'px',
                height: Math.random() * 8 + 2 + 'px',
                left: Math.random() * 100 + '%',
                top: Math.random() * 100 + '%',
              }}
              animate={{
                y: [0, -30, 0],
                x: [0, Math.random() * 20 - 10, 0],
                opacity: [0.2, 0.7, 0.2]
              }}
              transition={{
                repeat: Infinity,
                duration: Math.random() * 5 + 5,
                delay: Math.random() * 2,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>

        {/* Main content with animations */}
        <motion.div
          className="relative z-10 max-w-4xl mx-auto text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8 }}
        >
          <motion.div 
            className="mb-6"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.6 }}
          >
            <motion.div 
              className="inline-block bg-gradient-to-r from-cyan-500/30 to-blue-500/30 backdrop-blur-md px-4 py-1.5 rounded-full text-cyan-50 text-sm font-medium shadow-lg border border-cyan-400/20"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              Professional Measurement Solutions
            </motion.div>
          </motion.div>
          
          <motion.h1 
            className="text-6xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-cyan-100"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            LEAKAGE <span className="text-cyan-200">CLAMPS</span>
          </motion.h1>
          
          <motion.p 
            className="text-white text-xl mb-10 max-w-2xl mx-auto leading-relaxed drop-shadow-md"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
          >
            High-precision leakage current measurement tools for electrical troubleshooting and safety analysis
          </motion.p>
          
          {/* Animated navigation tabs */}
          <motion.div 
            className="inline-flex rounded-full p-1 mx-auto backdrop-blur-lg border border-white/20 shadow-2xl"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.6 }}
          >
            <button 
              onClick={() => window.location.href = '/measure/clamp-meters'}
              className="px-6 py-2.5 text-cyan-100 hover:text-white rounded-full transition-colors"
            >
              Overview
            </button>
            <button 
              onClick={() => window.location.href = '/measure/clamp-meters/power-harmonic'}
              className="px-6 py-2.5 text-cyan-100 hover:text-white rounded-full transition-colors"
            >
              Power & Harmonic
            </button>
            <div className="px-6 py-2.5 text-black font-medium rounded-full bg-gradient-to-r from-cyan-200 to-cyan-300 shadow-lg">
              Leakage Clamps
            </div>
          </motion.div>
        </motion.div>
        
        {/* Light rays effect */}
        <div className="absolute inset-0 overflow-hidden">
          <motion.div 
            className="absolute -top-40 -left-20 w-96 h-96 bg-gradient-to-br from-cyan-400/20 to-transparent rotate-45 blur-3xl"
            animate={{ 
              opacity: [0.3, 0.5, 0.3],
              scale: [0.8, 1.1, 0.8]
            }}
            transition={{ 
              repeat: Infinity,
              duration: 8
            }}
          />
          <motion.div 
            className="absolute -bottom-40 -right-20 w-96 h-96 bg-gradient-to-br from-blue-400/20 to-transparent -rotate-45 blur-3xl"
            animate={{ 
              opacity: [0.2, 0.4, 0.2],
              scale: [0.9, 1.2, 0.9]
            }}
            transition={{ 
              repeat: Infinity,
              duration: 10,
              delay: 3
            }}
          />
        </div>
        
        {/* Wave effect at bottom */}
        <div className="absolute bottom-0 left-0 right-0 h-10 overflow-hidden">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" className="absolute w-full h-auto min-w-full" style={{ bottom: -1 }}>
            <path fill="#ffffff" fillOpacity="0.08" d="M0,288L48,272C96,256,192,224,288,197.3C384,171,480,149,576,165.3C672,181,768,235,864,234.7C960,235,1056,181,1152,170.7C1248,160,1344,192,1392,208L1440,224L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
          </svg>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" className="absolute w-full h-auto min-w-full" style={{ bottom: -5 }}>
            <path fill="#ffffff" fillOpacity="0.1" d="M0,160L48,170.7C96,181,192,203,288,202.7C384,203,480,181,576,186.7C672,192,768,224,864,213.3C960,203,1056,149,1152,122.7C1248,96,1344,96,1392,96L1440,96L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
          </svg>
        </div>
      </div>
      
      {/* Product Details with 3D-like effect */}
      <motion.div
        variants={fadeIn}
        initial="hidden"
        animate="visible"
        className="mb-16"
      >
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden relative transform perspective-1000">
          {/* Glass-like header */}
          <div className="bg-gradient-to-r from-cyan-700 via-blue-600 to-cyan-700 p-8 relative overflow-hidden">
            <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
            <div className="absolute inset-0">
              <svg width="100%" height="100%" className="opacity-20">
                <pattern id="product-grid" width="20" height="20" patternUnits="userSpaceOnUse">
                  <circle cx="10" cy="10" r="1" fill="white" fillOpacity="0.4" />
                </pattern>
                <rect width="100%" height="100%" fill="url(#product-grid)" />
              </svg>
            </div>
            <div className="relative z-10">
              <div className="flex items-center justify-between">
                <h2 className="text-3xl font-bold text-white flex items-center mb-2">
                  <span className="mr-3">Leakage Clamp C.A 6416/C.A 6417</span>
                </h2>
              </div>
              <p className="text-cyan-100">
                High-precision leakage current measurement for electrical safety and troubleshooting
              </p>
            </div>
          </div>
          
          <div className="flex flex-col lg:flex-row">
            {/* Product Image with hover effect */}
            <div className="lg:w-1/3 p-8 bg-gradient-to-b from-cyan-50 via-blue-50 to-cyan-50 relative overflow-hidden">
              <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
              <div className="relative group">
                <div className="absolute -inset-1 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-full opacity-0 group-hover:opacity-10 transition-opacity duration-300 blur-xl"></div>
                
                <motion.div
                  whileHover={{ rotate: [0, -1, 1, -1, 0], scale: 1.08 }}
                  transition={{ duration: 0.7 }}
                  className="relative z-10 flex items-center justify-center p-8"
                >
                  <img
                    src="/assets/PA/leakage_preview_rev_1.png"
                    alt="Leakage Clamp C.A 6416/C.A 6417"
                    className="max-w-full h-auto max-h-96 drop-shadow-2xl"
                  />
                </motion.div>
                
                {/* Decorative elements */}
                <div className="absolute top-1/4 left-0 w-3 h-3 bg-cyan-500 rounded-full animate-ping" style={{ animationDuration: '3s' }}></div>
                <div className="absolute bottom-1/3 right-0 w-2 h-2 bg-blue-400 rounded-full animate-ping" style={{ animationDuration: '4s' }}></div>
                <div className="absolute bottom-1/4 left-1/4 w-1.5 h-1.5 bg-cyan-400 rounded-full animate-ping" style={{ animationDuration: '5s' }}></div>
              </div>
              
              {/* Preview button below the image */}
              <div className="flex justify-center mt-6">
                <button className="bg-white/90 hover:bg-white text-cyan-600 border border-cyan-200 px-6 py-2.5 rounded-full transition-all shadow-lg backdrop-blur-sm flex items-center hover:scale-105">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                    <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                  </svg>
                  PREVIEW
                </button>
              </div>
            </div>
            
            {/* Product Details with tabs */}
            <div className="lg:w-2/3 p-8">
              {/* Tabs for Features/Specs */}
              <div className="flex mb-8 border-b border-cyan-100">
                <button
                  className={`px-6 py-3 text-lg font-medium transition-all ${
                    activeTab === 'features' 
                      ? 'text-cyan-600 border-b-2 border-cyan-600' 
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                  onClick={() => setActiveTab('features')}
                >
                  <div className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Features
                  </div>
                </button>
                <button
                  className={`px-6 py-3 text-lg font-medium transition-all ${
                    activeTab === 'specs' 
                      ? 'text-cyan-600 border-b-2 border-cyan-600' 
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                  onClick={() => setActiveTab('specs')}
                >
                  <div className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                    </svg>
                    Specifications
                  </div>
                </button>
              </div>
              
              {/* Content based on active tab */}
              <AnimatePresence mode="wait">
                {activeTab === 'features' ? (
                  <motion.div
                    key="features"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.3 }}
                  >
                    <motion.div 
                      className="grid grid-cols-1 md:grid-cols-2 gap-4"
                      variants={staggerContainer}
                      initial="hidden"
                      animate="visible"
                    >
                      {features.map((feature, index) => (
                        <motion.div 
                          key={index} 
                          className="flex items-start group"
                          variants={itemVariant}
                        >
                          <div className="flex-shrink-0 mr-3 mt-1">
                            <div className="w-6 h-6 rounded-full bg-gradient-to-br from-cyan-400 to-blue-500 flex items-center justify-center shadow-lg shadow-cyan-500/20 group-hover:shadow-cyan-500/40 transition-all">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-white" viewBox=" 0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            </div>
                          </div>
                          <div>
                            <p className="text-gray-800 font-medium group-hover:text-gray-900 transition-colors">
                              {feature}
                            </p>
                          </div>
                        </motion.div>
                      ))}
                    </motion.div>
                  </motion.div>
                ) : (
                  <motion.div
                    key="specifications"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.3 }}
                    className="overflow-x-auto"
                  >
                    <div className="overflow-hidden rounded-xl shadow-md">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="bg-gradient-to-r from-cyan-600 to-blue-600 text-white">
                            <th className="p-3 text-left rounded-tl-xl font-bold text-base">PARAMETER</th>
                            <th className="p-3 text-left rounded-tr-xl font-bold text-base">RANGE</th>
                          </tr>
                        </thead>
                        <tbody>
                          {specifications.map((spec, index) => (
                            <motion.tr 
                              key={index} 
                              className={`${index % 2 === 0 ? 'bg-cyan-50' : 'bg-white'} hover:bg-cyan-100/50 transition-colors`}
                              initial={{ opacity: 0, y: 10 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: index * 0.05, duration: 0.3 }}
                            >
                              <td className="p-3 border-t border-cyan-100 font-semibold text-gray-700">{spec.parameter}</td>
                              <td className="p-3 border-t border-cyan-100 text-gray-800">{spec.value}</td>
                            </motion.tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
              
              {/* CTA Section with both buttons next to each other */}
              <motion.div 
                className="mt-10 flex flex-wrap gap-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.5 }}
              >
                <button className="bg-gradient-to-r from-cyan-600 via-blue-600 to-cyan-600 hover:from-cyan-700 hover:via-blue-700 hover:to-cyan-700 text-white px-8 py-3.5 rounded-full transition-all shadow-lg shadow-cyan-500/30 hover:shadow-cyan-500/50 font-medium flex items-center group">
                  <span>ENQUIRE NOW</span>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2 transform group-hover:translate-x-1 transition-transform" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L13.586 10l-3.293-3.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
                
                {/* PDF Brochure button */}
                <button 
                  className="bg-gradient-to-r from-cyan-600 to-blue-600 hover:from-cyan-700 hover:to-blue-700 text-white px-8 py-3.5 rounded-full transition-all shadow-lg shadow-cyan-500/30 flex items-center"
                  onClick={downloadPDF}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clipRule="evenodd" />
                  </svg>
                  PDF BROCHURE
                </button>
              </motion.div>
            </div>
          </div>
        </div>
      </motion.div>
      
      {/* Features Highlight Cards */}
      <motion.div 
        className="mb-16"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <h2 className="text-3xl font-bold mb-8 text-center text-gray-800">
          Key Features & <span className="text-cyan-600">Capabilities</span>
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Feature Card 1 */}
          <motion.div 
            className="bg-white rounded-xl shadow-lg overflow-hidden group hover:shadow-xl transition-all"
            whileHover={{ y: -5 }}
          >
            <div className="h-3 bg-cyan-500"></div>
            <div className="p-6">
              <div className="w-12 h-12 rounded-lg bg-cyan-100 text-cyan-600 flex items-center justify-center mb-4 group-hover:bg-cyan-500 group-hover:text-white transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2 text-gray-800">Precise Leakage Detection</h3>
              <p className="text-gray-600">
                Detect even the smallest leakage currents with high resolution (0.200mA to 39.99A) for comprehensive electrical safety testing.
              </p>
            </div>
          </motion.div>
          
          {/* Feature Card 2 */}
          <motion.div 
            className="bg-white rounded-xl shadow-lg overflow-hidden group hover:shadow-xl transition-all"
            whileHover={{ y: -5 }}
          >
            <div className="h-3 bg-blue-500"></div>
            <div className="p-6">
              <div className="w-12 h-12 rounded-lg bg-blue-100 text-blue-600 flex items-center justify-center mb-4 group-hover:bg-blue-500 group-hover:text-white transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2 text-gray-800">Comprehensive Data Storage</h3>
              <p className="text-gray-600">
                Store up to 300 readings (C.A 6416) or 2000 readings (C.A 6417) with time and date stamps for detailed analysis and reporting.
              </p>
            </div>
          </motion.div>
          
          {/* Feature Card 3 */}
          <motion.div 
            className="bg-white rounded-xl shadow-lg overflow-hidden group hover:shadow-xl transition-all"
            whileHover={{ y: -5 }}
          >
            <div className="h-3 bg-cyan-500"></div>
            <div className="p-6">
              <div className="w-12 h-12 rounded-lg bg-cyan-100 text-cyan-600 flex items-center justify-center mb-4 group-hover:bg-cyan-500 group-hover:text-white transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2 text-gray-800">Enhanced Safety</h3>
              <p className="text-gray-600">
                Built to CAT IV 600V safety standards, providing reliable protection in demanding industrial environments and critical applications.
              </p>
            </div>
          </motion.div>
        </div>
      </motion.div>

      {/* Related Products Section with Enhanced Gradients */}
      <motion.div 
        className="mb-16"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 1.0 }}
      >
        <h2 className="text-3xl font-bold mb-8 text-center text-gray-800">
          Related <span className="text-cyan-600">Products</span>
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Related Product 1 */}
          <motion.div 
            className="bg-white rounded-xl shadow-lg overflow-hidden group hover:shadow-xl transition-all border border-cyan-100"
            whileHover={{ y: -5, scale: 1.02 }}
          >
            <div className="h-48 bg-gradient-to-br from-cyan-400/10 via-blue-200/10 to-cyan-400/10 flex items-center justify-center p-6 relative overflow-hidden">
              <div className="absolute inset-0 bg-grid-pattern opacity-20"></div>
              
              {/* Animated background elements */}
              <div className="absolute h-16 w-16 -top-6 -right-6 bg-cyan-400/20 rounded-full blur-xl animate-float" style={{animationDelay: "0s", animationDuration: "8s"}}></div>
              <div className="absolute h-20 w-20 -bottom-10 -left-10 bg-blue-400/20 rounded-full blur-xl animate-float" style={{animationDelay: "2s", animationDuration: "10s"}}></div>
              
              <img 
                src="/images/power-clamp-1.png" 
                alt="Power Clamp Meter" 
                className="max-h-full max-w-full object-contain group-hover:scale-110 transition-transform duration-700 relative z-10"
              />
              
              {/* Glowing effect on hover */}
              <div className="absolute inset-0 bg-gradient-to-br from-cyan-400/0 to-blue-400/0 group-hover:from-cyan-400/10 group-hover:to-blue-400/10 transition-all duration-500"></div>
            </div>
            <div className="p-6 relative">
              {/* Decorative accent */}
              <div className="absolute w-1/3 h-1 top-0 left-0 bg-gradient-to-r from-cyan-500 to-transparent"></div>
              
              <h3 className="text-xl font-semibold mb-2 text-gray-800">Power & Harmonic Clamps</h3>
              <p className="text-gray-600 mb-4">
                Advanced clamp meters for comprehensive power quality analysis and harmonic measurement.
              </p>
              <button 
                onClick={() => window.location.href = '/measure/clamp-meters/power-harmonic'}
                className="text-cyan-600 font-medium hover:text-cyan-700 transition-colors flex items-center"
              >
                <span>View Details</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1 group-hover:translate-x-1 transition-transform" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L13.586 10l-3.293-3.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </motion.div>
          
          {/* Related Product 2 */}
          <motion.div 
            className="bg-white rounded-xl shadow-lg overflow-hidden group hover:shadow-xl transition-all border border-blue-100"
            whileHover={{ y: -5, scale: 1.02 }}
          >
            <div className="h-48 bg-gradient-to-br from-blue-400/10 via-cyan-200/10 to-cyan-400/10 flex items-center justify-center p-6 relative overflow-hidden">
              <div className="absolute inset-0 bg-grid-pattern opacity-20"></div>
              
              {/* Animated background elements */}
              <div className="absolute h-16 w-16 -bottom-6 -right-6 bg-blue-400/20 rounded-full blur-xl animate-float" style={{animationDelay: "1s", animationDuration: "9s"}}></div>
              <div className="absolute h-20 w-20 -top-10 -left-10 bg-cyan-400/20 rounded-full blur-xl animate-float" style={{animationDelay: "3s", animationDuration: "11s"}}></div>
              
              <img 
                src="/images/multimeter-1.png" 
                alt="Digital Multimeter" 
                className="max-h-full max-w-full object-contain group-hover:scale-110 transition-transform duration-700 relative z-10"
              />
              
              {/* Glowing effect on hover */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-400/0 to-cyan-400/0 group-hover:from-blue-400/10 group-hover:to-cyan-400/10 transition-all duration-500"></div>
            </div>
            <div className="p-6 relative">
              {/* Decorative accent */}
              <div className="absolute w-1/3 h-1 top-0 left-0 bg-gradient-to-r from-blue-500 to-transparent"></div>
              
              <h3 className="text-xl font-semibold mb-2 text-gray-800">Digital Multimeters</h3>
              <p className="text-gray-600 mb-4">
                Precision measurement tools for voltage, current, resistance, and more in a compact form factor.
              </p>
              <button 
                onClick={() => window.location.href = '/measure/multimeters'}
                className="text-blue-600 font-medium hover:text-blue-700 transition-colors flex items-center"
              >
                <span>View Details</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1 group-hover:translate-x-1 transition-transform" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L13.586 10l-3.293-3.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </motion.div>
          
          {/* Related Product 3 */}
          <motion.div 
            className="bg-white rounded-xl shadow-lg overflow-hidden group hover:shadow-xl transition-all border border-cyan-100"
            whileHover={{ y: -5, scale: 1.02 }}
          >
            <div className="h-48 bg-gradient-to-br from-cyan-400/10 via-blue-200/10 to-cyan-400/10 flex items-center justify-center p-6 relative overflow-hidden">
              <div className="absolute inset-0 bg-grid-pattern opacity-20"></div>
              
              {/* Animated background elements */}
              <div className="absolute h-16 w-16 -top-6 -left-6 bg-cyan-400/20 rounded-full blur-xl animate-float" style={{animationDelay: "2s", animationDuration: "10s"}}></div>
              <div className="absolute h-20 w-20 -bottom-10 -right-10 bg-blue-400/20 rounded-full blur-xl animate-float" style={{animationDelay: "4s", animationDuration: "12s"}}></div>
              
              <img 
                src="/images/earth-tester-1.png" 
                alt="Earth Tester" 
                className="max-h-full max-w-full object-contain group-hover:scale-110 transition-transform duration-700 relative z-10"
              />
              
              {/* Glowing effect on hover */}
              <div className="absolute inset-0 bg-gradient-to-br from-cyan-400/0 to-blue-400/0 group-hover:from-cyan-400/10 group-hover:to-blue-400/10 transition-all duration-500"></div>
            </div>
            <div className="p-6 relative">
              {/* Decorative accent */}
              <div className="absolute w-1/3 h-1 top-0 left-0 bg-gradient-to-r from-cyan-500 to-transparent"></div>
              
              <h3 className="text-xl font-semibold mb-2 text-gray-800">Earth Testers</h3>
              <p className="text-gray-600 mb-4">
                Specialized instruments for measuring earth resistance and soil resistivity for electrical safety compliance.
              </p>
              <button 
                onClick={() => window.location.href = '/measure/earth-testers'}
                className="text-cyan-600 font-medium hover:text-cyan-700 transition-colors flex items-center"
              >
                <span>View Details</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1 group-hover:translate-x-1 transition-transform" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L13.586 10l-3.293-3.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </motion.div>
        </div>
      </motion.div>
      
      {/* FAQ Section with Accordion */}
      <motion.div 
        className="mb-16"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 1.2 }}
      >
        <h2 className="text-3xl font-bold mb-8 text-center text-gray-800">
          Frequently Asked <span className="text-cyan-600">Questions</span>
        </h2>
        
        <div className="max-w-3xl mx-auto">
          <Accordion />
        </div>
      </motion.div>
      
      {/* Contact Information Section - enhanced styling */}
      <motion.div 
        className="mt-16 bg-gradient-to-r from-cyan-50 via-blue-50 to-cyan-50 p-12 rounded-2xl shadow-lg border border-cyan-100 relative overflow-hidden"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 1.4 }}
      >
        {/* Background elements */}
        <div className="absolute inset-0 bg-grid-pattern opacity-30"></div>
        <div className="absolute -top-12 -right-12 w-40 h-40 bg-cyan-200 rounded-full opacity-30 blur-2xl"></div>
        <div className="absolute -bottom-16 -left-16 w-60 h-60 bg-blue-200 rounded-full opacity-30 blur-3xl"></div>
        
        <div className="relative z-10">
          <h2 className="text-3xl font-bold mb-6 text-center text-gray-800">Need More Information?</h2>
          <p className="text-center text-gray-600 mb-8 max-w-2xl mx-auto">
            Our team of experts is ready to help you with product specifications, pricing information, and professional advice for your specific measurement needs.
          </p>
          <div className="flex flex-col md:flex-row gap-4 justify-center max-w-2xl mx-auto">
            <button className="bg-gradient-to-r from-cyan-600 to-blue-600 hover:from-cyan-700 hover:to-blue-700 text-white px-8 py-4 rounded-xl transition-all shadow-lg shadow-cyan-500/20 hover:shadow-cyan-500/40 text-lg font-medium flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              Contact Our Experts
            </button>
            <button className="bg-white hover:bg-gray-50 text-cyan-600 border-2 border-cyan-200 hover:border-cyan-300 px-8 py-4 rounded-xl transition-all shadow-lg text-lg font-medium flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
              Schedule a Demo
            </button>
          </div>
        </div>
      </motion.div>
    </PageLayout>
  );
};

// Accordion Component for FAQ Section
const Accordion = () => {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  
  const faqs = [
    {
      question: "What is the difference between C.A 6416 and C.A 6417 models?",
      answer: "The C.A 6416 offers 300 storage locations for measurements, while the C.A 6417 has expanded memory with 2000 storage locations. Additionally, the C.A 6417 features Bluetooth communication capabilities and includes GTC data transfer software for seamless data management and analysis."
    },
    {
      question: "How accurate are these leakage clamps for small current measurements?",
      answer: "Our leakage clamps are designed for high precision with a resolution starting from 0.200mA, making them ideal for detecting even very small leakage currents. The accuracy is typically ±1.5% of reading ±2 counts for current measurements, ensuring reliable results for safety testing and troubleshooting."
    },
    {
      question: "Can these clamps measure both AC and DC leakage currents?",
      answer: "Yes, these leakage clamps can measure both AC and DC leakage currents. They are particularly effective for measuring AC leakage currents in various electrical systems, helping identify potential safety hazards and troubleshoot electrical problems."
    },
    {
      question: "What safety certifications do these leakage clamps have?",
      answer: "Our leakage clamps are certified to CAT IV 600V safety standards, ensuring they provide protection in the most demanding electrical environments, including low-voltage installations at the utility level and outdoors."
    },
    {
      question: "How can I transfer data from these clamps to my computer?",
      answer: "The C.A 6417 model features Bluetooth communication that allows wireless data transfer to a computer. It comes with GTC data transfer software that facilitates data management, analysis, and report generation. The C.A 6416 model requires manual recording of measurements."
    }
  ];
  
  const toggleAccordion = (index: number) => {
    setActiveIndex(activeIndex === index ? null : index);
  };
  
  return (
    <div className="space-y-4">
      {faqs.map((faq, index) => (
        <div 
          key={index} 
          className="border border-cyan-100 rounded-xl overflow-hidden transition-all shadow-sm"
        >
          <button
            className={`w-full p-4 text-left font-medium flex justify-between items-center ${
              activeIndex === index ? 'bg-cyan-50 text-cyan-700' : 'bg-white text-gray-800'
            }`}
            onClick={() => toggleAccordion(index)}
          >
            <span>{faq.question}</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`h-5 w-5 transition-transform ${activeIndex === index ? 'transform rotate-180 text-cyan-600' : 'text-gray-500'}`}
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
          <div
            className={`bg-white transition-all duration-300 overflow-hidden ${
              activeIndex === index ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
            }`}
          >
            <div className="p-4 border-t border-cyan-100">
              <p className="text-gray-600">{faq.answer}</p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default LeakageClampsPage;