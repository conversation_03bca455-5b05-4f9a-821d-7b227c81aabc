import React, { useState } from 'react';
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON> } from "react-router-dom";
import PageLayout from "@/components/layout/PageLayout";
import { Ta<PERSON>, Ta<PERSON>List, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Check, Download, HelpCircle, ArrowRight } from "lucide-react";

const Ratiometer = () => {
  const [activeTab, setActiveTab] = useState('radiometer');

  const navItems = [
    { id: 'overview', label: 'Overview' },
    { id: 'installation', label: 'Installation Testers' },
    { id: 'insulation', label: 'Insulation Testers' },
    { id: 'insulation-continuity', label: 'Insulation & Continuity Testers' },
    { id: 'digital', label: 'Digital Insulation Testers' },
    { id: 'earth', label: 'Earth & Resistivity Testers' },
    { id: 'radiometer', label: 'Radiometer' }
  ];
  
  const features = [
    { text: "Models - DTR 8510", icon: <Check className="h-4 w-4" /> },
    { text: "Digital ratiometer for transformers", icon: <Check className="h-4 w-4" /> },
    { text: "The DTR 8510 is a portable digital ratiometer for testing power, voltage and current transformers.", icon: <Check className="h-4 w-4" /> },
    { text: "When connected to an uncoupled transformer, the DTR 8510 accurately measures the primary-to-secondary transformation ratio, while also indicating the polarity and excitation current.", icon: <Check className="h-4 w-4" /> },
    { text: "The DTR 8510 is totally automatic and uses a test method which complies with the ANSI/IEEE standard. Users are not required to perform any calibration or balancing.", icon: <Check className="h-4 w-4" /> }
  ];
  
  const selfCalibrationFeatures = [
    "Open circuits, windings, connections and cut-off circuits.",
    "Short-circuits (excessive value of excitation current).",
    "Incorrect connection of the test cables.",
    "Polarity reversals.",
    "The measurements are displayed quickly and accurately."
  ];
  
  const specifications = [
    {
      title: "Transformation ratio",
      description: "From 0.8000:1 to 8.000:1 (power and voltage transformers) and from 0.8000 to 1.000:0 (current transformers) - Autorange function"
    },
    {
      title: "Testing methodology",
      description: "Testing by excitation of the primary and reading of the secondary, in order to guarantee user safety"
    },
    {
      title: "Alert system",
      description: "Display of alerts in the event of incorrect connection, reversed polarity, open circuits and short-circuits"
    },
    {
      title: "Display",
      description: "Digital display - display of the ratios, winding directions - % deviation in relation to the rated values and excitation current. The bargraph indicates the progression of self-calibration and measurement"
    },
    {
      title: "Storage capacity",
      description: "Storage of up to 10,000 measurement results in internal memory"
    },
    {
      title: "Connectivity",
      description: "USB connection and DataView PC software supplied as standard to set up the instrument, download and analyse the measurements and generate test reports"
    },
    {
      title: "Power supply",
      description: "By NiMH rechargeable battery with external charger (battery life of 10 hours in continuous operation)"
    },
    {
      title: "Protection rating",
      description: "IP53 protection"
    },
    {
      title: "Safety standards",
      description: "IEC 61010 50V CAT IV safety"
    },
    {
      title: "Dimensions & weight",
      description: "272 x 248 x 130 mm / 3.7 kg"
    }
  ];
  
  const applications = [
    {
      title: "Power Transformers",
      description: "Test and verify transformation ratios on power transformers to ensure proper operation and safety."
    },
    {
      title: "Voltage Transformers",
      description: "Measure ratios and verify polarity on voltage transformers during commissioning or maintenance."
    },
    {
      title: "Current Transformers",
      description: "Check current transformers for proper ratio, polarity, and excitation performance to ensure measurement accuracy."
    }
  ];

  return (
    <PageLayout
      title="Ratiometer"
      subtitle="Professional transformer ratio testing device for precise measurements"
      category="measure"
    >
      {/* Category Navigation */}
      <div className="sticky top-0 z-10 bg-background border-b border-border/50 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Tabs defaultValue="radiometer" onValueChange={setActiveTab} className="w-full">
            <TabsList className="h-auto p-0 bg-transparent overflow-x-auto flex w-full justify-start">
              {navItems.map((item) => (
                <TabsTrigger 
                  key={item.id} 
                  value={item.id}
                  className="py-3 px-4 data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none rounded-none text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
                >
                  {item.label}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        </div>
      </div>

      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3 }}
            className="py-8"
          >
            {/* Hero Section */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-20">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="flex items-center justify-center bg-gradient-to-br from-primary/10 to-primary/5 rounded-2xl p-8"
              >
                <img
                  src="/api/placeholder/400/320"
                  alt="DTR 8510 Ratiometer"
                  className="max-w-full h-auto rounded-lg shadow-2xl"
                />
              </motion.div>
              
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <Badge className="mb-4 bg-primary/10 text-primary hover:bg-primary/20 px-3 py-1 text-sm font-medium">Professional Equipment</Badge>
                <h1 className="text-5xl font-bold tracking-tight mb-6">DTR 8510 Ratiometer</h1>
                <p className="text-lg text-muted-foreground mb-8">
                  High-precision transformer measurement device with automatic calibration and comprehensive safety features.
                </p>
                
                <div className="space-y-4 mb-8">
                  {features.map((feature, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <div className="mt-1 h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center text-primary">
                        {feature.icon}
                      </div>
                      <p className="text-base">{feature.text}</p>
                    </div>
                  ))}
                </div>
                
                <div className="flex flex-wrap gap-4">
                  <Button variant="outline" className="gap-2">
                    <HelpCircle className="h-4 w-4" />
                    Enquire
                  </Button>
                  <Button variant="outline" className="gap-2">
                    <Download className="h-4 w-4" />
                    Download Brochure
                  </Button>
                </div>
              </motion.div>
            </div>

            {/* Self-calibration Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="mb-20"
            >
              <div className="flex flex-col md:flex-row gap-12 bg-gradient-to-r from-primary/10 to-primary/5 rounded-2xl p-8">
                <div className="w-full md:w-2/3">
                  <h2 className="text-3xl font-bold mb-6">Intelligent Self-calibration</h2>
                  <p className="mb-8 text-lg text-muted-foreground">
                    The DTR 8510 features advanced self-calibration technology that automatically verifies your setup before each measurement to ensure safety and accuracy.
                  </p>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                    {selfCalibrationFeatures.map((feature, index) => (
                      <div key={index} className="flex items-start gap-3">
                        <div className="mt-1 h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center text-primary">
                          <Check className="h-3 w-3" />
                        </div>
                        <p className="text-base">{feature}</p>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="w-full md:w-1/3 flex items-center justify-center">
                  <div className="rounded-2xl overflow-hidden shadow-2xl">
                    <img
                      src="/api/placeholder/400/320"
                      alt="DTR 8510 Ratiometer close-up"
                      className="w-full h-auto object-cover"
                    />
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Specifications Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              className="mb-20"
            >
              <h2 className="text-4xl font-bold mb-8">Technical Specifications</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {specifications.map((spec, index) => (
                  <Card key={index} className="border-border/50 bg-background hover:bg-muted/10 transition-colors">
                    <CardHeader className="py-6 px-6">
                      <CardTitle className="text-xl font-semibold">{spec.title}</CardTitle>
                    </CardHeader>
                    <CardContent className="py-2 px-6 text-base text-muted-foreground">
                      {spec.description}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </motion.div>

            {/* Applications Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
              className="mb-20"
            >
              <h2 className="text-4xl font-bold mb-8">Applications</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {applications.map((app, index) => (
                  <Card key={index} className="bg-gradient-to-b from-background to-muted/10 border-border/50 overflow-hidden hover:shadow-lg transition-shadow">
                    <CardHeader className="pb-4 px-6 pt-6">
                      <CardTitle className="text-2xl font-bold">{app.title}</CardTitle>
                    </CardHeader>
                    <CardContent className="px-6 pb-6">
                      <p className="text-lg text-muted-foreground">{app.description}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </motion.div>

            {/* CTA Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.7 }}
              className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-2xl p-12 text-center"
            >
              <h2 className="text-4xl font-bold mb-6">Need Help With This Product?</h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto mb-8">
                Our team of experts can help you understand if the DTR 8510 Ratiometer is right for your specific needs and provide tailored recommendations.
              </p>
              <Button asChild size="lg" className="gap-2">
                <Link to="/contact">
                  Contact Our Experts <ArrowRight className="h-4 w-4" />
                </Link>
              </Button>
            </motion.div>
          </motion.div>
        </AnimatePresence>
      </div>
    </PageLayout>
  );
};

export default Ratiometer;