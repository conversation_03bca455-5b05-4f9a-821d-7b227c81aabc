import React from 'react';
import { Shield, Zap, Settings, Headphones, CheckCircle, Monitor, BarChart3, Lock, Star, Mail, Download, Info, ArrowRight, FileText, Clock } from 'lucide-react';
import PageLayout from "@/components/layout/PageLayout";

const ServoStabilizersPhase1 = () => {

  // Key Statistics for the hero section
  const keyStats = [
    { value: "98", suffix: "%", title: "Efficiency", icon: <Zap size={24} /> },
    { value: "±1", suffix: "%", title: "Voltage Regulation", icon: <BarChart3 size={24} /> },
    { value: "20", suffix: "kVA", title: "Max Capacity", icon: <Shield size={24} /> },
    { value: "24/7", suffix: "", title: "Protection", icon: <Clock size={24} /> }
  ];

  const specifications = [
    { parameter: "Rating (kVA)", "1kVA": "1 kVA", "2-3kVA": "2-3 kVA", "4-5kVA": "4-5 kVA", "7.5-10kVA": "7.5-10 kVA", "15-20kVA": "15-20 kVA" },
    { parameter: "Input Voltage Range", "1kVA": "190-250V", "2-3kVA": "190-250V", "4-5kVA": "190-250V", "7.5-10kVA": "190-250V", "15-20kVA": "190-250V" },
    { parameter: "Output Voltage", "1kVA": "220V ±1%", "2-3kVA": "220V ±1%", "4-5kVA": "220V ±1%", "7.5-10kVA": "220V ±1%", "15-20kVA": "220V ±1%" },
    { parameter: "Input Frequency", "1kVA": "47-53 Hz", "2-3kVA": "47-53 Hz", "4-5kVA": "47-53 Hz", "7.5-10kVA": "47-53 Hz", "15-20kVA": "47-53 Hz" },
    { parameter: "Digital Display", "1kVA": "3 digit, 7 segment", "2-3kVA": "3 digit, 7 segment", "4-5kVA": "3 digit, 7 segment", "7.5-10kVA": "3 digit, 7 segment", "15-20kVA": "3 digit, 7 segment" },
    { parameter: "Voltage Accuracy", "1kVA": "1% ±2 digit", "2-3kVA": "1% ±2 digit", "4-5kVA": "1% ±2 digit", "7.5-10kVA": "1% ±2 digit", "15-20kVA": "1% ±2 digit" },
    { parameter: "Under/Over Voltage Trip", "1kVA": "-5% / +10%", "2-3kVA": "-5% / +10%", "4-5kVA": "-5% / +10%", "7.5-10kVA": "-5% / +10%", "15-20kVA": "-5% / +10%" },
    { parameter: "Response Time", "1kVA": "< 20 ms", "2-3kVA": "< 20 ms", "4-5kVA": "< 20 ms", "7.5-10kVA": "< 20 ms", "15-20kVA": "< 20 ms" },
    { parameter: "Efficiency", "1kVA": "> 98%", "2-3kVA": "> 98%", "4-5kVA": "> 98%", "7.5-10kVA": "> 98%", "15-20kVA": "> 98%" },
    { parameter: "Transient Protection", "1kVA": "MOV Spike Suppressor", "2-3kVA": "MOV Spike Suppressor", "4-5kVA": "MOV Spike Suppressor", "7.5-10kVA": "MOV Spike Suppressor", "15-20kVA": "MOV Spike Suppressor" },
    { parameter: "Input Termination", "1kVA": "1.5m cable with 5A plug", "2-3kVA": "1.5m cable with 5A plug", "4-5kVA": "1.5m cable with 5A plug", "7.5-10kVA": "6A terminal block", "15-20kVA": "100A terminal block" },
    { parameter: "Output Termination", "1kVA": "1x5/15A socket", "2-3kVA": "1x5/15A socket", "4-5kVA": "2x5/15A socket", "7.5-10kVA": "Terminal block", "15-20kVA": "Terminal block" },
    { parameter: "Protection Features", "1kVA": "Complete Protection", "2-3kVA": "Complete Protection", "4-5kVA": "Complete Protection", "7.5-10kVA": "Complete Protection", "15-20kVA": "Complete Protection" },
    { parameter: "Manual Bypass", "1kVA": "Provided", "2-3kVA": "Provided", "4-5kVA": "Provided", "7.5-10kVA": "Provided", "15-20kVA": "Provided" },
    { parameter: "Operating Temperature", "1kVA": "0-50°C", "2-3kVA": "0-50°C", "4-5kVA": "0-50°C", "7.5-10kVA": "0-50°C", "15-20kVA": "0-50°C" },
    { parameter: "Warranty", "1kVA": "1 Year Comprehensive", "2-3kVA": "1 Year Comprehensive", "4-5kVA": "1 Year Comprehensive", "7.5-10kVA": "1 Year Comprehensive", "15-20kVA": "1 Year Comprehensive" }
  ];

  const features = [
    {
      icon: <BarChart3 className="w-8 h-8" />,
      title: "True RMS Correction",
      items: [
        "Microprocessor based control system for measurement and correction",
        "Digital Voltage, Current display through high visibility LED"
      ]
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: "Reliable by Design",
      items: [
        "Stabilizers designed to its rated capacity",
        "Lowest failure rates and life cycle cost in the industry",
        "1 year 'No Questions Asked' guarantee"
      ]
    },
    {
      icon: <Lock className="w-8 h-8" />,
      title: "Complete Protection",
      items: [
        "Short Circuit",
        "Under voltage",
        "Over voltage",
        "Electronic CT based Overload trip",
        "Built-in Spike Suppressor"
      ]
    },
    {
      icon: <Headphones className="w-8 h-8" />,
      title: "Dependable After Sales Support",
      items: [
        "Wide service network covering 100 + locations across India",
        "Committed Service response",
        "within 6 hours in service towns and within 24 hours in the same State"
      ]
    }
  ];

  const productRanges = [
    {
      title: "Regular Range: 190 - 250V",
      description: "for factories, offices & urban locations"
    },
    {
      title: "Wide Range: 170 - 270V",
      description: "for semi-urban locations with wide fluctuations"
    },
    {
      title: "Custom Range",
      description: "Can be provided for specific needs"
    }
  ];

  return (
    <PageLayout
      title="Servo Stabilizers - 1 Phase"
      subtitle="Advanced voltage protection solutions for your needs"
      category="protect"
    >
      {/* Modern Blue Background Component */}
      <div className="fixed inset-0 pointer-events-none z-[-1] overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100">
        {/* Abstract blue shapes */}
        <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-blue-200 rounded-bl-full opacity-30"></div>
        <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-blue-300 rounded-tr-full opacity-20"></div>

        {/* Floating geometric elements */}
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-blue-400 rounded-full opacity-10 blur-xl animate-pulse"></div>
        <div className="absolute bottom-1/3 right-1/3 w-48 h-48 bg-blue-500 rounded-full opacity-10 blur-xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="bg-gradient-to-br from-blue-50 via-blue-100 to-blue-200 font-sans -mx-6 -mt-24 pt-16 relative">
        {/* Enhanced Hero Section with Modern Design */}
        <section className="py-16 relative overflow-hidden">
          {/* Hero Background Elements - Blue Theme */}
          <div className="absolute inset-0 z-0">
            <div className="absolute top-0 right-0 w-1/2 md:w-3/4 h-full bg-blue-50 rounded-bl-[50px] md:rounded-bl-[100px] transform -skew-x-6 md:-skew-x-12"></div>
            <div className="absolute bottom-20 left-0 w-32 h-32 md:w-64 md:h-64 bg-blue-400 rounded-full opacity-10"></div>
            <div className="absolute top-1/3 right-1/4 w-24 h-24 md:w-48 md:h-48 bg-blue-300 rounded-full opacity-15"></div>
          </div>

          <div className="relative z-10 px-4 max-w-7xl mx-auto">
            {/* Main Title Section */}
            <div className="text-center text-blue-800 p-4 sm:p-6 md:p-8 overflow-hidden relative mb-8 sm:mb-12 md:mb-16">
              <div className="relative z-10">
                <h1 className="text-2xl sm:text-3xl md:text-5xl font-extrabold tracking-tight mb-2 sm:mb-3 md:mb-4 text-blue-800">
                  KRYKARD <span className="text-blue-600">SERVO STABILIZERS</span>
                  <span className="block text-blue-500 text-lg sm:text-xl md:text-3xl mt-2">1 Phase</span>
                </h1>

                <p className="text-base sm:text-lg md:text-2xl font-medium mb-4 sm:mb-6 md:mb-8 text-blue-600">
                  Compact Single-Phase Voltage Protection Solutions
                </p>

                <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white font-bold py-2 sm:py-3 md:py-4 px-4 sm:px-6 md:px-8 rounded-lg inline-block shadow-lg transform hover:scale-105 transition-transform duration-300 text-xs sm:text-sm md:text-base">
                  TRUE RMS CORRECTION • COMPACT DESIGN • APPLIANCE PROTECTION
                </div>
              </div>
            </div>

            {/* Hero Content Area - Left content, Right image */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 lg:gap-16 items-center mb-8 sm:mb-12 md:mb-16">
              {/* Left side: Content */}
              <div className="space-y-4 sm:space-y-6 md:space-y-8 order-2 lg:order-1">
                <div>
                  <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-blue-900 mb-2 sm:mb-3 md:mb-4">Compact Voltage Protection</h2>
                  <div className="h-1 sm:h-1.5 w-16 sm:w-20 md:w-24 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full mb-3 sm:mb-4 md:mb-6"></div>
                  <p className="text-sm sm:text-base md:text-lg text-blue-800 leading-relaxed">
                    Compact and reliable voltage protection solution designed to safeguard your valuable appliances from voltage fluctuations with True RMS correction and complete protection features.
                  </p>
                </div>

                <div className="bg-gradient-to-r from-blue-50/50 to-blue-100/70 p-3 sm:p-4 md:p-6 rounded-xl border border-blue-200/30 backdrop-blur-sm">
                  <h3 className="text-base sm:text-lg md:text-xl font-bold mb-2 sm:mb-3 md:mb-4 text-blue-800">Perfect for:</h3>
                  <ul className="space-y-2 sm:space-y-3 md:space-y-4">
                    {[
                      {icon: "🏠", text: "Home Appliances"},
                      {icon: "🏢", text: "Small Offices"},
                      {icon: "🛒", text: "Retail Shops"},
                      {icon: "💻", text: "Computer Systems"},
                      {icon: "📺", text: "Entertainment Systems"}
                    ].map((item, index) => (
                      <li key={index} className="flex items-center group">
                        <span className="text-lg sm:text-xl md:text-2xl mr-2 sm:mr-3 md:mr-4 transform group-hover:scale-110 transition-transform">
                          {item.icon}
                        </span>
                        <span className="text-blue-700 font-medium group-hover:text-blue-600 transition-colors text-xs sm:text-sm md:text-base">
                          {item.text}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="flex flex-wrap gap-3 sm:gap-4">
                  <button
                    onClick={() => {
                      window.location.href = '/contact/sales';
                    }}
                    className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-3 sm:px-4 md:px-6 py-2 sm:py-2.5 md:py-3 rounded-lg shadow-lg flex items-center gap-1 sm:gap-2 transition-all duration-300 text-xs sm:text-sm md:text-base hover:scale-105"
                  >
                    <span>Request Quote</span>
                    <ArrowRight size={16} />
                  </button>

                  <button
                    onClick={() => {
                      const link = document.createElement('a');
                      link.href = '/brochures/servo-stabilizers-1phase-brochure.pdf';
                      link.download = 'Servo-Stabilizers-1Phase-Brochure.pdf';
                      link.click();
                    }}
                    className="border-2 border-blue-600 text-blue-700 hover:bg-blue-50 px-3 sm:px-4 md:px-6 py-2 sm:py-2.5 md:py-3 rounded-lg shadow-lg transition-all duration-300 flex items-center gap-1 sm:gap-2 text-xs sm:text-sm md:text-base hover:scale-105"
                  >
                    <FileText size={16} />
                    <span>View Brochure</span>
                  </button>
                </div>
              </div>

              {/* Right side: Servo Stabilizer Image with Blue Background Design */}
              <div className="relative flex justify-center order-1 lg:order-2">
                {/* Blue gradient background similar to clampmeters */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-200 to-blue-50 rounded-full opacity-20 blur-xl transform scale-90"></div>

                <div className="w-full max-w-3xl h-[300px] sm:h-[500px] md:h-[700px] flex items-center justify-center relative z-10">
                  {/* Additional blue decorative elements */}
                  <div className="absolute top-10 left-10 w-16 h-16 bg-blue-300 rounded-full opacity-20 blur-lg"></div>
                  <div className="absolute bottom-10 right-10 w-24 h-24 bg-blue-400 rounded-full opacity-15 blur-lg"></div>

                  {/* Servo Stabilizer Image */}
                  <img
                    src="/servo-stabilizers/1phase-servo-stabilizer.png"
                    alt="1 Phase Servo Stabilizer"
                    className="max-w-full max-h-full object-contain drop-shadow-2xl transform hover:scale-105 transition-transform duration-700 relative z-10"
                    style={{ filter: "drop-shadow(0 20px 30px rgba(59, 130, 246, 0.3))" }}
                    onError={(e) => {
                      // Fallback to a generic servo stabilizer image or placeholder
                      e.currentTarget.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='300' viewBox='0 0 400 300'%3E%3Crect width='400' height='300' fill='%23f3f4f6'/%3E%3Ctext x='200' y='150' text-anchor='middle' dy='.3em' fill='%236b7280' font-family='Arial, sans-serif' font-size='18'%3E1 Phase Servo Stabilizer%3C/text%3E%3C/svg%3E";
                    }}
                  />
                </div>
              </div>
            </div>

            {/* Key Features Section - Enhanced Modern Design */}
            <div className="mb-16">
              <div className="text-center mb-12">
                <div>
                  <h2 className="text-3xl font-bold text-blue-900 mb-3 inline-block relative">
                    Key Performance Metrics
                    <div className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-blue-400 via-blue-600 to-blue-400"></div>
                  </h2>
                </div>
                <p className="mt-6 text-xl text-blue-700 max-w-2xl mx-auto font-medium">
                  Core specifications that define our 1-phase servo stabilizer excellence
                </p>
              </div>

              {/* Modern 3D Card Layout */}
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6 md:gap-8">
                {keyStats.map((stat, index) => {
                  // Define different gradient colors for each card
                  const gradients = [
                    "from-blue-500 to-blue-600",
                    "from-indigo-500 to-blue-500",
                    "from-blue-600 to-indigo-600",
                    "from-blue-600 to-blue-700"
                  ];

                  // Define different glow effects for each card
                  const glows = [
                    "from-blue-400/20 via-blue-500/10 to-transparent",
                    "from-indigo-400/20 via-blue-500/10 to-transparent",
                    "from-blue-500/20 via-indigo-500/10 to-transparent",
                    "from-blue-600/20 via-blue-700/10 to-transparent"
                  ];

                  return (
                    <div
                      key={index}
                      className="bg-white rounded-2xl shadow-xl relative overflow-hidden group hover:-translate-y-2 transition-transform duration-300"
                    >
                      {/* Top gradient bar */}
                      <div className={`h-2 bg-gradient-to-r ${gradients[index]}`}></div>

                      {/* Background glow effect on hover */}
                      <div className={`absolute inset-0 bg-gradient-to-br ${glows[index]} opacity-0 group-hover:opacity-100 transition-opacity duration-700`}></div>

                      {/* Content */}
                      <div className="p-4 sm:p-6 md:p-8 relative z-10">
                        {/* Animated icon with gradient background */}
                        <div className={`w-12 h-12 sm:w-16 sm:h-16 md:w-20 md:h-20 rounded-xl sm:rounded-2xl bg-gradient-to-r ${gradients[index]} flex items-center justify-center text-white mb-3 sm:mb-4 md:mb-6 shadow-lg mx-auto group-hover:scale-110 group-hover:rotate-3 transition-transform duration-300`}>
                          {stat.icon}
                        </div>

                        {/* Feature value */}
                        <div className="mb-2 sm:mb-3 md:mb-4 text-center">
                          <div className="flex items-center justify-center">
                            <span className="text-3xl sm:text-4xl md:text-5xl font-extrabold bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-blue-500">
                              {stat.value}
                            </span>
                            <span className="text-xl sm:text-2xl md:text-3xl font-bold text-blue-600 ml-1">
                              {stat.suffix}
                            </span>
                          </div>
                        </div>

                        {/* Feature title with hover effect */}
                        <h3 className="text-sm sm:text-base md:text-xl font-bold text-center text-blue-800 group-hover:text-blue-600 transition-colors duration-300">
                          {stat.title}
                        </h3>

                        {/* Decorative dots */}
                        <div className="flex justify-center mt-2 sm:mt-3 md:mt-4 space-x-1">
                          <div className={`w-1 h-1 sm:w-1.5 sm:h-1.5 rounded-full bg-gradient-to-r ${gradients[index]}`}></div>
                          <div className={`w-1 h-1 sm:w-1.5 sm:h-1.5 rounded-full bg-gradient-to-r ${gradients[index]}`}></div>
                          <div className={`w-1 h-1 sm:w-1.5 sm:h-1.5 rounded-full bg-gradient-to-r ${gradients[index]}`}></div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Quality Indicators */}
            <div className="flex items-center justify-center gap-6 mb-8">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 md:w-7 md:h-7 text-yellow-400 fill-current" />
                ))}
                <span className="ml-2 md:ml-4 text-sm md:text-xl text-blue-700 font-medium">Guardian of Your Appliances</span>
              </div>
            </div>
          </div>
        </section>

      {/* Overview Section - Full Width */}
      <div className="w-full bg-white">
        <div className="w-full px-6 md:px-12 lg:px-16 py-16">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-8">
              Advanced Servo Voltage Protection
            </h2>
            <p className="text-xl md:text-2xl text-gray-700 leading-relaxed text-justify max-w-6xl mx-auto">
              Our Servo Controlled Voltage Stabilizers provide superior voltage regulation with True RMS correction, ensuring optimal protection for your valuable appliances and equipment. Built with microprocessor-based control systems for precise measurement and correction.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl shadow-xl p-8 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border border-blue-200">
                <div className="flex flex-col items-center text-center mb-6">
                  <div className="bg-gradient-to-br from-blue-600 to-blue-800 rounded-2xl p-4 shadow-lg mb-4">
                    <div className="text-white">{feature.icon}</div>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900">{feature.title}</h3>
                </div>
                <ul className="space-y-4">
                  {feature.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-green-600 mt-1 mr-3 flex-shrink-0" />
                      <span className="text-base text-gray-800 leading-relaxed text-justify font-medium">{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Product Range Section - Full Width */}
      <div className="w-full bg-gradient-to-br from-blue-100 to-blue-200">
        <div className="w-full px-6 md:px-12 lg:px-16 py-16">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-8">
              Product Range for Your Needs
            </h2>
            <p className="text-xl md:text-2xl text-gray-700 leading-relaxed text-justify max-w-6xl mx-auto">
              Choose from our comprehensive range of voltage stabilizers designed to meet different operating environments and voltage fluctuation patterns.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {productRanges.map((range, index) => (
              <div key={index} className="bg-white rounded-3xl shadow-2xl p-10 hover:shadow-3xl transition-all duration-500 transform hover:scale-105 border-2 border-blue-300">
                <div className="flex items-center mb-8">
                  <div className="bg-gradient-to-br from-blue-600 to-blue-800 rounded-2xl p-5 shadow-xl">
                    <Settings className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 ml-6">{range.title}</h3>
                </div>
                <p className="text-lg text-gray-800 leading-relaxed text-justify font-medium">{range.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Technical Specifications Section - Full Width */}
      <div className="w-full bg-gradient-to-br from-blue-900 to-blue-800">
        <div className="w-full px-6 md:px-12 lg:px-16 py-16">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Technical Specifications
            </h2>
            <p className="text-xl text-blue-100 leading-relaxed text-justify max-w-4xl mx-auto">
              Detailed technical parameters and standards ensuring optimal performance and reliability across all rating ranges.
            </p>
          </div>

          <div className="bg-white rounded-3xl shadow-2xl overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-gradient-to-r from-blue-700 to-blue-800">
                    <th className="px-8 py-6 text-left text-lg font-bold text-white border-b-2 border-blue-600">
                      Parameter
                    </th>
                    <th className="px-8 py-6 text-center text-lg font-bold text-white border-b-2 border-blue-600">
                      1 kVA
                    </th>
                    <th className="px-8 py-6 text-center text-lg font-bold text-white border-b-2 border-blue-600">
                      2-3 kVA
                    </th>
                    <th className="px-8 py-6 text-center text-lg font-bold text-white border-b-2 border-blue-600">
                      4-5 kVA
                    </th>
                    <th className="px-8 py-6 text-center text-lg font-bold text-white border-b-2 border-blue-600">
                      7.5-10 kVA
                    </th>
                    <th className="px-8 py-6 text-center text-lg font-bold text-white border-b-2 border-blue-600">
                      15-20 kVA
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {specifications.map((spec, index) => (
                    <tr key={index} className={`${index % 2 === 0 ? 'bg-blue-50' : 'bg-white'} hover:bg-blue-100 transition-colors duration-200`}>
                      <td className="px-8 py-5 text-base font-bold text-gray-900 border-b border-blue-200 bg-gradient-to-r from-blue-100 to-blue-50">
                        {spec.parameter}
                      </td>
                      <td className="px-8 py-5 text-base text-gray-800 border-b border-blue-200 text-center font-medium">
                        {spec['1kVA']}
                      </td>
                      <td className="px-8 py-5 text-base text-gray-800 border-b border-blue-200 text-center font-medium">
                        {spec['2-3kVA']}
                      </td>
                      <td className="px-8 py-5 text-base text-gray-800 border-b border-blue-200 text-center font-medium">
                        {spec['4-5kVA']}
                      </td>
                      <td className="px-8 py-5 text-base text-gray-800 border-b border-blue-200 text-center font-medium">
                        {spec['7.5-10kVA']}
                      </td>
                      <td className="px-8 py-5 text-base text-gray-800 border-b border-blue-200 text-center font-medium">
                        {spec['15-20kVA']}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="bg-gradient-to-r from-blue-100 to-blue-50 px-8 py-6 border-t-2 border-blue-200">
              <p className="text-base text-gray-800 font-medium flex items-center justify-center">
                <Info className="w-6 h-6 inline mr-3 text-blue-600" />
                All specifications are subject to standard tolerances and testing conditions as per IS 9815 standards.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Call to Action Section - Full Width */}
      <div className="w-full bg-gradient-to-r from-blue-800 via-blue-700 to-blue-900 text-white">
        <div className="w-full px-6 md:px-12 lg:px-16 py-20">
          <div className="text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-8">
              Protect Your Valuable Appliances Today
            </h2>
            <p className="text-xl md:text-2xl text-blue-100 mb-12 max-w-4xl mx-auto leading-relaxed text-justify">
              Choose from our comprehensive range of servo controlled voltage stabilizers designed to meet your specific power protection needs. Get expert consultation and support.
            </p>
            <div className="flex justify-center">
              <button
                onClick={() => {
                  // Redirect to Sales page
                  window.location.href = '/contact/sales';
                }}
                className="bg-blue-600 text-white px-10 py-5 rounded-xl font-bold text-xl hover:bg-blue-500 transition-all duration-300 transform hover:scale-105 shadow-xl"
              >
                <Mail className="w-6 h-6 inline mr-3" />
                Contact Expert
              </button>
            </div>
          </div>
        </div>
      </div>
      </div>

      {/* Add CSS for blue grid pattern and effects */}
      <style dangerouslySetInnerHTML={{
        __html: `
          .bg-grid-pattern {
            background-image:
              linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
            background-size: 20px 20px;
          }

          .text-shadow-blue {
            text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
          }

          .shadow-blue {
            box-shadow: 0 0 30px rgba(59, 130, 246, 0.3);
          }

          .watermark {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='200' height='200' viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Ctext x='50%' y='50%' dominant-baseline='middle' text-anchor='middle' font-family='Arial' font-weight='bold' font-size='24' fill='rgba(59, 130, 246, 0.08)' transform='rotate(-45, 100, 100)'%3EKRYKARD%3C/text%3E%3C/svg%3E");
            background-repeat: repeat;
            background-size: 200px 200px;
            pointer-events: none;
            z-index: -1;
          }
        `
      }} />
    </PageLayout>
  );
};

export default ServoStabilizersPhase1;