import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  ArrowRight,
  Check,
  ChevronRight,
  ArrowDownCircle,
  Zap,
  BarChart,
  Gauge,
  Shield,
  FileText,
  Zap as Lightning,
  ExternalLink
} from "lucide-react";
import PageLayout from "@/components/layout/PageLayout";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

// PDF URL for brochure
const PDF_URL = "/T&M April 2025.pdf";

// Modern Background Component
const ModernBackground = () => {
  return (
    <div className="fixed inset-0 pointer-events-none z-[-1] overflow-hidden bg-gradient-to-br from-yellow-50 to-yellow-100">
      {/* Abstract shapes */}
      <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-yellow-200 rounded-bl-full opacity-30"></div>
      <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-yellow-300 rounded-tr-full opacity-20"></div>

      {/* Subtle grid pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
    </div>
  );
};

// Enhanced Hero Section Component with updated buttons
const HeroSection = ({ onRequestDemo, onViewBrochure }) => {
  return (
    <div className="relative py-6 md:py-12 overflow-hidden font-['Open_Sans']">
      {/* Hero Background Elements - Mobile optimized */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 right-0 w-1/2 md:w-3/4 h-full bg-yellow-50 rounded-bl-[50px] md:rounded-bl-[100px] transform -skew-x-6 md:-skew-x-12"></div>
        <div className="absolute bottom-20 left-0 w-32 h-32 md:w-64 md:h-64 bg-yellow-400 rounded-full opacity-10"></div>
      </div>

      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 items-center">
          {/* Text Content */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-4 text-center lg:text-left"
          >
            <div className="inline-block bg-yellow-400 py-1 px-3 rounded-full mb-2">
              <span className="text-sm md:text-base font-semibold text-gray-900 font-['Open_Sans']">KRYKARD Precision Instruments</span>
            </div>
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight font-['Open_Sans']">
              CLAMP <span className="text-yellow-400">METERS</span>
            </h1>

            <p className="text-base md:text-lg lg:text-xl text-gray-900 leading-relaxed font-medium text-center lg:text-justify max-w-lg mx-auto lg:mx-0 font-['Open_Sans']">
              Professional-grade instruments for accurate electrical measurements without circuit interruption.
            </p>

            <div className="pt-4 flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center lg:justify-start max-w-md sm:max-w-none mx-auto lg:mx-0">
              <Button
                className="w-full sm:w-auto px-4 sm:px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-semibold rounded-lg shadow-md transition duration-300 flex items-center justify-center space-x-2 text-sm sm:text-base"
                onClick={onRequestDemo}
              >
                <span>Request Demo</span>
                <ArrowRight className="ml-2 h-4 sm:h-5 w-4 sm:w-5" />
              </Button>
              <Button
                className="w-full sm:w-auto px-4 sm:px-6 py-3 bg-white border-2 border-yellow-400 text-gray-900 font-semibold rounded-lg shadow-sm transition duration-300 hover:bg-gray-50 flex items-center justify-center space-x-2 text-sm sm:text-base"
                onClick={onViewBrochure}
              >
                <span>View Brochure</span>
                <FileText className="ml-2 h-4 sm:h-5 w-4 sm:w-5" />
              </Button>
            </div>
          </motion.div>

          {/* Product Image */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative order-first lg:order-last"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-200 to-yellow-50 rounded-full opacity-20 blur-xl transform scale-90"></div>
            <motion.div
              animate={{
                y: [0, -10, 0],
              }}
              transition={{
                repeat: Infinity,
                duration: 3,
                ease: "easeInOut"
              }}
              className="relative z-10 flex justify-center"
            >
              <img
                src="/image-removebg-preview.png"
                alt="Krykard Clamp Meter"
                className="max-h-[300px] sm:max-h-[400px] md:max-h-[500px] lg:max-h-[600px] w-auto object-contain drop-shadow-2xl"
              />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

// Animated Product Card with improved styling
const ProductCard = ({
  title,
  modelInfo,
  image,
  clampingDiameter,
  displayInfo,
  features,
  onViewDetailsClick
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="group h-full font-['Open_Sans']"
    >
      <div className={`h-full rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 bg-white border border-gray-100`}>
        {/* Product Image with Colored Background */}
        <div className={`relative p-4 md:p-6 flex justify-center items-center bg-yellow-50 h-48 md:h-56 overflow-hidden group-hover:bg-opacity-80 transition-all duration-700`}>
          <motion.img
            src={image}
            alt={title}
            className="h-36 md:h-48 object-contain z-10 drop-shadow-xl"
            whileHover={{ rotate: [-1, 1, -1], transition: { repeat: Infinity, duration: 2 } }}
          />
          <div className={`absolute top-2 left-2 md:top-4 md:left-4 bg-yellow-400 text-white text-xs md:text-sm font-bold py-1 px-2 md:px-3 rounded-full`}>
            {modelInfo}
          </div>
        </div>

        {/* Product Content */}
        <div className="p-4 space-y-3 font-['Open_Sans']">
          <h3 className="text-base md:text-lg lg:text-xl font-bold text-gray-900 group-hover:text-yellow-600 transition-colors duration-300 text-center md:text-left font-['Open_Sans']">
            {title}
          </h3>

          {/* Key Features */}
          <div className="space-y-2">
            {features.slice(0, 3).map((feature, idx) => (
              <div key={idx} className="flex items-start">
                <Check className="h-4 w-4 text-yellow-400 mt-1 mr-2 flex-shrink-0" />
                <span className="text-gray-900 text-sm md:text-base font-semibold text-justify font-['Open_Sans']">{feature}</span>
              </div>
            ))}
          </div>

          {/* Specs Badge */}
          <div className="flex flex-wrap gap-2 pt-2 justify-center md:justify-start">
            <span className="inline-block bg-yellow-100 rounded-full px-3 py-1 text-xs md:text-sm font-semibold text-gray-700 font-['Open_Sans']">
              {clampingDiameter}
            </span>
            <span className="inline-block bg-yellow-100 rounded-full px-3 py-1 text-xs md:text-sm font-semibold text-gray-700 font-['Open_Sans']">
              {displayInfo}
            </span>
          </div>

          {/* View Details Button */}
          <Button
            onClick={onViewDetailsClick}
            className={`w-full mt-4 py-3 px-4 bg-yellow-400 hover:bg-yellow-500 text-center font-semibold text-gray-900 rounded-lg transition-all duration-300 transform group-hover:-translate-y-1 flex items-center justify-center text-sm md:text-base`}
          >
            <span>View Details</span>
            <ChevronRight className="ml-1 h-4 w-4" />
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

// Feature Highlight Component with improved styling
const FeatureHighlight = ({ icon, title, description }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
      whileHover={{ y: -8, boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)" }}
      className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 p-4 h-full border-b-4 border-yellow-400 font-['Open_Sans']"
    >
      <div className="flex flex-col h-full text-center md:text-left">
        <div className="bg-gradient-to-br from-yellow-400 to-yellow-300 w-12 h-12 rounded-lg flex items-center justify-center mb-3 shadow-md mx-auto md:mx-0">
          {icon}
        </div>
        <h3 className="text-base md:text-lg lg:text-xl font-bold text-gray-900 mb-2 font-['Open_Sans']">{title}</h3>
        <p className="text-gray-900 flex-grow font-medium text-sm md:text-base text-center md:text-justify font-['Open_Sans']">{description}</p>
      </div>
    </motion.div>
  );
};

// SpecItem component for product details
const SpecItem = ({ icon, text }) => (
  <div className="flex items-center space-x-3 py-2 border-b border-yellow-100 font-['Open_Sans']">
    <div className="bg-yellow-100 p-2 rounded-lg">
      {icon}
    </div>
    <span className="text-gray-900 font-semibold text-base">{text}</span>
  </div>
);

// Combined Tab Component for Features and Measurements
const ProductTabContent = ({ activeProductType, activeTab }) => {
  // Features data based on product type
  const features = {
    power: [
      "Clamping diameter: F205: 34 mm, F404: 48 mm, F604: 60 mm",
      "Display: F205: 6000 counts backlit LCD, F404/F604: 10,000 counts backlit LCD",
      "Diode Test (Semiconductor function)",
      "Measures True Inrush, Min/Max, Peak+/Peak-",
      "Audible continuity (adjustable from 1 to 999 Ω)",
      "RELativ ΔX / ΔX/X (%) measurement",
      "Auto range, Hold & Auto power off",
      "Measures kW, kVAr, kVA & PF (F205)",
      "IP 40 (F205), IP 54 (F404 & F604)"
    ],
    solar: [
      "Specially designed for Photo voltaic applications with 1700 V DC",
      "Clamping diameter: F406: 48 mm, F606: 60 mm",
      "Display: 10,000 counts backlit LCD",
      "Diode Test (Semiconductor function)",
      "Measures True Inrush, Min/Max, Peak+/Peak-",
      "Audible continuity (adjustable from 1 to 999 Ω)",
      "RELativ ΔX / ΔX/X (%) measurement",
      "Auto range, Hold & Auto power off",
      "Measures kW, kVAr, kVA & PF"
    ],
    harmonics: [
      "Clamping diameter: F407: 48 mm, F607: 60 mm",
      "Display: 10,000 counts backlit LCD",
      "Measures True Inrush, Min/Max, Peak+/Peak-",
      "Audible continuity (40 Ω)",
      "Auto range, Hold & Auto power off",
      "Measures kW, kVAr, kVA, PF & DPF",
      "Data recording & PC interface",
      "Communication: Bluetooth"
    ]
  };

  // Measurements data based on product type
  const measurements = {
    power: [
      { label: "Voltage V (AC/DC)", value: "F205: Up to 1,200 V AC/DC\nF404 & F604: Up to 1,200 V AC/ 1,700 V DC" },
      { label: "Current I (AC/DC)", value: "F205: Up to 600 A AC/ 900 A DC\nF404: Up to 1,000 A AC/ 1,500 A DC\nF604: Up to 2,000 A AC/ 3,000 A DC" },
      { label: "Accuracy (V & I)", value: "1% of reading + 3 counts" },
      { label: "Resistance", value: "Up to 99.99 kΩ" },
      { label: "Temperature", value: "Up to 1000°C (F404 & F604)" },
      { label: "1 phase & 3 phase power", value: "Up to 600 KW (F205)" }
    ],
    solar: [
      { label: "Voltage V (AC/DC)", value: "Up to 1,200 V AC/1,700 V DC" },
      { label: "Current I (AC/DC)", value: "F406: Up to 1,000 A AC/ 1,500 A DC\nF606: Up to 2,000 A AC/ 3,000 A DC" },
      { label: "Accuracy (V & I)", value: "1% of reading + 3 counts" },
      { label: "Resistance", value: "Up to 99.99 kΩ" },
      { label: "1 phase & 3 phase Power", value: "F406: Up to 1,200 kW\nF606: Up to 2,400 kW" },
      { label: "Voltage & Current THDf/THDr", value: "Available" }
    ],
    harmonics: [
      { label: "Voltage V (AC/DC)", value: "Up to 1,000 V" },
      { label: "Current I (AC/DC)", value: "F407: Up to 1,000 A AC/ 1,500 A DC\nF607: Up to 2,000 A AC/ 3,000 A DC" },
      { label: "Accuracy (V & I)", value: "1% of reading + 3 counts" },
      { label: "Resistance", value: "Up to 99.99 kΩ" },
      { label: "1 phase & 3 phase Power", value: "F407: Up to 1,000 kW\nF607: Up to 2,000 kW" },
      { label: "Harmonic Analysis", value: "Voltage & Current THDf/THDr\nIndividual Harmonics up to 25° order" }
    ]
  };

  if (activeTab === "features") {
    return (
      <div className="bg-white rounded-2xl shadow-lg overflow-hidden font-['Open_Sans']">
        <div className="bg-gradient-to-r from-yellow-400 to-yellow-400 p-3">
          <h3 className="text-xl md:text-2xl font-bold text-center text-white">Salient Features</h3>
        </div>
        <div className="p-4">
          <div className="space-y-1">
            {features[activeProductType].map((feature, idx) => (
              <motion.div
                key={idx}
                initial={{ opacity: 0, x: -10 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: idx * 0.05 }}
                viewport={{ once: true }}
                className="flex items-start p-2 hover:bg-yellow-50 rounded-lg transition-colors duration-300"
              >
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-yellow-100 text-yellow-600 flex items-center justify-center mr-3">
                  <Check className="h-4 w-4" />
                </div>
                <span className="text-gray-900 font-semibold text-sm md:text-base text-justify font-['Open_Sans']">{feature}</span>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    );
  } else {
    return (
      <div className="bg-white rounded-2xl shadow-lg overflow-hidden font-['Open_Sans']">
        <div className="bg-gradient-to-r from-yellow-400 to-yellow-400 p-3">
          <h3 className="text-xl md:text-2xl font-bold text-center text-white">Measurements</h3>
        </div>
        <div className="p-4">
          <div className="grid md:grid-cols-2 gap-4">
            {measurements[activeProductType].map((item, idx) => (
              <motion.div
                key={idx}
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: idx * 0.05 }}
                viewport={{ once: true }}
                className="bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow duration-300 border border-yellow-100"
              >
                <h4 className="font-semibold text-gray-900 mb-2 border-b border-yellow-200 pb-2 text-base font-['Open_Sans']">{item.label}</h4>
                <div className="text-gray-900 font-medium text-sm md:text-base font-['Open_Sans']">
                  {item.value.split('\n').map((line, i) => (
                    <div key={i} className="mb-1 text-justify">{line}</div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    );
  }
};

// Contact Section Component with improved styling
const ContactSection = ({ onContactClick }) => {
  return (
    <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 py-6 px-4 rounded-2xl shadow-lg font-['Open_Sans']">
      <div className="max-w-full mx-auto text-center">
        <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-black mb-3 font-['Open_Sans']">Need More Information?</h2>
        <p className="text-black mb-6 max-w-4xl mx-auto font-semibold text-sm md:text-base lg:text-lg text-center font-['Open_Sans']">
          Our team of experts is ready to help you with product specifications, custom solutions,
          pricing, and any other details you need about the KRYKARD Clamp Meters.
        </p>
        <Button
          className="inline-flex items-center px-6 md:px-8 py-3 md:py-4 bg-yellow-400 hover:bg-yellow-500 text-black font-semibold rounded-lg shadow-md transition duration-300 transform hover:-translate-y-1 text-sm md:text-base"
          onClick={onContactClick}
        >
          Contact Our Experts
          <ArrowRight className="ml-2 h-4 md:h-5 w-4 md:w-5" />
        </Button>
      </div>
    </div>
  );
};

// Comparison Table Component with improved styling
const ComparisonTable = ({ productType }) => {
  const models = {
    power: ['F205', 'F404', 'F604'],
    solar: ['F406', 'F606'],
    harmonics: ['F407', 'F607']
  };

  const features = {
    power: [
      { name: 'Clamping Diameter', values: ['34 mm', '48 mm', '60 mm'] },
      { name: 'Display Resolution', values: ['6000 counts', '10,000 counts', '10,000 counts'] },
      { name: 'Max AC Current', values: ['600 A', '1,000 A', '2,000 A'] },
      { name: 'Max DC Current', values: ['900 A', '1,500 A', '3,000 A'] },
      { name: 'Max Voltage AC/DC', values: ['1,200 V', '1,200/1,700 V', '1,200/1,700 V'] },
      { name: 'Power Measurement', values: ['Yes', 'Yes', 'Yes'] },
      { name: 'IP Rating', values: ['IP 40', 'IP 54', 'IP 54'] }
    ],
    solar: [
      { name: 'Clamping Diameter', values: ['48 mm', '60 mm'] },
      { name: 'Display Resolution', values: ['10,000 counts', '10,000 counts'] },
      { name: 'Max AC Current', values: ['1,000 A', '2,000 A'] },
      { name: 'Max DC Current', values: ['1,500 A', '3,000 A'] },
      { name: 'Max DC Voltage', values: ['1,700 V', '1,700 V'] },
      { name: 'Power Measurement', values: ['Yes', 'Yes'] },
      { name: 'PV Specific Features', values: ['Yes', 'Yes'] }
    ],
    harmonics: [
      { name: 'Clamping Diameter', values: ['48 mm', '60 mm'] },
      { name: 'Display Resolution', values: ['10,000 counts', '10,000 counts'] },
      { name: 'Max AC Current', values: ['1,000 A', '2,000 A'] },
      { name: 'Max DC Current', values: ['1,500 A', '3,000 A'] },
      { name: 'Max Voltage', values: ['1,000 V', '1,000 V'] },
      { name: 'Harmonic Analysis', values: ['Up to 25°', 'Up to 25°'] },
      { name: 'Bluetooth Connection', values: ['Yes', 'Yes'] }
    ]
  };

  return (
    <div className="bg-white rounded-2xl shadow-lg overflow-hidden mt-6 font-['Open_Sans']">
      <div className="bg-gradient-to-r from-yellow-400 to-yellow-400 p-3">
        <h3 className="text-xl md:text-2xl font-bold text-center text-white">Model Comparison</h3>
      </div>
      <div className="p-2 sm:p-4 overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead>
            <tr>
              <th className="px-2 sm:px-4 py-2 bg-yellow-50 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider rounded-tl-lg">Feature</th>
              {models[productType].map((model, idx) => (
                <th key={idx} className={`px-2 sm:px-4 py-2 bg-yellow-50 text-center text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider ${idx === models[productType].length - 1 ? 'rounded-tr-lg' : ''}`}>
                  {model}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {features[productType].map((feature, idx) => (
              <motion.tr
                key={idx}
                initial={{ opacity: 0, y: 5 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: idx * 0.05 }}
                viewport={{ once: true }}
                className={idx % 2 === 0 ? 'bg-white hover:bg-yellow-50 transition-colors duration-200' : 'bg-gray-50 hover:bg-yellow-50 transition-colors duration-200'}
              >
                <td className="px-2 sm:px-4 py-3 text-xs sm:text-sm md:text-base font-medium text-gray-900 break-words">{feature.name}</td>
                {feature.values.map((value, i) => (
                  <td key={i} className="px-2 sm:px-4 py-3 text-xs sm:text-sm md:text-base text-gray-900 text-center font-medium break-words">
                    {value}
                  </td>
                ))}
              </motion.tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

// Applications Section Component with improved styling
const ApplicationsSection = () => {
  const applications = [
    {
      title: "Electrical Installation",
      icon: <Zap className="h-6 md:h-8 w-6 md:w-8 text-yellow-600" />,
      description: "Used for testing electrical installations, measuring current flow, and identifying overloaded circuits."
    },
    {
      title: "Solar Power Systems",
      icon: <Lightning className="h-6 md:h-8 w-6 md:w-8 text-yellow-600" />,
      description: "Perfect for solar installations, measuring DC current output from panels and AC current delivery to the grid."
    },
    {
      title: "Power Quality Analysis",
      icon: <BarChart className="h-6 md:h-8 w-6 md:w-8 text-yellow-600" />,
      description: "Analyze harmonics, power factor, and other power quality parameters to ensure efficient energy use."
    },
    {
      title: "Industrial Maintenance",
      icon: <Gauge className="h-6 md:h-8 w-6 md:w-8 text-yellow-600" />,
      description: "Regular maintenance of industrial equipment, identifying potential failures before they occur."
    }
  ];

  return (
    <div className="py-6 md:py-8 bg-gradient-to-br from-yellow-50 to-white rounded-2xl my-6 shadow-lg font-['Open_Sans']">
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-6 md:mb-8">
          <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-gray-900 mb-3 font-['Open_Sans']">Application Areas</h2>
          <p className="text-sm md:text-base lg:text-lg text-gray-800 max-w-4xl mx-auto font-medium text-center font-['Open_Sans']">
            Our clamp meters are designed for a wide range of electrical measurement applications
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {applications.map((app, idx) => (
            <motion.div
              key={idx}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: idx * 0.1 }}
              viewport={{ once: true }}
              className="bg-white p-4 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-2 border-b-4 border-yellow-400 text-center md:text-left"
            >
              <div className="bg-yellow-100 w-12 md:w-16 h-12 md:h-16 rounded-lg flex items-center justify-center mb-3 text-yellow-600 mx-auto md:mx-0">
                {app.icon}
              </div>
              <h3 className="text-base md:text-lg lg:text-xl font-semibold mb-2 text-gray-900 font-['Open_Sans']">{app.title}</h3>
              <p className="text-gray-900 font-semibold text-sm md:text-base text-center md:text-justify font-['Open_Sans']">{app.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Main Clamp Meters Component
const ClampMeters = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const [activeProductType, setActiveProductType] = useState("power");
  const [activeDetailTab, setActiveDetailTab] = useState("features");
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navigate = useNavigate();
  const location = useLocation();

  // Effect to check URL parameters for initial tab and product type
  useEffect(() => {
    try {
      const params = new URLSearchParams(location.search);
      const tab = params.get('tab');
      const product = params.get('product');
      const detailTab = params.get('detailTab');

      if (tab && ['overview', 'details', 'applications'].includes(tab)) {
        setActiveTab(tab);
      }
      if (product && ['power', 'solar', 'harmonics'].includes(product)) {
        setActiveProductType(product);
      }
      if (detailTab && ['features', 'measurements', 'comparison'].includes(detailTab)) {
        setActiveDetailTab(detailTab);
      }

      // If we're viewing product details, scroll to the product section
      if (tab === 'details' && product) {
        // Use requestAnimationFrame instead of setTimeout to prevent blinking
        requestAnimationFrame(() => {
          // Find the product detail section by ID and scroll directly to it
          const productDetailSection = document.getElementById('product-detail-section');
          if (productDetailSection) {
            productDetailSection.scrollIntoView({ behavior: 'auto', block: 'start' });
          }
        });
      }
    } catch (error) {
      console.error("Error reading URL parameters:", error);
    }
  }, [location]);

  // Handler for Request Demo button
  const handleRequestDemo = () => {
    try {
      navigate("/contact/sales");
    } catch (error) {
      console.error("Navigation error:", error);
      // Fallback to window.location if navigate fails
      window.location.href = "/contact/sales";
    }
  };

  // Handler for View Brochure button
  const handleViewBrochure = () => {
    try {
      window.open(PDF_URL, '_blank');
    } catch (error) {
      console.error("Error opening brochure:", error);
    }
  };

  // Handler for View Details button
  const handleViewDetails = (productType: string) => {
    try {
      // Update state
      setActiveProductType(productType);
      setActiveTab('details');
      setActiveDetailTab('features');

      // Update URL - this will directly show the details tab
      navigate(`?tab=details&product=${productType}&detailTab=features`, { replace: true });
    } catch (error) {
      console.error("handleViewDetails error:", error);
      // Fallback - just set the states without URL manipulation
      setActiveProductType(productType);
      setActiveTab('details');
      setActiveDetailTab('features');
    }
  };

  // Power Clamp Meter data
  const powerClampData = {
    title: "Power Clamp Meter ",
    modelInfo: "F205/F404/F604",
    image: "/F205-Power-Clamp.png",
    clampingDiameter: "34-60 mm",
    displayInfo: "10,000 counts LCD",
    features: [
      "True RMS reading on AC and AC+DC",
      "AC and DC voltage up to 1,000 V",
      "Current: up to 2,000 A AC / 3,000 A DC",
      "Measures kW, kVAr, kVA & PF",
      "Auto range, Hold & Auto power off"
    ],
    colors: {
      primary: 'yellow-400',
      secondary: 'yellow-100'
    }
  };

  // Solar Clamp Meter data
  const solarClampData = {
    title: "Solar Clamp Meter",
    modelInfo: "F406/F606",
    image: "/F403-Power-Clamp.png",
    clampingDiameter: "48-60 mm",
    displayInfo: "10,000 counts LCD",
    features: [
      "Specially designed for Photo voltaic applications",
      "True RMS reading on AC and AC+DC",
      "AC and DC voltage up to 1,700 V DC",
      "Current: up to 2,000 A AC / 3,000 A DC",
      "Measures kW, kVAr, kVA & PF"
    ],
    colors: {
      primary: 'yellow-400',
      secondary: 'yellow-100'
    }
  };

  // Power & Harmonics Clamp Meter data
  const harmonicsClampData = {
    title: "Power & Harmonics Clamp Meter",
    modelInfo: "F407/F607",
    image: "/F407-Power-Clamp.png",
    clampingDiameter: "48-60 mm",
    displayInfo: "10,000 counts LCD",
    features: [
      "True RMS reading on AC and AC+DC",
      "Harmonics up to 25th order",
      "Bluetooth Communication",
      "Data recording & PC interface",
      "Measures kW, kVAr, kVA, PF & DPF"
    ],
    colors: {
      primary: 'yellow-400',
      secondary: 'yellow-100'
    }
  };

  // Product Type Options
  const productOptions = [
    { id: "power", label: "Power Clamp", models: "F205/F404/F604" },
    { id: "solar", label: "Solar Clamp", models: "F406/F606" },
    { id: "harmonics", label: "Harmonics Clamp", models: "F407/F607" }
  ];

  // Get active product data
  const getActiveProductData = () => {
    if (activeProductType === "power") return powerClampData;
    if (activeProductType === "solar") return solarClampData;
    return harmonicsClampData;
  };

  // Navigation tabs data
  const navTabs = [
    { id: "overview", label: "Overview", icon: <Gauge className="h-5 w-5" /> },
    { id: "details", label: "Product Details", icon: <Zap className="h-5 w-5" /> },
    { id: "applications", label: "Applications", icon: <Shield className="h-5 w-5" /> }
  ];

  return (
    <PageLayout
      title="Clamp Meters"
      subtitle=""
      category="measure"
    >
      <style>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
      <div className="font-['Open_Sans']">
      {/* Modern Background */}
      <ModernBackground />

      {/* Premium Modern Navigation Tabs - Responsive Design */}
      <div className="sticky top-0 z-30 bg-white shadow-lg border-b border-gray-100 font-['Open_Sans']">
        <div className="max-w-full mx-auto px-2">
          {/* Desktop Navigation */}
          <div className="hidden md:flex justify-center py-2">
            <div className="bg-gray-50 p-1.5 rounded-full flex shadow-sm">
              {navTabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => {
                    setActiveTab(tab.id);
                    if (tab.id === "details") {
                      navigate(`?tab=${tab.id}&product=${activeProductType}&detailTab=${activeDetailTab}`, { replace: true });
                    } else {
                      navigate(`?tab=${tab.id}`, { replace: true });
                    }
                    setIsMobileMenuOpen(false);
                  }}
                  className={cn(
                    "relative px-6 py-2.5 font-medium rounded-full transition-all duration-300 flex items-center mx-1 overflow-hidden text-base",
                    activeTab === tab.id
                      ? "bg-gradient-to-r from-yellow-400 to-yellow-400 text-black shadow-md transform -translate-y-0.5"
                      : "text-gray-700 hover:bg-yellow-50 hover:text-yellow-500"
                  )}
                >
                  <div className="flex items-center relative z-10">
                    <span className="mr-2">{tab.icon}</span>
                    <span>{tab.label}</span>
                  </div>
                  {activeTab === tab.id && (
                    <motion.div
                      layoutId="navIndicator"
                      className="absolute inset-0 bg-gradient-to-r from-yellow-400 to-yellow-400 -z-0"
                      initial={false}
                      transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                    />
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Mobile Navigation */}
          <div className="md:hidden py-2 flex justify-between items-center">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="text-gray-700 hover:text-yellow-500 focus:outline-none"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            <span className="font-semibold text-gray-900 text-lg">
              {navTabs.find(tab => tab.id === activeTab)?.label}
            </span>

            <div className="w-6"></div> {/* Spacer for balanced layout */}
          </div>

          {/* Mobile Menu Dropdown */}
          <div className={`md:hidden overflow-hidden transition-all duration-300 ease-in-out ${isMobileMenuOpen ? 'max-h-60' : 'max-h-0'}`}>
            <div className="bg-white rounded-lg shadow-lg p-2 mb-2">
              {navTabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => {
                    setActiveTab(tab.id);
                    if (tab.id === "details") {
                      navigate(`?tab=${tab.id}&product=${activeProductType}&detailTab=${activeDetailTab}`, { replace: true });
                    } else {
                      navigate(`?tab=${tab.id}`, { replace: true });
                    }
                    setIsMobileMenuOpen(false);
                  }}
                  className={`w-full text-left px-4 py-3 rounded-lg my-1 flex items-center ${
                    activeTab === tab.id
                      ? "bg-yellow-50 text-yellow-600 font-medium"
                      : "text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  <span className="mr-3">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {activeTab === "overview" && (
        <>
          {/* Hero Section */}
          <HeroSection
            onRequestDemo={handleRequestDemo}
            onViewBrochure={handleViewBrochure}
          />

          {/* Key Features Overview */}
          <div className="py-6 md:py-8 bg-gradient-to-br from-yellow-50 to-white font-['Open_Sans']">
            <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-6 md:mb-8">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                  viewport={{ once: true }}
                >
                  <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-yellow-600 mb-3 font-['Open_Sans']">Why Choose Our Clamp Meters?</h2>
                  <p className="mt-3 text-sm md:text-base lg:text-lg text-gray-800 max-w-4xl mx-auto font-medium text-center font-['Open_Sans']">
                    Precision-engineered instruments that combine accuracy, durability, and advanced features
                  </p>
                </motion.div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
                <FeatureHighlight
                  icon={<Gauge className="h-6 w-6 text-white" />}
                  title="Precision Accuracy"
                  description="Our clamp meters provide 1% reading accuracy with 3-count resolution, ensuring reliable measurements for professional applications."
                />

                <FeatureHighlight
                  icon={<Zap className="h-6 w-6 text-white" />}
                  title="Advanced Power Analysis"
                  description="Measure real power (kW), reactive power (kVAr), apparent power (kVA) and power factor (PF) for comprehensive electrical assessments."
                />

                <FeatureHighlight
                  icon={<Shield className="h-6 w-6 text-white" />}
                  title="Non-Intrusive Measurement"
                  description="Measure current without breaking the circuit, with clamping diameters up to 60mm to accommodate a wide range of conductors."
                />
              </div>
            </div>
          </div>

          {/* Products Section */}
          <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6 md:py-8 font-['Open_Sans']">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-6 md:mb-8"
            >
              <span className="inline-block bg-yellow-100 text-yellow-800 px-4 py-1 rounded-full text-sm md:text-base font-semibold mb-3 font-['Open_Sans']">
                PROFESSIONAL SERIES
              </span>
              <h2 className="text-xl md:text-2xl lg:text-4xl font-bold mb-4 text-gray-900 font-['Open_Sans']">
                Our Clamp Meter Series
              </h2>
              <p className="max-w-4xl mx-auto text-gray-800 text-sm md:text-base lg:text-lg font-medium text-center font-['Open_Sans']">
                Choose the perfect clamp meter for your electrical measurement needs.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
              <ProductCard
                {...powerClampData}
                onViewDetailsClick={() => handleViewDetails("power")}
              />
              <ProductCard
                {...solarClampData}
                onViewDetailsClick={() => handleViewDetails("solar")}
              />
              <ProductCard
                {...harmonicsClampData}
                onViewDetailsClick={() => handleViewDetails("harmonics")}
              />
            </div>
          </div>

          {/* Contact Information Section */}
          <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <ContactSection onContactClick={handleRequestDemo} />
          </div>
        </>
      )}

      {activeTab === "details" && (
        <div id="product-detail-section" className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6 md:py-8 font-['Open_Sans']">
          {/* Enhanced Product Type Selector - Compact Version */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-gradient-to-r from-yellow-50 to-white rounded-xl shadow-md p-4 mb-6 relative overflow-hidden"
          >
            <div className="absolute top-0 right-0 w-32 md:w-64 h-32 md:h-64 bg-yellow-200 rounded-full opacity-20 transform translate-x-1/2 -translate-y-1/2 blur-3xl"></div>

            <h2 className="text-base md:text-lg lg:text-xl font-bold text-gray-900 mb-3 relative z-10 text-center md:text-left font-['Open_Sans']">
              Select <span className="text-yellow-400">Model Series</span>
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 relative z-10">
              {productOptions.map((option) => (
                <motion.button
                  key={option.id}
                  whileHover={{ y: -3, boxShadow: "0 8px 20px -5px rgba(0, 0, 0, 0.1)" }}
                  whileTap={{ y: 0 }}
                  onClick={() => {
                    setActiveProductType(option.id);
                    navigate(`?tab=details&product=${option.id}&detailTab=${activeDetailTab}`, { replace: true });
                  }}
                  className={`relative rounded-lg transition-all duration-300 overflow-hidden ${
                    activeProductType === option.id
                      ? "ring-1 ring-yellow-400"
                      : "hover:ring-1 hover:ring-yellow-400"
                  }`}
                >
                  <div className={`h-full py-3 px-3 flex flex-col items-center text-center ${
                    activeProductType === option.id
                      ? "bg-yellow-400 text-black"
                      : "bg-white hover:bg-yellow-100"
                  }`}>
                    <div className="mb-2">
                      {option.id === "power" && (
                        <Gauge className={`h-6 md:h-8 w-6 md:w-8 ${activeProductType === option.id ? "text-black" : "text-yellow-400"}`} />
                      )}
                      {option.id === "solar" && (
                        <Lightning className={`h-6 md:h-8 w-6 md:w-8 ${activeProductType === option.id ? "text-black" : "text-yellow-400"}`} />
                      )}
                      {option.id === "harmonics" && (
                        <BarChart className={`h-6 md:h-8 w-6 md:w-8 ${activeProductType === option.id ? "text-black" : "text-yellow-400"}`} />
                      )}
                    </div>

                    <h3 className={`text-sm md:text-base lg:text-lg font-bold mb-1 font-['Open_Sans'] ${activeProductType === option.id ? "text-white" : "text-gray-900"}`}>
                      {option.label}
                    </h3>

                    <div className={`text-xs md:text-sm font-['Open_Sans'] ${activeProductType === option.id ? "text-white opacity-80" : "text-gray-500"}`}>
                      {option.models}
                    </div>

                    {activeProductType === option.id && (
                      <div className="mt-2 bg-white bg-opacity-20 rounded-full px-3 py-0.5 text-xs md:text-sm font-semibold">
                        Selected
                      </div>
                    )}
                  </div>
                </motion.button>
              ))}
            </div>
          </motion.div>          {/* Enhanced Product Detail Section */}
          <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
            <div className="md:col-span-5">
              <div className="sticky top-24">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                  className="bg-gradient-to-br from-white to-yellow-50 rounded-2xl shadow-lg p-6 mb-4 relative overflow-hidden"
                >
                  {/* Background decoration */}
                  <div className="absolute top-0 right-0 w-1/2 h-1/2 bg-yellow-200 rounded-full opacity-10 blur-3xl"></div>
                  <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-yellow-100 rounded-full opacity-10 blur-3xl"></div>

                  {/* Product badge */}
                  <div className="absolute top-4 left-4 bg-yellow-400 text-white px-3 py-1 rounded-full text-sm font-semibold z-10">
                    {getActiveProductData().modelInfo}
                  </div>

                  {/* Image container with glow effect */}
                  <div className="relative mb-6 mt-3">
                    <div className="absolute inset-0 bg-gradient-to-br from-yellow-200 to-yellow-50 rounded-full opacity-30 blur-xl transform scale-90"></div>
                    <motion.div
                      animate={{ y: [0, -10, 0] }}
                      transition={{ repeat: Infinity, duration: 3, ease: "easeInOut" }}
                      className="relative z-10 flex justify-center items-center py-6"
                    >
                      <img
                        src={getActiveProductData().image}
                        alt={getActiveProductData().title}
                        className="max-h-56 w-auto object-contain drop-shadow-2xl transform transition-transform duration-500 hover:scale-110"
                      />
                    </motion.div>
                  </div>

                  {/* Product details */}
                  <div className="text-center mb-6">
                    <h3 className="text-xl md:text-2xl font-bold text-gray-900 mb-2 font-['Open_Sans']">{getActiveProductData().title}</h3>
                    <div className="flex justify-center space-x-3 mb-4">
                      {getActiveProductData().features.slice(0, 1).map((feature, idx) => (
                        <span key={idx} className="inline-block bg-yellow-100 text-yellow-700 px-3 py-1 rounded-full text-sm font-medium font-['Open_Sans']">
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Specs cards */}
                  <div className="grid grid-cols-2 gap-3 mb-6">
                    <div className="bg-white p-3 rounded-xl shadow-sm border border-yellow-100 transform transition-transform duration-300 hover:-translate-y-1 hover:shadow-md">
                      <div className="flex items-center mb-2">
                        <Gauge className="h-5 w-5 text-yellow-400 mr-2" />
                        <span className="text-sm font-medium text-gray-500 font-['Open_Sans']">Clamping Diameter</span>
                      </div>
                      <span className="font-semibold text-gray-900 text-base font-['Open_Sans']">{getActiveProductData().clampingDiameter}</span>
                    </div>
                    <div className="bg-white p-3 rounded-xl shadow-sm border border-yellow-100 transform transition-transform duration-300 hover:-translate-y-1 hover:shadow-md">
                      <div className="flex items-center mb-2">
                        <Zap className="h-5 w-5 text-yellow-400 mr-2" />
                        <span className="text-sm font-medium text-gray-500 font-['Open_Sans']">Display</span>
                      </div>
                      <span className="font-semibold text-gray-900 text-base font-['Open_Sans']">{getActiveProductData().displayInfo}</span>
                    </div>
                  </div>

                  {/* View Brochure button */}
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      className="w-full bg-gradient-to-r from-yellow-400 to-yellow-400 hover:from-yellow-400 hover:to-yellow-400 text-gray-900 font-semibold rounded-lg shadow-md transition-all duration-300 flex items-center justify-center py-3 text-base"
                      onClick={handleViewBrochure}
                    >
                      <span>View Product Brochure</span>
                      <FileText className="ml-2 h-5 w-5" />
                    </Button>
                  </motion.div>
                </motion.div>
              </div>
            </div>

            <div className="md:col-span-7">
              {/* Enhanced Detail Tabs Navigation */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="bg-white rounded-xl shadow-lg mb-6 overflow-hidden"
              >
                <div className="flex border-b overflow-x-auto scrollbar-hide">
                  {[
                    { id: "features", label: "Features", icon: <Shield className="h-4 sm:h-5 w-4 sm:w-5" /> },
                    { id: "measurements", label: "Measurements", icon: <Gauge className="h-4 sm:h-5 w-4 sm:w-5" /> },
                    { id: "comparison", label: "Comparison", icon: <BarChart className="h-4 sm:h-5 w-4 sm:w-5" /> }
                  ].map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => {
                        setActiveDetailTab(tab.id);
                        navigate(`?tab=details&product=${activeProductType}&detailTab=${tab.id}`, { replace: true });
                      }}
                      className={`px-3 sm:px-4 py-3 font-medium whitespace-nowrap flex items-center transition-all duration-300 text-sm sm:text-base min-w-fit ${
                        activeDetailTab === tab.id
                          ? "bg-yellow-50 border-b-2 border-yellow-400 text-yellow-700"
                          : "text-gray-700 hover:text-yellow-600 hover:bg-yellow-100"
                      }`}
                    >
                      <span className="mr-1 sm:mr-2">{tab.icon}</span>
                      <span className="hidden xs:inline sm:inline">{tab.label}</span>
                      <span className="xs:hidden sm:hidden">{tab.label.slice(0, 4)}</span>
                    </button>
                  ))}
                </div>
              </motion.div>

              {/* Tab Content with enhanced styling */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="transform origin-top"
              >                {activeDetailTab === "comparison" ? (
                  <ComparisonTable productType={activeProductType} />
                ) : (
                  <ProductTabContent activeProductType={activeProductType} activeTab={activeDetailTab} />
                )}
              </motion.div>
            </div>
          </div>

          {/* Enhanced Contact Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
            className="mt-12"
          >
            <ContactSection onContactClick={handleRequestDemo} />
          </motion.div>
        </div>
      )}

      {activeTab === "applications" && (
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6 md:py-8 font-['Open_Sans']">
          <ApplicationsSection />

          <div className="mt-6 md:mt-8 bg-white rounded-2xl shadow-lg p-4 md:p-6">
            <h2 className="text-lg md:text-xl lg:text-2xl font-bold text-gray-900 mb-4 text-center md:text-left font-['Open_Sans']">Industry-Specific Solutions</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 mb-6">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="flex flex-col md:flex-row items-center md:items-start space-y-3 md:space-y-0 md:space-x-4 text-center md:text-left"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-600 flex-shrink-0">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-2 font-['Open_Sans']">Commercial Buildings</h3>
                  <p className="text-gray-800 font-medium text-sm md:text-base text-center md:text-justify font-['Open_Sans']">
                    Monitor power quality, identify harmonic issues, and ensure efficient energy consumption in commercial buildings.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="flex flex-col md:flex-row items-center md:items-start space-y-3 md:space-y-0 md:space-x-4 text-center md:text-left"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-600 flex-shrink-0">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-2 font-['Open_Sans']">Manufacturing</h3>
                  <p className="text-gray-800 font-medium text-sm md:text-base text-center md:text-justify font-['Open_Sans']">
                    Troubleshoot electrical issues in production equipment, reduce downtime, and improve energy efficiency.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="flex flex-col md:flex-row items-center md:items-start space-y-3 md:space-y-0 md:space-x-4 text-center md:text-left"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-600 flex-shrink-0">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-2 font-['Open_Sans']">Renewable Energy</h3>
                  <p className="text-gray-800 font-medium text-sm md:text-base text-center md:text-justify font-['Open_Sans']">
                    Measure and optimize solar panel performance, troubleshoot inverter issues, and verify system outputs.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="flex flex-col md:flex-row items-center md:items-start space-y-3 md:space-y-0 md:space-x-4 text-center md:text-left"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-600 flex-shrink-0">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V4a1 1 0 011-1h2a1 1 0 011 1v14a1 1 0 01-1 1h-2a1 1 0 01-1-1z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-2 font-['Open_Sans']">Electric Vehicles</h3>
                  <p className="text-gray-800 font-medium text-sm md:text-base text-center md:text-justify font-['Open_Sans']">
                    Test charging stations, analyze power consumption, and verify electrical systems in EV infrastructure.
                  </p>
                </div>
              </motion.div>
            </div>

            {/* View Brochure button */}
            <div className="flex justify-center mt-6">
              <Button
                className="w-full sm:w-auto px-6 py-3 bg-gradient-to-r from-yellow-400 to-yellow-400 hover:from-yellow-500 hover:to-yellow-500 text-gray-900 font-semibold rounded-lg shadow-md transition duration-300 flex items-center justify-center space-x-2 text-sm md:text-base"
                onClick={handleViewBrochure}
              >
                <span>View Brochure</span>
                <FileText className="ml-2 h-4 md:h-5 w-4 md:w-5" />
              </Button>
            </div>
          </div>

          <div className="mt-6 md:mt-8">
            <ContactSection onContactClick={handleRequestDemo} />
          </div>
        </div>
      )}

      {/* No PDF Viewer Modal - Using direct link instead */}
      </div>
    </PageLayout>
  );
};

export default ClampMeters;