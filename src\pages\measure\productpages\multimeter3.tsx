import React, { useState } from 'react';

// Define types for better type safety
type Product = {
  id: number;
  name: string;
  color: string;
  image: string;
  category: string;
  features: string[];
  specs: {
    parameter: string;
    range: string;
    accuracy: string;
  }[];
};

type ProductCardProps = {
  product: Product;
  onClick: () => void;
};

type ProductDetailProps = {
  product: Product;
};

type FeatureItemProps = {
  children: React.ReactNode;
  small?: boolean;
};

type ApplicationCardProps = {
  title: string;
  description: string;
};

type SocialButtonProps = {
  icon: string;
  light?: boolean;
};

const MultimeterProductPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<number>(0);
  
  const products: Product[] = [
    {
      id: 1,
      name: "ATEST M 3105",
      color: "gray",
      image: "/api/placeholder/400/500",
      category: "TRMS AC DC DIGITAL MULTIMETERS",
      features: [
        "600V ACV/DCV",
        "True RMS on ACV",
        "6000 count digital large display",
        "Auto ranging",
        "Relative function",
        "Non-Contact Voltage detection",
        "Continuity with buzzer and display",
        "μF/nF function",
        "High Voltage view",
        "Double size screen for clarity",
        "Safety standard CAT III 600V"
      ],
      specs: [
        { parameter: "DC Voltage", range: "60, 600, 600V", accuracy: "0.5" },
        { parameter: "AC Voltage", range: "60, 600, 600V", accuracy: "1" },
        { parameter: "Diode", range: "600mA", accuracy: "0.5" },
        { parameter: "ACA, DCA", range: "6A, 10A", accuracy: "1.5" },
        { parameter: "μCAP", range: "400μA, 4000μA", accuracy: "0.8" },
        { parameter: "Resistance", range: "600Ω-40MΩ", accuracy: "0.5" },
        { parameter: "Capacitance", range: "3nF ~ 3000μF", accuracy: "2" },
        { parameter: "Frequency Counter", range: "100Hz ~ 40KHz", accuracy: "0.1" },
        { parameter: "Diode Test", range: "3.000V", accuracy: "1" }
      ]
    },
    {
      id: 2,
      name: "ATEST M 3106",
      color: "gray",
      image: "/api/placeholder/400/500",
      category: "TRMS AC DC DIGITAL MULTIMETERS",
      features: [
        "600V ACV/DCV",
        "True RMS on ACV & DCA",
        "6000 count digital large display",
        "μF/nF function",
        "True Hold for capture peak readings",
        "Relative function",
        "Non-Contact Voltage detection",
        "Continuity with buzzer and display",
        "High Voltage view",
        "Double size screen for clarity",
        "Safety standard CAT III 600V"
      ],
      specs: [
        { parameter: "DC Voltage", range: "60, 600, 600V", accuracy: "0.5" },
        { parameter: "AC Voltage", range: "60, 600, 600V", accuracy: "1" },
        { parameter: "Diode", range: "600mA", accuracy: "0.5" },
        { parameter: "ACA, DCA", range: "6A, 10A", accuracy: "1.5" },
        { parameter: "μCAP", range: "400μA, 4000μA", accuracy: "0.8" },
        { parameter: "Resistance", range: "600Ω-40MΩ", accuracy: "0.5" },
        { parameter: "Capacitance", range: "3nF ~ 3000μF", accuracy: "2" },
        { parameter: "Frequency Counter", range: "100Hz ~ 40KHz", accuracy: "0.1" },
        { parameter: "Temperature", range: "-40°C ~ 400°C", accuracy: "1" },
        { parameter: "Diode Test", range: "3.000V", accuracy: "1" }
      ]
    },
    {
      id: 3,
      name: "ATEST M 2590",
      color: "red",
      image: "/api/placeholder/400/500",
      category: "TRMS AC DC DIGITAL MULTIMETERS",
      features: [
        "1000V ACV/DCV",
        "True RMS measurements on ACV",
        "6000 count digital and 60 segments analog display",
        "LoZ for prevent false reading from ghost voltage",
        "True Hold for capture peak readings",
        "Relative function",
        "Non-Contact Voltage detection",
        "Double with built-in magnetic holder",
        "Continuity with buzzer and display",
        "Battery capacity indication in segments",
        "Safety standard CAT IV 600V"
      ],
      specs: [
        { parameter: "DC Voltage", range: "600mV, 1000V", accuracy: "0.5" },
        { parameter: "AC Voltage", range: "600mV, 1000V", accuracy: "1" },
        { parameter: "Auto V-LoZ", range: "600mV, 1000V", accuracy: "2" },
        { parameter: "DC Current", range: "600μA", accuracy: "2" },
        { parameter: "AC Current", range: "600μA", accuracy: "1.5" },
        { parameter: "Resistance", range: "600Ω-40MΩ", accuracy: "0.5" },
        { parameter: "Capacitance", range: "1.000μF ~ 4,000μF", accuracy: "1.5" },
        { parameter: "Frequency Counter", range: "100Hz ~ 500KHz", accuracy: "0.1" },
        { parameter: "Diode Test", range: "3.000V", accuracy: "" }
      ]
    },
    {
      id: 4,
      name: "ATEST M 2591",
      color: "red",
      image: "/api/placeholder/400/500",
      category: "TRMS AC DC DIGITAL MULTIMETERS",
      features: [
        "1000V ACV/DCV",
        "True RMS measurements on ACV",
        "6000 count digital and 60 segments analog display",
        "μA measurements on both AC and DC",
        "LoZ for prevent false reading from ghost voltage",
        "True Hold for capture peak readings",
        "Relative function",
        "Non-Contact Voltage detection",
        "Double with built-in magnetic holder",
        "Continuity with buzzer and display",
        "Battery capacity indication in segments",
        "Safety standard CAT IV 600V"
      ],
      specs: [
        { parameter: "DC Voltage", range: "600mV, 1000V", accuracy: "0.5" },
        { parameter: "AC Voltage", range: "600mV, 1000V", accuracy: "1" },
        { parameter: "Auto V-LoZ", range: "600mV, 1000V", accuracy: "2" },
        { parameter: "DC Current", range: "600μA", accuracy: "2" },
        { parameter: "AC Current", range: "600μA", accuracy: "1.5" },
        { parameter: "Resistance", range: "600Ω-40MΩ", accuracy: "0.5" },
        { parameter: "Capacitance", range: "1μF ~ 10mF", accuracy: "1.5" },
        { parameter: "Frequency Counter", range: "100Hz ~ 500KHz", accuracy: "0.1" },
        { parameter: "Temperature", range: "-40°C ~ 400°C", accuracy: "1" },
        { parameter: "Diode Test", range: "3.000V", accuracy: "" }
      ]
    },
    {
      id: 5,
      name: "ATEST M 2592",
      color: "red",
      image: "/api/placeholder/400/500",
      category: "TRMS AC DC DIGITAL MULTIMETERS",
      features: [
        "1000V ACV/DCV",
        "True RMS measurements on ACV/ACA",
        "(AC+DC)V and (AC+DC)mV measurements",
        "(AC+DC)A and (AC+DC)mA measurements",
        "6000 count digital and 62 segments analog display",
        "LoZ for prevent false reading from ghost voltage",
        "Non-Contact Voltage detection",
        "11A/1000V & 400mA/1000V High Energy Fuses",
        "Shock proof from 4 feet drops",
        "Safety standard CAT IV 600V"
      ],
      specs: [
        { parameter: "DC Voltage", range: "60mV, 1000V", accuracy: "0.08" },
        { parameter: "AC Voltage", range: "60mV, 1000V", accuracy: "0.8" },
        { parameter: "Auto V-LoZ", range: "600V, 1000V", accuracy: "0.8" },
        { parameter: "DC Current", range: "60mA, 10A", accuracy: "0.8" },
        { parameter: "AC Current", range: "60mA, 10A", accuracy: "1.2" },
        { parameter: "Resistance", range: "600Ω-40MΩ", accuracy: "0.8" },
        { parameter: "Capacitance", range: "1μF-10mF", accuracy: "1.2" },
        { parameter: "Frequency Counter", range: "100Hz ~ 100KHz", accuracy: "0.1" },
        { parameter: "Temperature", range: "-40°C ~ 400°C", accuracy: "1" },
        { parameter: "Diode Test", range: "3.000V", accuracy: "" }
      ]
    }
  ];

  // Add CSS for scrollbar hiding
  const scrollbarHideStyle = `
    .scrollbar-hide::-webkit-scrollbar {
      display: none;
    }
    .scrollbar-hide {
      -ms-overflow-style: none;
      scrollbar-width: none;
    }
  `;

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {/* Add style tag for scrollbar hiding */}
      <style>{scrollbarHideStyle}</style>
      
      {/* Header */}
      <header className="bg-white py-4 shadow-sm border-b">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold">ATEST Digital Multimeters</h1>
            <div className="flex space-x-1">
              <SocialButton icon="facebook" />
              <SocialButton icon="twitter" />
              <SocialButton icon="linkedin" />
              <SocialButton icon="youtube" />
              <SocialButton icon="message" />
            </div>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex overflow-x-auto whitespace-nowrap py-2 scrollbar-hide">
            <button 
              className={`px-4 py-2 mx-1 ${activeTab === 0 ? 'bg-yellow-100 text-yellow-800 font-medium rounded-md' : 'hover:bg-gray-100 rounded-md'}`}
              onClick={() => setActiveTab(0)}
            >
              All Products
            </button>
            {products.map((product) => (
              <button 
                key={product.id}
                className={`px-4 py-2 mx-1 ${activeTab === product.id ? 'bg-yellow-100 text-yellow-800 font-medium rounded-md' : 'hover:bg-gray-100 rounded-md'}`}
                onClick={() => setActiveTab(product.id)}
              >
                {product.name}
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="flex-grow py-8">
        <div className="container mx-auto px-4">
          {activeTab === 0 ? (
            <div className="space-y-16">
              {/* Gray Series Section */}
              <div className="mb-8">
                <h2 className="text-2xl font-bold mb-6 pb-2 border-b">Gray Series - General Purpose Multimeters</h2>
                <div className="grid md:grid-cols-2 gap-8">
                  {products.filter(p => p.color === "gray").map(product => (
                    <ProductCard 
                      key={product.id} 
                      product={product} 
                      onClick={() => setActiveTab(product.id)}
                    />
                  ))}
                </div>
              </div>
              
              {/* Red Series Section */}
              <div>
                <h2 className="text-2xl font-bold mb-6 pb-2 border-b">Red Series - Professional Multimeters</h2>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {products.filter(p => p.color === "red").map(product => (
                    <ProductCard 
                      key={product.id} 
                      product={product} 
                      onClick={() => setActiveTab(product.id)}
                    />
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <ProductDetail product={products.find(p => p.id === activeTab) || products[0]} />
          )}
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-6">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="mb-4 md:mb-0">© 2025 ATEST Digital Multimeters. All rights reserved.</p>
            <div className="flex space-x-4">
              <SocialButton icon="facebook" light />
              <SocialButton icon="twitter" light />
              <SocialButton icon="linkedin" light />
              <SocialButton icon="youtube" light />
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

// Product Card Component
const ProductCard: React.FC<ProductCardProps> = ({ product, onClick }) => {
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
      <div className="p-4">
        <div className="flex flex-col items-center mb-4">
          <img 
            src={product.image} 
            alt={`ATEST ${product.name} Digital Multimeter`} 
            className="h-64 object-contain"
          />
          <h3 className="text-xl font-semibold mt-4">{product.name}</h3>
        </div>
        
        <div className="mb-4">
          <ul className="space-y-2">
            {product.features.slice(0, 4).map((feature, index) => (
              <FeatureItem key={index} small>
                {feature}
              </FeatureItem>
            ))}
          </ul>
        </div>

        <div className="flex justify-center mt-4">
          <button 
            onClick={onClick}
            className="px-6 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 transition w-full"
          >
            View Details
          </button>
        </div>
      </div>
    </div>
  );
};

// Product Detail Component
const ProductDetail: React.FC<ProductDetailProps> = ({ product }) => {
  return (
    <div className="space-y-8">
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="flex flex-col lg:flex-row">
          <div className="lg:w-1/3 p-8 flex justify-center items-center bg-gray-50">
            <img 
              src={product.image} 
              alt={`ATEST ${product.name} Digital Multimeter`} 
              className="max-h-96 object-contain"
            />
          </div>
          <div className="lg:w-2/3 p-8">
            <h2 className="text-4xl font-bold mb-6">
              <span className="text-gray-700">TRMS AC DC </span>
              <span className="text-yellow-500">DIGITAL MULTIMETERS</span>
            </h2>
            <h3 className="text-2xl font-medium mb-6">Model: {product.name}</h3>
            
            <div className="mb-8">
              <h4 className="text-xl font-medium mb-4">Features</h4>
              <ul className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {product.features.map((feature, index) => (
                  <FeatureItem key={index}>
                    {feature}
                  </FeatureItem>
                ))}
              </ul>
            </div>

            <div className="flex gap-4">
              <button className="px-8 py-2 bg-white text-yellow-500 border border-yellow-500 rounded-md hover:bg-yellow-50 transition">
                ENQUIRE
              </button>
              <button className="px-8 py-2 bg-white text-yellow-500 border border-yellow-500 rounded-md hover:bg-yellow-50 transition">
                BROCHURE
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Specifications Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="p-8">
          <h3 className="text-2xl font-medium mb-6">Technical Specifications</h3>
          
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead className="bg-yellow-500 text-white">
                <tr>
                  <th className="p-3 text-left">PARAMETER</th>
                  <th className="p-3 text-left">RANGE</th>
                  <th className="p-3 text-left">ACCURACY %</th>
                </tr>
              </thead>
              <tbody>
                {product.specs.map((spec, index) => (
                  <tr key={index} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                    <td className="p-3 border">{spec.parameter}</td>
                    <td className="p-3 border">{spec.range}</td>
                    <td className="p-3 border">{spec.accuracy}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Key Applications */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="p-8">
          <h3 className="text-2xl font-medium mb-4">Key Applications</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <ApplicationCard 
              title="Electrical Maintenance" 
              description="Ideal for general electrical system troubleshooting and maintenance in residential and commercial settings."
            />
            <ApplicationCard 
              title="Electronics Testing" 
              description="Perfect for testing electronic components and circuits with precision and reliability."
            />
            <ApplicationCard 
              title="HVAC Systems" 
              description="Suitable for diagnosing and maintaining heating, ventilation, and air conditioning systems."
            />
            {product.features.some((f) => f.includes("Temperature")) && (
              <ApplicationCard 
                title="Temperature Monitoring" 
                description="Built-in temperature measurement capabilities for monitoring thermal conditions in various applications."
              />
            )}
            {product.features.some((f) => f.includes("data logging")) && (
              <ApplicationCard 
                title="Data Logging" 
                description="Record measurements over time for detailed analysis and documentation of electrical parameters."
              />
            )}
            <ApplicationCard 
              title="Safety Verification" 
              description={`CAT ${product.features.find((f) => f.includes('CAT'))?.split('CAT ')[1] || 'IV 600V'} rated for safe operation in demanding environments.`}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

// Helper Components
const FeatureItem: React.FC<FeatureItemProps> = ({ children, small }) => (
  <li className="flex items-start gap-2">
    <div className={`mt-1 flex-shrink-0 ${small ? 'w-4 h-4' : 'w-5 h-5'} rounded-full bg-yellow-500 flex items-center justify-center text-white`}>
      ✓
    </div>
    <span className={small ? 'text-sm' : ''}>{children}</span>
  </li>
);

const ApplicationCard: React.FC<ApplicationCardProps> = ({ title, description }) => (
  <div className="bg-gray-50 rounded-lg p-4 border border-gray-200 hover:border-yellow-300 transition-colors">
    <h4 className="font-medium text-lg mb-2">{title}</h4>
    <p className="text-gray-600 text-sm">{description}</p>
  </div>
);

// Improved SocialButton component with proper icon rendering
const SocialButton: React.FC<SocialButtonProps> = ({ icon, light }) => {
  const baseClasses = "w-8 h-8 flex items-center justify-center rounded-full";
  const lightClasses = light ? "bg-gray-700 hover:bg-gray-600" : "bg-gray-200 hover:bg-gray-300 text-gray-700";
  
  // Icon rendering function
  const renderIcon = () => {
    switch(icon) {
      case "facebook":
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M18 2h-3a5 5 0 00-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 011-1h3z"></path>
          </svg>
        );
      case "twitter":
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M23 3a10.9 10.9 0 01-3.14 1.53 4.48 4.48 0 00-7.86 3v1A10.66 10.66 0 013 4s-4 9 5 13a11.64 11.64 0 01-7 2c9 5 20 0 20-11.5a4.5 4.5 0 00-.08-.83A7.72 7.72 0 0023 3z"></path>
          </svg>
        );
      case "linkedin":
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M16 8a6 6 0 016 6v7h-4v-7a2 2 0 00-2-2 2 2 0 00-2 2v7h-4v-7a6 6 0 016-6zM2 9h4v12H2z"></path>
            <circle cx="4" cy="4" r="2"></circle>
          </svg>
        );
      case "youtube":
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"></path>
          </svg>
        );
      case "message":
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M21 11.5a8.38 8.38 0 01-.9 3.8 8.5 8.5 0 01-7.6 4.7 8.38 8.38 0 01-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 01-.9-3.8 8.5 8.5 0 014.7-7.6 8.38 8.38 0 013.8-.9h.5a8.48 8.48 0 018 8v.5z"></path>
          </svg>
        );
      default:
        return null;
    }
  };
  
  return (
    <a href="#" className={`${baseClasses} ${lightClasses} transition-colors duration-200`}>
      {renderIcon()}
    </a>
  );
};

export default MultimeterProductPage;