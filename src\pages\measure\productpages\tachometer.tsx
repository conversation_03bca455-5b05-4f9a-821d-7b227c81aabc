import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import Layout from "@/components/layout/Layout";
import { Button } from "@/components/ui/button";
// import PdfViewer from "@/components/ui/pdf-viewer";

interface ProductSpec {
  label: string;
  description: string;
}

interface TableRow {
  parameter: string;
  range: string;
  accuracy: string;
}

interface Product {
  id: string;
  name: string;
  title: string;
  subtitle?: string;
  model: string;
  image: string;
  description?: string;
  features: string[];
  specs: ProductSpec[];
  hasRangeTable?: boolean;
  rangeData?: TableRow[];
}

const TachometerProductPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('tachometer-ca1725');
  
  const products: Product[] = [
    {
      id: 'tachometer-ca1725',
      name: 'Tachometer',
      title: 'TACHO',
      subtitle: 'METERS',
      model: 'C.A 1725-1727',
      image: '/images/tachometer-ca1725.png',
      features: [
        'Models -C.A 1725-1727',
        'With its rotary switch, the CA 1725 is simple to use and offers numerous measurement possibilities with or without contact.',
        'Measurement by photo-reflection for no-contact measurement: a reflective strip is positioned on the part to be tested.',
        'Contact measurement with accessories: a mechanical adapter (cylinder, cone or calibrated wheel) converts the rotation speed into pulses which are then measured by the tachometer.',
        'The digital display shows a 42-segment bargraph, the functions selected, the alarm thresholds, etc.',
        'The rotary switch gives direct access to 7 different measurement units (RPM, Hz, etc.) and each key on the keyboard corresponds to a function (MIN MAX, Smoothing, alarms, etc.).'
      ],
      specs: [
        { label: 'Measurement method', description: 'Photo-reflection (non-contact) and Contact measurement with accessories' },
        { label: 'Measurement units', description: 'RPM, Hz, m/min, ft/min, in/min, and others accessible via rotary switch' },
        { label: 'Display', description: '42-segment bargraph with digital readout and function indicators' },
        { label: 'Measurement range', description: '6 to 100,000 RPM (optical) / 6 to 25,000 RPM (contact)' },
        { label: 'Functions', description: 'MIN, MAX, Smoothing, Alarms, Memory functions' },
        { label: 'Operation', description: 'Simple rotary switch for unit selection and keyboard for functions' },
        { label: 'Accessories included', description: 'Reflective strips, mechanical adapters (cylinder, cone, calibrated wheel)' }
      ]
    },
    {
      id: 'tachometer-digital',
      name: 'Digital Tachometer',
      title: 'DIGITAL TACHO',
      subtitle: 'METERS',
      model: 'DT-6236B',
      image: '/images/tachometer-digital.png',
      features: [
        'Digital tachometer with dual measurement modes',
        'Contactless measurement using laser technology',
        'Contact measurement with various adaptors',
        'Auto range with high accuracy',
        'Memory for Last/Max/Min readings',
        'Large LCD display with backlight',
        'Low battery indicator',
        'Auto power off function'
      ],
      specs: [
        { label: 'Measurement method', description: 'Optical (laser) and Contact with mechanical adapters' },
        { label: 'Measurement range (optical)', description: '2.5 to 99,999 RPM' },
        { label: 'Measurement range (contact)', description: '0.5 to 19,999 RPM' },
        { label: 'Accuracy', description: '±(0.05% + 1 digit)' },
        { label: 'Resolution', description: '0.1 RPM (under 1,000 RPM), 1 RPM (over 1,000 RPM)' },
        { label: 'Sampling time', description: '1 second (over 6 RPM)' },
        { label: 'Display', description: '5-digit LCD with function indicators and backlight' },
        { label: 'Memory', description: 'Last/Maximum/Minimum value recording' }
      ]
    },
    {
      id: 'tachometer-mechanical',
      name: 'Mechanical Tachometer',
      title: 'MECHANICAL TACHO',
      subtitle: 'METERS',
      model: 'MT-200',
      image: '/images/tachometer-mechanical.png',
      features: [
        'Mechanical tachometer with analog display',
        'No batteries required for operation',
        'Direct contact measurement',
        'Robust construction for industrial environments',
        'Multiple scale readings',
        'Supplied with various contact tips',
        'Carrying case included'
      ],
      specs: [
        { label: 'Measurement method', description: 'Direct mechanical contact only' },
        { label: 'Measurement range', description: '0 to 10,000 RPM' },
        { label: 'Accuracy', description: '±1% of full scale' },
        { label: 'Scale', description: 'Dual scale with RPM and linear speed' },
        { label: 'Operation', description: 'Push-to-operate mechanism with auto-reset' },
        { label: 'Construction', description: 'Metal casing with hardened contact points' },
        { label: 'Accessories', description: 'Various contact tips, extension shaft, carrying case' }
      ]
    }
  ];
  
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };
  
  const activeProduct = products.find(product => product.id === activeTab);

  return (
    <Layout>
      <div className="pt-24 md:pt-32">
        {/* Product Tabs */}
        <div className="bg-white border-b">
          <div className="container mx-auto px-4">
            <div className="flex overflow-x-auto py-4 space-x-6">
              {products.map(product => (
                <button
                  key={product.id}
                  className={`whitespace-nowrap px-4 py-2 font-medium rounded-md transition-colors ${
                    activeTab === product.id 
                      ? 'bg-yellow-500 text-white' 
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => handleTabChange(product.id)}
                >
                  {product.name}
                </button>
              ))}
            </div>
          </div>
        </div>
        
        {/* Main Content */}
        {activeProduct && (
          <main className="flex-grow container mx-auto px-4 py-8">
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              {/* Product Hero Section */}
              <div className="relative">
                <div className={`absolute inset-0 bg-gray-100`}></div>
                <div className="relative flex flex-col md:flex-row p-6 md:p-12 items-center">
                  <div className="w-full md:w-1/3 flex justify-center mb-8 md:mb-0">
                    <div className="bg-white p-4 rounded-lg shadow-md transform hover:scale-105 transition-transform duration-300">
                      <img 
                        src={activeProduct.image} 
                        alt={activeProduct.name} 
                        className="max-h-80 object-contain"
                      />
                    </div>
                  </div>
                  <div className="w-full md:w-2/3 md:pl-12">
                    <div className="flex flex-col">
                      <h1 className="text-4xl font-normal text-gray-700 mb-2">
                        {activeProduct.title}
                      </h1>
                      {activeProduct.subtitle && (
                        <h2 className="text-4xl font-bold text-yellow-500 mb-6">
                          {activeProduct.subtitle}
                        </h2>
                      )}
                    </div>
                    <p className="text-lg font-medium mb-4">Model: {activeProduct.model}</p>
                    
                    {/* Features List */}
                    <div className="mb-6">
                      <ul className="space-y-3">
                        {activeProduct.features.map((feature, index) => (
                          <li key={index} className="flex items-start">
                            <div className="flex-shrink-0 h-5 w-5 rounded-full bg-yellow-500 mt-1 mr-3"></div>
                            <span className="text-gray-700">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div className="flex space-x-4 mt-6">
                      <Button variant="outline" className="border-yellow-500 text-yellow-500 hover:bg-yellow-50 rounded-full">
                        ENQUIRE
                      </Button>
                      <Button variant="outline" className="border-yellow-500 text-yellow-500 hover:bg-yellow-50 rounded-full">
                        BROCHURE
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Product Specifications Section */}
              <div className="p-6 md:p-12 border-t border-gray-200">
                <h3 className="text-2xl font-bold text-gray-700 mb-8">Specifications</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {activeProduct.specs.map((spec, index) => (
                    <div key={index} className="flex items-start">
                      <div className="flex-shrink-0 flex items-start">
                        <div className="h-2 w-2 rounded-full bg-gray-500 mt-2 mr-2"></div>
                      </div>
                      <div>
                        <span className="font-bold text-gray-700">{spec.label}: </span>
                        <span className="text-gray-600">{spec.description}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              {/* Applications Section */}
              <div className="p-6 md:p-12 bg-gray-50 border-t border-gray-200">
                <h3 className="text-2xl font-bold text-gray-700 mb-8">Applications</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                      </svg>
                    </div>
                    <h4 className="text-lg font-bold text-gray-700 mb-2">Industrial Machinery</h4>
                    <p className="text-gray-600">Ideal for measuring rotation speeds in motors, turbines, pumps, and other industrial equipment for maintenance and optimization.</p>
                  </div>
                  
                  <div className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                      </svg>
                    </div>
                    <h4 className="text-lg font-bold text-gray-700 mb-2">Automotive</h4>
                    <p className="text-gray-600">Perfect for automotive engineers and mechanics to measure engine RPM, fan speeds, and other rotating components.</p>
                  </div>
                  
                  <div className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                      </svg>
                    </div>
                    <h4 className="text-lg font-bold text-gray-700 mb-2">Research & Development</h4>
                    <p className="text-gray-600">Used in R&D laboratories to test and verify rotation speeds in prototypes and experimental equipment.</p>
                  </div>
                </div>
              </div>
              
              {/* Accessories Section */}
              <div className="p-6 md:p-12 border-t border-gray-200">
                <h3 className="text-2xl font-bold text-gray-700 mb-8">Available Accessories</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                    <div className="p-4">
                      <div className="w-full h-40 bg-gray-100 rounded flex items-center justify-center mb-4">
                        <svg className="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                      </div>
                      <h4 className="text-lg font-bold text-gray-700 mb-2">Reflective Strips</h4>
                      <p className="text-gray-600 text-sm mb-4">Adhesive reflective strips for non-contact measurements. Pack of 15 strips.</p>
                      <Button className="w-full bg-yellow-500 text-white hover:bg-yellow-600">
                        View Details
                      </Button>
                    </div>
                  </div>
                  
                  <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                    <div className="p-4">
                      <div className="w-full h-40 bg-gray-100 rounded flex items-center justify-center mb-4">
                        <svg className="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                      </div>
                      <h4 className="text-lg font-bold text-gray-700 mb-2">Contact Adapter Set</h4>
                      <p className="text-gray-600 text-sm mb-4">Set of mechanical adapters including cone, cylinder, and calibrated wheel.</p>
                      <Button className="w-full bg-yellow-500 text-white hover:bg-yellow-600">
                        View Details
                      </Button>
                    </div>
                  </div>
                  
                  <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                    <div className="p-4">
                      <div className="w-full h-40 bg-gray-100 rounded flex items-center justify-center mb-4">
                        <svg className="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20.618 5.984A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016zM12 9v2m0 4h.01"></path>
                        </svg>
                      </div>
                      <h4 className="text-lg font-bold text-gray-700 mb-2">Protective Case</h4>
                      <p className="text-gray-600 text-sm mb-4">Hard carrying case with foam inserts for tachometer and accessories.</p>
                      <Button className="w-full bg-yellow-500 text-white hover:bg-yellow-600">
                        View Details
                      </Button>
                    </div>
                  </div>
                  
                  <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                    <div className="p-4">
                      <div className="w-full h-40 bg-gray-100 rounded flex items-center justify-center mb-4">
                        <svg className="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                      </div>
                      <h4 className="text-lg font-bold text-gray-700 mb-2">Calibration Certificate</h4>
                      <p className="text-gray-600 text-sm mb-4">Factory calibration certificate for compliance with quality standards.</p>
                      <Button className="w-full bg-yellow-500 text-white hover:bg-yellow-600">
                        View Details
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Call to Action */}
              <div className="p-6 md:p-12 bg-gray-800 text-white text-center">
                <h3 className="text-2xl font-bold mb-4">Need Professional Rotation Measurement Solutions?</h3>
                <p className="text-gray-300 mb-6 max-w-2xl mx-auto">Contact our team of experts to find the right tachometer for your specific rotation speed measurement needs.</p>
                <Button asChild className="px-8 py-3 bg-yellow-500 text-white hover:bg-yellow-600">
                  <Link to="/contact">REQUEST A QUOTE</Link>
                </Button>
              </div>
            </div>
          </main>
        )}
      </div>
    </Layout>
  );
};

export default TachometerProductPage;