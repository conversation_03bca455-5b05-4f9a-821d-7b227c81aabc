import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ArrowLeft, ArrowRight, FileText, Check, Zap, Activity, Cpu, BarChart, Shield } from 'lucide-react';
import PageLayout from '@/components/layout/PageLayout';
import { Button } from '@/components/ui/button';

// PDF URL for brochures
const PDF_URL = "https://example.com/brochure.pdf";

// Product data
const productData = {
  alm20: {
    model: "ALM 20",
    title: "Single-Phase Power Quality Analyzer",
    channels: "Single-Phase",
    image: "/alm20.png",
    description: "The ALM 20 is a compact single-phase power quality analyzer designed for basic power measurements and troubleshooting. It offers essential functionality for electricians and maintenance technicians.",
    hardwareSpecs: [
      { icon: <Cpu className="h-5 w-5 text-yellow-500" />, text: "Single-phase measurement" },
      { icon: <Activity className="h-5 w-5 text-yellow-500" />, text: "Voltage range: 0-600V AC" },
      { icon: <Zap className="h-5 w-5 text-yellow-500" />, text: "Current range: 0-1000A (with clamp)" },
      { icon: <Shield className="h-5 w-5 text-yellow-500" />, text: "CAT III 600V safety rating" },
    ],
    capabilitySpecs: [
      { icon: <BarChart className="h-5 w-5 text-yellow-500" />, text: "Power factor measurement" },
      { icon: <Activity className="h-5 w-5 text-yellow-500" />, text: "Harmonic analysis up to 25th order" },
      { icon: <Zap className="h-5 w-5 text-yellow-500" />, text: "Energy consumption monitoring" },
      { icon: <Shield className="h-5 w-5 text-yellow-500" />, text: "Voltage sag/swell detection" },
    ],
    fullSpecs: [
      { category: "General", specs: [
        { name: "Display", value: "Color LCD, 320 x 240 pixels" },
        { name: "Memory", value: "8GB internal storage" },
        { name: "Battery Life", value: "8 hours continuous operation" },
        { name: "Dimensions", value: "220 x 120 x 80 mm" },
        { name: "Weight", value: "1.2 kg" },
      ]},
      { category: "Electrical", specs: [
        { name: "Voltage Range", value: "0-600V AC" },
        { name: "Current Range", value: "0-1000A (with clamp)" },
        { name: "Frequency Range", value: "45-65 Hz" },
        { name: "Power Factor", value: "0.00 to 1.00" },
        { name: "Harmonics", value: "Up to 25th order" },
      ]},
    ]
  },
  alm31: {
    model: "ALM 31",
    title: "Three-Phase Power Quality Analyzer",
    channels: "Three-Phase",
    image: "/alm31.png",
    description: "The ALM 31 is a professional three-phase power quality analyzer with advanced features for comprehensive power quality analysis. It's designed for industrial applications and power distribution networks.",
    hardwareSpecs: [
      { icon: <Cpu className="h-5 w-5 text-yellow-500" />, text: "Three-phase measurement" },
      { icon: <Activity className="h-5 w-5 text-yellow-500" />, text: "Voltage range: 0-1000V AC" },
      { icon: <Zap className="h-5 w-5 text-yellow-500" />, text: "Current range: 0-3000A (with clamp)" },
      { icon: <Shield className="h-5 w-5 text-yellow-500" />, text: "CAT IV 600V safety rating" },
    ],
    capabilitySpecs: [
      { icon: <BarChart className="h-5 w-5 text-yellow-500" />, text: "Advanced harmonic analysis up to 50th order" },
      { icon: <Activity className="h-5 w-5 text-yellow-500" />, text: "Transient capture" },
      { icon: <Zap className="h-5 w-5 text-yellow-500" />, text: "Flicker measurement" },
      { icon: <Shield className="h-5 w-5 text-yellow-500" />, text: "Unbalance analysis" },
    ],
    fullSpecs: [
      { category: "General", specs: [
        { name: "Display", value: "Color TFT, 640 x 480 pixels" },
        { name: "Memory", value: "16GB internal storage" },
        { name: "Battery Life", value: "6 hours continuous operation" },
        { name: "Dimensions", value: "280 x 180 x 90 mm" },
        { name: "Weight", value: "2.1 kg" },
      ]},
      { category: "Electrical", specs: [
        { name: "Voltage Range", value: "0-1000V AC" },
        { name: "Current Range", value: "0-3000A (with clamp)" },
        { name: "Frequency Range", value: "42-69 Hz" },
        { name: "Power Factor", value: "0.000 to 1.000" },
        { name: "Harmonics", value: "Up to 50th order" },
      ]},
    ]
  },
  alm36: {
    model: "ALM 36",
    title: "Advanced Power Quality Analyzer",
    channels: "Three-Phase + Neutral",
    image: "/alm36.png",
    description: "The ALM 36 is our flagship power quality analyzer with comprehensive measurement capabilities and advanced analysis features. It's designed for power quality professionals and energy consultants.",
    hardwareSpecs: [
      { icon: <Cpu className="h-5 w-5 text-yellow-500" />, text: "Three-phase + neutral measurement" },
      { icon: <Activity className="h-5 w-5 text-yellow-500" />, text: "Voltage range: 0-1500V AC/DC" },
      { icon: <Zap className="h-5 w-5 text-yellow-500" />, text: "Current range: 0-6000A (with clamp)" },
      { icon: <Shield className="h-5 w-5 text-yellow-500" />, text: "CAT IV 1000V safety rating" },
    ],
    capabilitySpecs: [
      { icon: <BarChart className="h-5 w-5 text-yellow-500" />, text: "Full harmonic analysis up to 100th order" },
      { icon: <Activity className="h-5 w-5 text-yellow-500" />, text: "High-speed transient capture" },
      { icon: <Zap className="h-5 w-5 text-yellow-500" />, text: "Power quality according to IEC 61000-4-30 Class A" },
      { icon: <Shield className="h-5 w-5 text-yellow-500" />, text: "Advanced event detection and recording" },
    ],
    fullSpecs: [
      { category: "General", specs: [
        { name: "Display", value: "Color touchscreen, 800 x 600 pixels" },
        { name: "Memory", value: "32GB internal storage + SD card slot" },
        { name: "Battery Life", value: "8 hours continuous operation" },
        { name: "Dimensions", value: "300 x 200 x 100 mm" },
        { name: "Weight", value: "2.5 kg" },
      ]},
      { category: "Electrical", specs: [
        { name: "Voltage Range", value: "0-1500V AC/DC" },
        { name: "Current Range", value: "0-6000A (with clamp)" },
        { name: "Frequency Range", value: "40-70 Hz" },
        { name: "Power Factor", value: "0.000 to 1.000" },
        { name: "Harmonics", value: "Up to 100th order" },
      ]},
    ]
  },
  ca8345: {
    model: "CA 8345",
    title: "Professional Power Quality Analyzer",
    channels: "Three-Phase + Neutral",
    image: "/ca8345.png",
    description: "The CA 8345 is a professional-grade power quality analyzer with comprehensive measurement capabilities and compliance with international standards. It's ideal for industrial and utility applications.",
    hardwareSpecs: [
      { icon: <Cpu className="h-5 w-5 text-yellow-500" />, text: "Three-phase + neutral measurement" },
      { icon: <Activity className="h-5 w-5 text-yellow-500" />, text: "Voltage range: 0-1000V AC/DC" },
      { icon: <Zap className="h-5 w-5 text-yellow-500" />, text: "Current range: 0-10000A (with clamp)" },
      { icon: <Shield className="h-5 w-5 text-yellow-500" />, text: "CAT IV 1000V safety rating" },
    ],
    capabilitySpecs: [
      { icon: <BarChart className="h-5 w-5 text-yellow-500" />, text: "Full harmonic analysis up to 63rd order" },
      { icon: <Activity className="h-5 w-5 text-yellow-500" />, text: "Transient capture up to 12kHz" },
      { icon: <Zap className="h-5 w-5 text-yellow-500" />, text: "IEC 61000-4-30 Class A compliance" },
      { icon: <Shield className="h-5 w-5 text-yellow-500" />, text: "Inrush current measurement" },
    ],
    fullSpecs: [
      { category: "General", specs: [
        { name: "Display", value: "Color touchscreen, 1024 x 600 pixels" },
        { name: "Memory", value: "64GB internal storage + USB port" },
        { name: "Battery Life", value: "10 hours continuous operation" },
        { name: "Dimensions", value: "290 x 190 x 95 mm" },
        { name: "Weight", value: "2.3 kg" },
      ]},
      { category: "Electrical", specs: [
        { name: "Voltage Range", value: "0-1000V AC/DC" },
        { name: "Current Range", value: "0-10000A (with clamp)" },
        { name: "Frequency Range", value: "40-69 Hz" },
        { name: "Power Factor", value: "0.000 to 1.000" },
        { name: "Harmonics", value: "Up to 63rd order" },
      ]},
    ]
  }
};

// Spec Item component
const SpecItem = ({ icon, text }: { icon: React.ReactNode; text: string }) => (
  <div className="flex items-center space-x-2 md:space-x-3 py-2 md:py-3 border-b border-yellow-100">
    <div className="bg-yellow-100 p-1.5 md:p-2 rounded-lg flex-shrink-0">
      {icon}
    </div>
    <span className="text-gray-800 font-medium text-sm md:text-base">{text}</span>
  </div>
);

// Main component
const PowerQualityAnalyzerDetail = () => {
  const { productId } = useParams<{ productId: string }>();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<string>('overview');
  
  // Get product data based on URL parameter
  const product = productData[productId as keyof typeof productData];
  
  useEffect(() => {
    // If product doesn't exist, redirect to main page
    if (!product) {
      navigate('/measure/power-quality-analyzers');
    }
    
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, [product, navigate]);
  
  // Handler for Request Demo button
  const handleRequestDemo = () => {
    navigate("/contact/sales");
  };

  // Handler for View Brochure button
  const handleViewBrochure = () => {
    window.open(PDF_URL, '_blank');
  };
  
  // Handler for Back button
  const handleBack = () => {
    navigate('/measure/power-quality-analyzers');
  };
  
  if (!product) {
    return <div>Loading...</div>;
  }

  return (
    <PageLayout title="Power Quality Analyzers" subtitle="Detailed product information" category="measure">
      <div className="mb-8">
        <Button 
          variant="outline" 
          onClick={handleBack}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to All Analyzers
        </Button>
      </div>
      
      <div className="bg-gradient-to-br from-yellow-50 to-white rounded-3xl shadow-xl overflow-hidden border border-yellow-100 mb-12">
        <div className="absolute top-0 right-0 w-96 h-96 bg-yellow-200 rounded-full opacity-20 -z-10 blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-yellow-200 rounded-full opacity-20 -z-10 blur-3xl"></div>

        <div className="flex flex-col lg:flex-row gap-8 px-4 md:px-6 py-8 md:py-12">
          <motion.div
            className="lg:flex-[1.2] flex justify-center items-center"
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="relative w-full flex justify-center">
              <motion.div
                className="relative"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.5 }}
              >
                <motion.div
                  className="absolute -inset-4 bg-yellow-200 rounded-full opacity-20 blur-xl z-0"
                  animate={{
                    scale: [1, 1.1, 1],
                    opacity: [0.2, 0.3, 0.2],
                  }}
                  transition={{
                    repeat: Infinity,
                    duration: 5,
                    ease: "easeInOut"
                  }}
                />
                <motion.img
                  src={product.image}
                  alt={product.model}
                  className="w-auto h-auto object-contain relative z-10 max-h-[500px] md:max-h-[800px] lg:max-h-[900px] max-w-full"
                  animate={{
                    y: [0, -20, 0],
                  }}
                  transition={{
                    repeat: Infinity,
                    duration: 4,
                    ease: "easeInOut"
                  }}
                  style={{ minHeight: '350px' }}
                />
              </motion.div>
            </div>
          </motion.div>

          <motion.div
            className="lg:flex-[0.8]"
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="flex flex-wrap items-center mb-6 gap-3">
              <h1 className="text-2xl md:text-3xl font-bold text-yellow-600">{product.model}</h1>
              <div className="bg-yellow-100 text-yellow-700 px-4 py-1 rounded-full text-sm font-medium">
                {product.channels}
              </div>
            </div>
            
            <h2 className="text-xl md:text-2xl font-semibold text-gray-800 mb-4">{product.title}</h2>

            <p className="text-gray-800 mb-8">
              {product.description}
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-x-8 md:gap-y-4 mb-8">
              <div className="bg-white p-4 md:p-5 rounded-xl shadow-md">
                <h3 className="text-lg md:text-xl font-semibold text-yellow-700 mb-4 border-b-2 border-yellow-300 pb-1 inline-block">
                  Hardware
                </h3>
                <div className="space-y-3">
                  {product.hardwareSpecs.map((spec, idx) => (
                    <SpecItem
                      key={idx}
                      icon={spec.icon}
                      text={spec.text}
                    />
                  ))}
                </div>
              </div>

              <div className="bg-white p-4 md:p-5 rounded-xl shadow-md">
                <h3 className="text-lg md:text-xl font-semibold text-yellow-700 mb-4 border-b-2 border-yellow-300 pb-1 inline-block">
                  Capabilities
                </h3>
                <div className="space-y-3">
                  {product.capabilitySpecs.map((spec, idx) => (
                    <SpecItem
                      key={idx}
                      icon={spec.icon}
                      text={spec.text}
                    />
                  ))}
                </div>
              </div>
            </div>

            <div className="flex flex-wrap gap-4">
              <Button
                onClick={handleRequestDemo}
                className="bg-yellow-500 hover:bg-yellow-600 text-white rounded-xl shadow-md px-6"
              >
                Request Demo
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
              <Button
                onClick={handleViewBrochure}
                className="bg-white border border-yellow-200 text-yellow-600 hover:bg-yellow-50 rounded-xl shadow-sm px-6"
              >
                View Brochure
                <FileText className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </motion.div>
        </div>
      </div>
      
      {/* Tabs for additional information */}
      <div className="mb-8">
        <div className="flex flex-wrap gap-2 border-b border-gray-200">
          <button
            onClick={() => setActiveTab('overview')}
            className={`px-4 py-2 font-medium rounded-t-lg ${
              activeTab === 'overview'
                ? 'bg-yellow-500 text-white'
                : 'text-gray-600 hover:text-yellow-600'
            }`}
          >
            Overview
          </button>
          <button
            onClick={() => setActiveTab('specifications')}
            className={`px-4 py-2 font-medium rounded-t-lg ${
              activeTab === 'specifications'
                ? 'bg-yellow-500 text-white'
                : 'text-gray-600 hover:text-yellow-600'
            }`}
          >
            Specifications
          </button>
          <button
            onClick={() => setActiveTab('applications')}
            className={`px-4 py-2 font-medium rounded-t-lg ${
              activeTab === 'applications'
                ? 'bg-yellow-500 text-white'
                : 'text-gray-600 hover:text-yellow-600'
            }`}
          >
            Applications
          </button>
        </div>
      </div>
      
      {/* Tab content */}
      <div className="mb-12">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <p className="text-gray-700">
              The {product.model} is a powerful tool for analyzing power quality issues in {product.channels.toLowerCase()} systems. 
              It provides comprehensive measurement capabilities and advanced analysis features to help identify and resolve power quality problems.
            </p>
            <p className="text-gray-700">
              With its intuitive interface and robust design, the {product.model} is suitable for a wide range of applications, 
              from troubleshooting electrical installations to conducting detailed power quality surveys.
            </p>
          </div>
        )}
        
        {activeTab === 'specifications' && (
          <div className="space-y-8">
            {product.fullSpecs.map((category, idx) => (
              <div key={idx} className="bg-white p-6 rounded-xl shadow-md">
                <h3 className="text-xl font-semibold text-yellow-700 mb-4">{category.category} Specifications</h3>
                <table className="w-full">
                  <tbody>
                    {category.specs.map((spec, i) => (
                      <tr key={i} className={i % 2 === 0 ? 'bg-gray-50' : ''}>
                        <td className="py-3 px-4 font-medium text-gray-700 w-1/3">{spec.name}</td>
                        <td className="py-3 px-4 text-gray-800">{spec.value}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ))}
          </div>
        )}
        
        {activeTab === 'applications' && (
          <div className="space-y-6">
            <p className="text-gray-700">
              The {product.model} is ideal for the following applications:
            </p>
            <ul className="space-y-2">
              <li className="flex items-start">
                <Check className="h-5 w-5 text-yellow-500 mt-1 mr-3 flex-shrink-0" />
                <span className="text-gray-800">Power quality audits and surveys</span>
              </li>
              <li className="flex items-start">
                <Check className="h-5 w-5 text-yellow-500 mt-1 mr-3 flex-shrink-0" />
                <span className="text-gray-800">Troubleshooting electrical installations</span>
              </li>
              <li className="flex items-start">
                <Check className="h-5 w-5 text-yellow-500 mt-1 mr-3 flex-shrink-0" />
                <span className="text-gray-800">Energy efficiency studies</span>
              </li>
              <li className="flex items-start">
                <Check className="h-5 w-5 text-yellow-500 mt-1 mr-3 flex-shrink-0" />
                <span className="text-gray-800">Preventive maintenance</span>
              </li>
              <li className="flex items-start">
                <Check className="h-5 w-5 text-yellow-500 mt-1 mr-3 flex-shrink-0" />
                <span className="text-gray-800">Compliance verification with power quality standards</span>
              </li>
            </ul>
          </div>
        )}
      </div>
    </PageLayout>
  );
};

export default PowerQualityAnalyzerDetail;
