import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  ArrowRight,
  Check,
  ChevronRight,
  FileText,
  Zap,
  BarChart,
  Gauge,
  Shield,
  ExternalLink
} from "lucide-react";
import PageLayout from "@/components/layout/PageLayout";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

// Modern Background Component
const ModernBackground = () => {
  return (
    <div className="fixed inset-0 pointer-events-none z-[-1] overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Abstract shapes */}
      <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-yellow-100 rounded-bl-full opacity-30"></div>
      <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-yellow-200 rounded-tr-full opacity-20"></div>

      {/* Subtle grid pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
    </div>
  );
};

// PDF URL for brochure
const PDF_URL = "/T&M April 2025.pdf";

// Enhanced Hero Section Component with updated buttons
const HeroSection = ({ onRequestDemo, onViewBrochure }) => {
  return (
    <div className="relative py-8 md:py-12 overflow-hidden font-['Open_Sans']">
      {/* Hero Background Elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 right-0 w-3/4 h-full bg-yellow-50 rounded-bl-[100px] transform -skew-x-12"></div>
        <div className="absolute bottom-20 left-0 w-64 h-64 bg-yellow-400 rounded-full opacity-10"></div>
      </div>

      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6 items-center">
          {/* Text Content */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-3 md:space-y-4 order-2 lg:order-1"
          >
            <div className="inline-block bg-yellow-400 py-1 px-3 rounded-full mb-2">
              <span className="text-sm font-semibold text-black font-['Open_Sans']">KRYKARD Precision Instruments</span>
            </div>
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold text-black leading-tight text-center lg:text-left font-['Open_Sans']">
              DIGITAL <span className="text-yellow-500">MULTIMETERS</span>
            </h1>

            <p className="text-base md:text-lg text-black leading-relaxed font-semibold text-center lg:text-justify font-['Open_Sans']">
              Advanced metering solutions for monitoring and analyzing electrical parameters with precision.
            </p>

            <div className="pt-3 flex flex-wrap gap-2 md:gap-3 justify-center lg:justify-start">
              <Button
                className="px-4 md:px-6 py-2 md:py-3 bg-yellow-400 hover:bg-yellow-500 text-black font-semibold rounded-lg shadow-md transition duration-300 flex items-center space-x-2 text-sm md:text-base font-['Open_Sans']"
                onClick={onRequestDemo}
              >
                <span>Request Demo</span>
                <ArrowRight className="ml-1 md:ml-2 h-4 w-4 md:h-5 md:w-5" />
              </Button>
              <Button
                className="px-4 md:px-6 py-2 md:py-3 bg-white border-2 border-yellow-400 text-black font-semibold rounded-lg shadow-sm transition duration-300 hover:bg-gray-50 flex items-center space-x-2 text-sm md:text-base font-['Open_Sans']"
                onClick={onViewBrochure}
              >
                <span>View Brochure</span>
                <FileText className="ml-1 md:ml-2 h-4 w-4 md:h-5 md:w-5" />
              </Button>
            </div>
          </motion.div>

          {/* Product Image */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative order-1 lg:order-2 mb-4 lg:mb-0"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-200 to-yellow-50 rounded-full opacity-30 blur-2xl transform scale-110"></div>
            <motion.div
              animate={{
                y: [0, -15, 0],
              }}
              transition={{
                repeat: Infinity,
                duration: 3,
                ease: "easeInOut"
              }}
              className="relative z-10 flex justify-center"
            >
              <img
                src="/multimeter\Multimeter_hero_section-removebg-preview.png"
                alt="Krykard Digital Multimeter"
                className="max-h-[200px] md:max-h-[250px] lg:max-h-[300px] w-auto object-contain drop-shadow-2xl"
              />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

// Animated Product Card with improved styling
const ProductCard = ({
  title,
  modelNumber,
  image,
  displayInfo,
  features,
  measurements,
  colors = {
    primary: 'yellow-400',
    secondary: 'yellow-50'
  },
  onViewDetailsClick
}: {
  title: string;
  modelNumber: string;
  image: string;
  displayInfo: string;
  features: string[];
  measurements: { label: string; value: string }[];
  colors?: {
    primary: string;
    secondary: string;
  };
  onViewDetailsClick: () => void;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="group h-full font-['Open_Sans']"
    >
      <div className={`h-full rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 bg-white border border-gray-100`}>
        {/* Product Image with Colored Background */}
        <div className={`relative p-3 md:p-4 flex justify-center items-center bg-${colors.secondary} h-40 md:h-48 overflow-hidden group-hover:bg-opacity-80 transition-all duration-700`}>
          <motion.img
            src={image}
            alt={title}
            className="h-32 md:h-40 object-contain z-10 drop-shadow-xl"
            whileHover={{ rotate: [-1, 1, -1], transition: { repeat: Infinity, duration: 2 } }}
          />
          <div className={`absolute top-2 left-2 bg-${colors.primary} text-white text-xs font-bold py-1 px-2 rounded-full font-['Open_Sans']`}>
            {modelNumber}
          </div>
        </div>

        {/* Product Content */}
        <div className="p-3 md:p-4 space-y-2 md:space-y-3">
          <h3 className="text-base md:text-lg font-bold text-black group-hover:text-yellow-600 transition-colors duration-300 text-center font-['Open_Sans']">
            {title}
          </h3>

          {/* Key Features */}
          <div className="space-y-1">
            {features.slice(0, 3).map((feature, idx) => (
              <div key={idx} className="flex items-start">
                <Check className="h-3 w-3 md:h-4 md:w-4 text-yellow-400 mt-1 mr-2 flex-shrink-0" />
                <span className="text-black text-xs md:text-sm font-semibold font-['Open_Sans']">{feature}</span>
              </div>
            ))}
          </div>

          {/* Specs Badge */}
          <div className="flex flex-wrap gap-1 md:gap-2 pt-1">
            <span className="inline-block bg-gray-100 rounded-full px-2 py-0.5 text-xs font-semibold text-gray-700 font-['Open_Sans']">
              {displayInfo}
            </span>
            <span className="inline-block bg-gray-100 rounded-full px-2 py-0.5 text-xs font-semibold text-gray-700 break-words font-['Open_Sans']">
              {measurements[0].label}: {measurements[0].value.split('\n')[0]}
            </span>
          </div>

          {/* View Details Button */}
          <Button
            onClick={onViewDetailsClick}
            className={`w-full mt-2 py-2 md:py-3 px-3 bg-${colors.primary} hover:bg-yellow-500 text-center font-semibold text-black rounded-lg transition-all duration-300 transform group-hover:-translate-y-1 flex items-center justify-center text-sm md:text-base font-['Open_Sans']`}
          >
            <span>View Details</span>
            <ChevronRight className="ml-1 h-3 w-3 md:h-4 md:w-4" />
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

// Feature Highlight Component with improved styling
const FeatureHighlight = ({
  icon,
  title,
  description
}: {
  icon: React.ReactNode;
  title: string;
  description: string
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
      whileHover={{ y: -8, boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)" }}
      className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 p-4 h-full border-b-4 border-yellow-400 text-center md:text-left font-['Open_Sans']"
    >
      <div className="flex flex-col h-full">
        <div className="bg-gradient-to-br from-yellow-400 to-yellow-300 w-12 h-12 md:w-16 md:h-16 rounded-lg flex items-center justify-center mb-3 shadow-md mx-auto md:mx-0">
          {icon}
        </div>
        <h3 className="text-base md:text-lg lg:text-xl font-bold text-black mb-2 font-['Open_Sans']">{title}</h3>
        <p className="text-black font-semibold text-sm md:text-base text-center md:text-justify flex-grow font-['Open_Sans']">{description}</p>
      </div>
    </motion.div>
  );
};

// SpecItem component for product details - kept for future use
// const SpecItem = ({ icon, text }: { icon: React.ReactNode; text: string }) => (
//   <div className="flex items-center space-x-3 py-3 border-b border-yellow-100">
//     <div className="bg-yellow-100 p-2 rounded-lg">
//       {icon}
//     </div>
//     <span className="text-gray-800 font-medium">{text}</span>
//   </div>
// );

// Combined Tab Component for Features and Measurements
const ProductTabContent = ({
  activeProductType,
  activeTab
}: {
  activeProductType: string;
  activeTab: string
}) => {
  // Features data based on product type
  const features = {
    basic: [
      "Display: 6000 counts monochrome digital display with blue backlighting",
      "Audible continuity & Diode test",
      "VLowZ, HOLD, NCV",
      "Min, Max values",
      "IP 54"
    ],
    standard: [
      "Display: 6,000 counts backlit with bargraph",
      "Measurement Type: Average (DMM 210 & DMM 220), TRMS (DMM 230)",
      "Calibre Selection: Autorange/Manual",
      "Audible continuity & Diode test",
      "Automatic shutdown (deactivatable)",
      "Relative mode – MIN, MAX",
      "IP 67"
    ],
    advanced: [
      "Display: 40,000 counts backlit with bargraph",
      "Measurement Type: TRMS",
      "Calibre Selection: Autorange/Manual",
      "Audible continuity & Diode test",
      "Automatic shutdown (deactivatable)",
      "Relative mode – MIN, MAX, PEAK (1 ms)",
      "IP 67"
    ],
    ca_advanced: [
      "Display: 2 x 6,000 counts with backlighting & bargraph",
      "Autorange/Deactivatable",
      "Automatic AC/DC detection (CA 5273)",
      "Audible continuity & Diode test",
      "Differential (ΔX)/RELative (ΔX/X%) measurement (CA 5277)",
      "1 ms Peak+/Peak values (CA 5277)",
      "Hold",
      "IP 54"
    ],
    mtx_high: [
      "Display: 60,000 counts backlight digital monochrome",
      "TRMS value",
      "Audible continuity detection (600 Ω SIGNAL 30 Ω ≤ 5 V)",
      "Diode test",
      "HOLD/Auto-HOLD",
      "Communication: USB",
      "PC interface",
      "IP 67"
    ],
    professional: [
      "Display: 4 x 100,000 count colour graphical with backlighting",
      "TRMS value",
      "Audible continuity detection (1000 Ω/SIGNAL < 20 Ω < 3.5 V) & Diode test",
      "Display of trends, multiple parameters & 600 Hz waveform",
      "HOLD/Auto-HOLD",
      "Memory CA 5292: 10,000 measurements",
      "CA 5293: 30,000 measurements",
      "Communication: USB, Bluetooth (optional)",
      "PC interface",
      "IP 67"
    ],
    leakage: [
      "Clamping diameter: 28 mm",
      "Display: 10,000 counts backlit LCD",
      "Hold & Auto power off",
      "Audible Continuity (Buzzer 35 Ω)",
      "Complied IEC 61557-13 standard"
    ]
  };

  // Measurements data based on product type
  const measurements = {
    basic: [
      { label: "Voltage", value: "V (AC): 0.4 V to 600 V & 10 mV to 1000 V\nV (AC/DC/AC+DC): 10 mV to 1000 V" },
      { label: "Current", value: "A (AC): 2 mA to 10 A & 10 μA to 10 A" },
      { label: "Resistance", value: "Range: Upto 40 MΩ" },
      { label: "Capacitance", value: "Range: Upto 100 mF" },
      { label: "Temperature", value: "Range: Upto 1,200°C" }
    ],
    standard: [
      { label: "Voltage", value: "V(AC): 6 V to 1000 V\nV(DC): 600 mV to 1000 V" },
      { label: "AC bandwidth", value: "Upto 1 kHz" },
      { label: "Current", value: "A (AC/DC): 600 μA to 10 A" },
      { label: "Resistance", value: "600 Ω to 60 MΩ" },
      { label: "Frequency", value: "Upto 10 MHz" },
      { label: "Capacitance", value: "Upto 1000 μF (DMM 220 & DMM 230)" },
      { label: "Temperature", value: "Upto 750°C (DMM 220 & DMM 230)" }
    ],
    advanced: [
      { label: "Voltage", value: "V(AC/DC): 400 mV to 1000 V" },
      { label: "AC bandwidth", value: "Upto 1 kHz" },
      { label: "Current", value: "A (AC/DC): 400 μA to 10 A" },
      { label: "Resistance", value: "400 Ω to 40 MΩ" },
      { label: "Frequency", value: "Upto 100 MHz" },
      { label: "Capacitance", value: "Upto 40 mF" },
      { label: "Temperature", value: "Upto 1000°C" }
    ],
    ca_advanced: [
      { label: "Voltage", value: "V (AC/DC): 600 mV to 1000 V (CA 5273)\nV (AC/DC/AC+DC): 60 mV to 1000 V (CA 5275 & CA 5277)" },
      { label: "AC bandwidth", value: "Upto 3 kHz (CA 5273) & 10 kHz (CA 5275 & CA5277)" },
      { label: "Current", value: "A (AC/DC): 20 mA to 10 A (CA 5273)\nA (AC/DC/AC+DC): 6 mA to 10 A (CA 5275 & CA 5277)" },
      { label: "Resistance", value: "Upto 60 MΩ" },
      { label: "Frequency", value: "Upto 50 kHz" },
      { label: "Capacitance", value: "Upto 60 mF" },
      { label: "Temperature", value: "Upto 1,200°C (CA 5273 & CA 5277)" }
    ],
    mtx_high: [
      { label: "Voltage", value: "V (AC/DC/AC+DC): Upto 1000 V" },
      { label: "AC & AC+DC bandwidth", value: "100 kHz" },
      { label: "Current", value: "A (AC/DC/AC+DC): Upto 20 A" },
      { label: "Frequency", value: "Upto 600 kHz" },
      { label: "Resistance", value: "Upto 60 MΩ" },
      { label: "Capacitance", value: "Upto 60 mF" },
      { label: "Temperature", value: "Upto 800°C" }
    ],
    professional: [
      { label: "Voltage", value: "V (AC/DC/AC+DC): Upto 1000 V Accuracy\nV DC: 0.03% (CA 5292) & 0.02% (CA 5293)\nV AC: 0.3%\nAC & AC+DC bandwidth: Upto 100 kHz (CA 5292) & 200 kHz (CA 5293)" },
      { label: "Current", value: "A (AC/DC/AC+DC): Upto 20 A\nAccuracy: 0.08% (DC) & 0.3% (AC/AC+DC)\nAC & AC+DC bandwidth: 50 kHz" },
      { label: "Frequency", value: "Upto 5 MHz" },
      { label: "Resistance", value: "Upto 100 MΩ" },
      { label: "Capacitance", value: "Upto 10 mF" },
      { label: "Temperature", value: "Upto 1200 °C (with K thermocouple)" }
    ],
    leakage: [
      { label: "AC current", value: "Range: 60 mA to 100 A\nAccuracy: 1.2 % ± 5 cts" },
      { label: "Voltage (AC/DC)", value: "600 V" },
      { label: "Resistance & Continuity", value: "at 1 kΩ" },
      { label: "Frequency", value: "5 Hz to 1 kHz" }
    ]
  };

  // Define types for feature and measurement
  type Measurement = {
    label: string;
    value: string;
  };

  if (activeTab === "features") {
    return (
      <div className="bg-white rounded-2xl shadow-lg overflow-hidden font-['Open_Sans']">
        <div className="bg-gradient-to-r from-yellow-400 to-yellow-400 p-3">
          <h3 className="text-xl md:text-2xl font-bold text-center text-white">Salient Features</h3>
        </div>
        <div className="p-2 sm:p-4">
          <div className="space-y-1">
            {features[activeProductType].map((feature: string, idx: number) => (
              <motion.div
                key={idx}
                initial={{ opacity: 0, x: -10 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: idx * 0.05 }}
                viewport={{ once: true }}
                className="flex items-start p-2 hover:bg-yellow-50 rounded-lg transition-colors duration-300"
              >
                <div className="flex-shrink-0 w-5 h-5 rounded-full bg-yellow-100 text-yellow-600 flex items-center justify-center mr-2 mt-0.5">
                  <Check className="h-3 w-3 md:h-4 md:w-4" />
                </div>
                <span className="text-gray-900 text-sm md:text-base font-bold font-['Open_Sans']">{feature}</span>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    );
  } else {
    return (
      <div className="bg-white rounded-2xl shadow-lg overflow-hidden font-['Open_Sans']">
        <div className="bg-gradient-to-r from-yellow-400 to-yellow-400 p-3">
          <h3 className="text-xl md:text-2xl font-bold text-center text-white">Measurements</h3>
        </div>
        <div className="p-2 sm:p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4">
            {measurements[activeProductType].map((item: Measurement, idx: number) => (
              <motion.div
                key={idx}
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: idx * 0.05 }}
                viewport={{ once: true }}
                className="bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow duration-300 border border-yellow-100"
              >
                <h4 className="font-bold text-gray-900 mb-2 border-b border-yellow-200 pb-2 text-sm md:text-base font-['Open_Sans']">{item.label}</h4>
                <div className="text-gray-900 text-xs md:text-sm font-bold font-['Open_Sans']">
                  {item.value.split('\n').map((line: string, i: number) => (
                    <div key={i} className="mb-1 break-words">{line}</div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    );
  }
};

// Contact Section Component with improved styling
const ContactSection = ({ onContactClick }: { onContactClick: () => void }) => {
  return (
    <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 py-6 px-4 rounded-2xl shadow-lg font-['Open_Sans']">
      <div className="max-w-full mx-auto text-center">
        <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-black mb-3 font-['Open_Sans']">Need More Information?</h2>
        <p className="text-black mb-6 max-w-4xl mx-auto font-semibold text-sm md:text-base lg:text-lg text-center font-['Open_Sans']">
          Our team of experts is ready to help you with product specifications, custom solutions,
          pricing, and any other details you need about the KRYKARD Digital Multimeters.
        </p>
        <Button
          className="inline-flex items-center px-6 md:px-8 py-3 md:py-4 bg-yellow-400 hover:bg-yellow-500 text-black font-semibold rounded-lg shadow-md transition duration-300 transform hover:-translate-y-1 text-sm md:text-base"
          onClick={onContactClick}
        >
          Contact Our Experts
          <ArrowRight className="ml-2 h-4 md:h-5 w-4 md:w-5" />
        </Button>
      </div>
    </div>
  );
};

// Comparison Table Component with improved styling
const ComparisonTable = ({ productType }) => {
  const models = {
    basic: ['MTX 203'],
    standard: ['DMM 210', 'DMM 220', 'DMM 230'],
    advanced: ['DMM 240'],
    ca_advanced: ['CA 5273', 'CA 5275', 'CA 5277'],
    mtx_high: ['MTX 3291'],
    professional: ['CA 5292', 'CA 5293'],
    leakage: ['F65']
  };

  const features = {
    basic: [
      { name: 'Display Resolution', values: ['6000 counts'] },
      { name: 'Display Type', values: ['Monochrome with blue backlighting'] },
      { name: 'Max AC Voltage', values: ['1000 V'] },
      { name: 'Max AC Current', values: ['10 A'] },
      { name: 'Resistance', values: ['Up to 40 MΩ'] },
      { name: 'Capacitance', values: ['Up to 100 mF'] },
      { name: 'IP Rating', values: ['IP 54'] }
    ],
    standard: [
      { name: 'Display Resolution', values: ['6,000 counts', '6,000 counts', '6,000 counts'] },
      { name: 'Display Type', values: ['Backlit with bargraph', 'Backlit with bargraph', 'Backlit with bargraph'] },
      { name: 'Measurement Type', values: ['Average', 'Average', 'TRMS'] },
      { name: 'Max AC Voltage', values: ['1000 V', '1000 V', '1000 V'] },
      { name: 'Capacitance', values: ['-', 'Up to 1000 μF', 'Up to 1000 μF'] },
      { name: 'Temperature', values: ['-', 'Up to 750°C', 'Up to 750°C'] },
      { name: 'IP Rating', values: ['IP 67', 'IP 67', 'IP 67'] }
    ],
    advanced: [
      { name: 'Display Resolution', values: ['40,000 counts'] },
      { name: 'Display Type', values: ['Backlit with bargraph'] },
      { name: 'Measurement Type', values: ['TRMS'] },
      { name: 'AC Bandwidth', values: ['Up to 1 kHz'] },
      { name: 'Max Frequency', values: ['Up to 100 MHz'] },
      { name: 'Capacitance', values: ['Up to 40 mF'] },
      { name: 'Temperature', values: ['Up to 1000°C'] },
      { name: 'Peak Measurement', values: ['1 ms'] },
      { name: 'IP Rating', values: ['IP 67'] }
    ],
    ca_advanced: [
      { name: 'Display Resolution', values: ['6000 counts', '6000 counts', '6000 counts'] },
      { name: 'AC/DC Detection', values: ['Automatic', '-', '-'] },
      { name: 'AC Bandwidth', values: ['Up to 3 kHz', 'Up to 10 kHz', 'Up to 10 kHz'] },
      { name: 'Max AC Current', values: ['10 A', '10 A', '10 A'] },
      { name: 'Differential Measurement', values: ['-', '-', 'Yes (ΔX/X%)'] },
      { name: 'Peak Values', values: ['-', '-', '1 ms Peak+/Peak-'] },
      { name: 'Temperature', values: ['Up to 1,200°C', '-', 'Up to 1,200°C'] }
    ],
    mtx_high: [
      { name: 'Display Resolution', values: ['60,000 counts'] },
      { name: 'Display Type', values: ['Backlit digital monochrome'] },
      { name: 'AC & AC+DC Bandwidth', values: ['100 kHz'] },
      { name: 'Max Current', values: ['20 A'] },
      { name: 'Max Frequency', values: ['600 kHz'] },
      { name: 'Communications', values: ['USB'] },
      { name: 'IP Rating', values: ['IP 67'] }
    ],
    professional: [
      { name: 'Display Resolution', values: ['100,000 counts', '100,000 counts'] },
      { name: 'Display Type', values: ['Color graphical', 'Color graphical'] },
      { name: 'VDC Accuracy', values: ['0.03%', '0.02%'] },
      { name: 'AC Bandwidth', values: ['Up to 100 kHz', 'Up to 200 kHz'] },
      { name: 'Memory', values: ['10,000 measurements', '30,000 measurements'] },
      { name: 'Communications', values: ['USB, Optional Bluetooth', 'USB, Optional Bluetooth'] },
      { name: 'IP Rating', values: ['IP 67', 'IP 67'] }
    ],
    leakage: [
      { name: 'Clamping Diameter', values: ['28 mm'] },
      { name: 'Display Resolution', values: ['10,000 counts'] },
      { name: 'Max AC Current', values: ['100 A'] },
      { name: 'Accuracy', values: ['1.2% ± 5 cts'] },
      { name: 'Voltage', values: ['600 V'] },
      { name: 'Frequency Range', values: ['5 Hz to 1 kHz'] },
      { name: 'IEC Compliance', values: ['IEC 61557-13'] }
    ]
  };

  // Define types for feature and model
  type Feature = {
    name: string;
    values: string[];
  };

  // For mobile view, create a more responsive layout
  const renderMobileView = () => {
    return (
      <div className="block md:hidden">
        {features[productType].map((feature: Feature, idx: number) => (
          <motion.div
            key={idx}
            initial={{ opacity: 0, y: 5 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: idx * 0.05 }}
            viewport={{ once: true }}
            className={`mb-4 p-3 rounded-lg ${idx % 2 === 0 ? 'bg-white' : 'bg-gray-50'} border border-gray-100`}
          >
            <div className="font-bold text-gray-900 mb-2 pb-1 border-b border-yellow-100 font-['Open_Sans']">{feature.name}</div>
            <div className="grid grid-cols-2 gap-2">
              {models[productType].map((model: string, modelIdx: number) => (
                <div key={modelIdx} className="flex flex-col">
                  <span className="text-xs font-bold text-yellow-600 mb-1 font-['Open_Sans']">{model}</span>
                  <span className="text-sm text-gray-900 font-bold font-['Open_Sans']">{feature.values[modelIdx] || '-'}</span>
                </div>
              ))}
            </div>
          </motion.div>
        ))}
      </div>
    );
  };

  // For desktop view, use the table layout
  const renderDesktopView = () => {
    return (
      <div className="hidden md:block overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead>
            <tr>
              <th className="px-4 sm:px-6 py-3 bg-yellow-50 text-left text-xs font-bold text-gray-500 uppercase tracking-wider rounded-tl-lg font-['Open_Sans']">Feature</th>
              {models[productType].map((model: string, idx: number) => (
                <th key={idx} className={`px-4 sm:px-6 py-3 bg-yellow-50 text-center text-xs font-bold text-gray-500 uppercase tracking-wider font-['Open_Sans'] ${idx === models[productType].length - 1 ? 'rounded-tr-lg' : ''}`}>
                  {model}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {features[productType].map((feature: Feature, idx: number) => (
              <motion.tr
                key={idx}
                initial={{ opacity: 0, y: 5 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: idx * 0.05 }}
                viewport={{ once: true }}
                className={idx % 2 === 0 ? 'bg-white hover:bg-yellow-50 transition-colors duration-200' : 'bg-gray-50 hover:bg-yellow-50 transition-colors duration-200'}
              >
                <td className="px-4 sm:px-6 py-3 sm:py-4 text-sm font-bold text-gray-900 font-['Open_Sans']">{feature.name}</td>
                {feature.values.map((value: string, i: number) => (
                  <td key={i} className="px-4 sm:px-6 py-3 sm:py-4 text-sm text-gray-900 text-center font-bold font-['Open_Sans']">
                    {value}
                  </td>
                ))}
              </motion.tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <div className="bg-white rounded-2xl shadow-lg overflow-hidden mt-6 font-['Open_Sans']">
      <div className="bg-gradient-to-r from-yellow-400 to-yellow-400 p-3">
        <h3 className="text-xl md:text-2xl font-bold text-center text-white">Model Comparison</h3>
      </div>
      <div className="p-2 sm:p-4">
        {renderMobileView()}
        {renderDesktopView()}
      </div>
    </div>
  );
};

// Applications Section Component with improved styling
const ApplicationsSection = () => {
  const applications = [
    {
      title: "Electrical Installation",
      icon: <Zap className="h-6 w-6 sm:h-8 sm:w-8 text-yellow-600" />,
      description: "Used for testing electrical installations, measuring voltage, current, and resistance in circuits and components."
    },
    {
      title: "HVAC Systems",
      icon: <Gauge className="h-6 w-6 sm:h-8 sm:w-8 text-yellow-600" />,
      description: "Perfect for HVAC technicians to diagnose system problems, check temperature sensors, and verify proper operation."
    },
    {
      title: "Electronics Repair",
      icon: <Shield className="h-6 w-6 sm:h-8 sm:w-8 text-yellow-600" />,
      description: "Ideal for troubleshooting and repairing electronic equipment, testing components, and verifying circuit functionality."
    },
    {
      title: "Industrial Maintenance",
      icon: <BarChart className="h-6 w-6 sm:h-8 sm:w-8 text-yellow-600" />,
      description: "Regular maintenance of industrial equipment, identifying potential failures before they occur."
    }
  ];

  return (
    <div className="py-6 md:py-8 bg-gradient-to-br from-yellow-50 to-white rounded-2xl my-6 shadow-lg font-['Open_Sans']">
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-6 md:mb-8">
          <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-gray-900 mb-3 font-['Open_Sans']">Application Areas</h2>
          <p className="text-sm md:text-base lg:text-lg text-gray-800 max-w-4xl mx-auto font-medium text-center font-['Open_Sans']">
            Our digital multimeters are designed for a wide range of electrical measurement applications
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {applications.map((app, idx) => (
            <motion.div
              key={idx}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: idx * 0.1 }}
              viewport={{ once: true }}
              className="bg-white p-4 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-2 border-b-4 border-yellow-400 text-center md:text-left"
            >
              <div className="bg-yellow-100 w-12 md:w-16 h-12 md:h-16 rounded-lg flex items-center justify-center mb-3 text-yellow-600 mx-auto md:mx-0">
                {app.icon}
              </div>
              <h3 className="text-base md:text-lg lg:text-xl font-semibold mb-2 text-gray-900 font-['Open_Sans']">{app.title}</h3>
              <p className="text-gray-900 font-semibold text-sm md:text-base text-center md:text-justify font-['Open_Sans']">{app.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Main Digital Multimeters Component
const DigitalMultimeters = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const [activeProductType, setActiveProductType] = useState("basic");
  const [activeDetailTab, setActiveDetailTab] = useState("features");
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // Effect to check URL parameters for initial tab and product type
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const tab = params.get('tab');
    const product = params.get('product');
    const detailTab = params.get('detailTab');

    if (tab) setActiveTab(tab);
    if (product) setActiveProductType(product);
    if (detailTab) setActiveDetailTab(detailTab);
  }, [location]);

  // Handler for Request Demo button
  const handleRequestDemo = () => {
    navigate("/contact/sales");
  };

  // Handler for View Brochure button
  const handleViewBrochure = () => {
    window.open(PDF_URL, '_blank');
  };

  // Handler for View Details button
  const handleViewDetails = (productType: string) => {
    // First set the states and update URL
    setActiveProductType(productType);
    setActiveTab('details');
    setActiveDetailTab('features');
    navigate(`?tab=details&product=${productType}&detailTab=features`, { replace: true });

    // Wait for the UI to update before scrolling to the specific product section
    setTimeout(() => {
      // Find the product details section
      const productDetailsSection = document.querySelector('.product-details-section');
      if (productDetailsSection) {
        productDetailsSection.scrollIntoView({ behavior: 'auto', block: 'start' });
      }
    }, 100);
  };

  // Basic Multimeter data (MTX 203)
  const basicMultimeterData = {
    title: "Basic Digital Multimeter",
    modelNumber: "MTX 203",
    image: "/multimeter/MTX-203.png",
    displayInfo: "6000 counts LCD",
    features: [
      "Display with blue backlighting",
      "Audible continuity & Diode test",
      "VLowZ, HOLD, NCV",
      "Min, Max values",
      "IP 54"
    ],
    measurements: [
      { label: "Voltage", value: "Up to 1000 V AC/DC" },
      { label: "Current", value: "Up to 10 A" },
      { label: "Resistance", value: "Up to 40 MΩ" }
    ],
    colors: {
      primary: 'yellow-400',
      secondary: 'yellow-100'
    }
  };

  // Standard Multimeter data (DMM 210/220/230)
  const standardMultimeterData = {
    title: "Standard Digital Multimeter",
    modelNumber: "DMM 210/220/230",
    image: "/multimeter/dmm_210-removebg-preview.png",
    displayInfo: "6,000 counts LCD",
    features: [
      "Display with bargraph",
      "Average/TRMS measurement",
      "Autorange/Manual",
      "Audible continuity & Diode test",
      "IP 67"
    ],
    measurements: [
      { label: "Voltage", value: "Up to 1000 V AC/DC" },
      { label: "Current", value: "Up to 10 A" },
      { label: "Frequency", value: "Up to 10 MHz" }
    ],
    colors: {
      primary: 'yellow-400',
      secondary: 'yellow-100'
    }
  };

  // Advanced Multimeter data (DMM 240)
  const advancedMultimeterData = {
    title: "Advanced Digital Multimeter",
    modelNumber: "DMM 240",
    image: "/multimeter/Dmm_240-removebg-preview.png",
    displayInfo: "40,000 counts LCD",
    features: [
      "Display with bargraph",
      "TRMS measurement",
      "Autorange/Manual",
      "Relative mode – MIN, MAX, PEAK",
      "IP 67"
    ],
    measurements: [
      { label: "Voltage", value: "Up to 1000 V AC/DC" },
      { label: "Current", value: "Up to 10 A" },
      { label: "Frequency", value: "Up to 100 MHz" }
    ],
    colors: {
      primary: 'yellow-400',
      secondary: 'yellow-100'
    }
  };

  // CA Advanced Series data (CA 5273/5275/5277)
  const caAdvancedData = {
    title: "CA Advanced Series",
    modelNumber: "CA 5273/5275/5277",
    image: "/multimeter/C.A-5273.png",
    displayInfo: "2 x 6,000 counts LCD",
    features: [
      "Dual display with backlighting",
      "Automatic AC/DC detection (CA 5273)",
      "Differential measurement (CA 5277)",
      "1 ms Peak values (CA 5277)",
      "IP 54"
    ],
    measurements: [
      { label: "Voltage", value: "Up to 1000 V AC/DC" },
      { label: "Current", value: "Up to 10 A" },
      { label: "AC bandwidth", value: "Up to 10 kHz" }
    ],
    colors: {
      primary: 'yellow-400',
      secondary: 'yellow-100'
    }
  };

  // MTX High Resolution data (MTX 3291)
  const mtxHighData = {
    title: "High Resolution Multimeter",
    modelNumber: "MTX 3291",
    image: "/multimeter/Mtx_3291-removebg-preview.png",
    displayInfo: "60,000 counts LCD",
    features: [
      "TRMS measurement",
      "Audible continuity detection",
      "HOLD/Auto-HOLD",
      "USB communication",
      "IP 67"
    ],
    measurements: [
      { label: "Voltage", value: "Up to 1000 V" },
      { label: "Current", value: "Up to 20 A" },
      { label: "Bandwidth", value: "100 kHz" }
    ],
    colors: {
      primary: 'yellow-400',
      secondary: 'yellow-100'
    }
  };

  // Professional Multimeter data (CA 5292/5293)
  const professionalMultimeterData = {
    title: "Professional Digital Multimeter",
    modelNumber: "CA 5292/CA 5293",
    image: "/multimeter/C.A-5292.png",
    displayInfo: "100,000 count color display",
    features: [
      "4 x 100,000 count colour graphical display",
      "Display of trends & waveforms",
      "Memory for up to 30,000 measurements",
      "USB & Bluetooth connectivity",
      "IP 67"
    ],
    measurements: [
      { label: "Voltage", value: "Up to 1000 V with 0.02% accuracy" },
      { label: "Current", value: "Up to 20 A" },
      { label: "Frequency", value: "Up to 5 MHz" }
    ],
    colors: {
      primary: 'yellow-400',
      secondary: 'yellow-100'
    }
  };

  // Leakage Clamp Meter data (F65)
  const leakageClampData = {
    title: "RMS Leakage Current Clamp",
    modelNumber: "F65",
    image: "/multimeter/f65-removebg-preview.png",
    displayInfo: "10,000 counts LCD",
    features: [
      "Clamping diameter: 28 mm",
      "Hold & Auto power off",
      "Audible Continuity (Buzzer 35 Ω)",
      "Complied IEC 61557-13 standard"
    ],
    measurements: [
      { label: "AC Current", value: "60 mA to 100 A" },
      { label: "Voltage", value: "Up to 600 V" },
      { label: "Frequency", value: "5 Hz to 1 kHz" }
    ],
    colors: {
      primary: 'yellow-400',
      secondary: 'yellow-100'
    }
  };

  // Product Type Options for the selector
  const productOptions = [
    { id: "basic", label: "Basic Multimeter", models: "MTX 203", icon: <Gauge className="h-6 w-6" /> },
    { id: "standard", label: "Standard Multimeter", models: "DMM 210/220/230", icon: <Gauge className="h-6 w-6" /> },
    { id: "advanced", label: "Advanced Multimeter", models: "DMM 240", icon: <Gauge className="h-6 w-6" /> },
    { id: "ca_advanced", label: "CA Advanced Series", models: "CA 5273/5275/5277", icon: <Zap className="h-6 w-6" /> },
    { id: "mtx_high", label: "High Resolution", models: "MTX 3291", icon: <BarChart className="h-6 w-6" /> },
    { id: "professional", label: "Professional Multimeter", models: "CA 5292/5293", icon: <Shield className="h-6 w-6" /> },
    { id: "leakage", label: "Leakage Current Clamp", models: "F65", icon: <Zap className="h-6 w-6" /> }
  ];

  // Get active product data based on selected product type
  const getActiveProductData = () => {
    if (activeProductType === "basic") return basicMultimeterData;
    if (activeProductType === "standard") return standardMultimeterData;
    if (activeProductType === "advanced") return advancedMultimeterData;
    if (activeProductType === "ca_advanced") return caAdvancedData;
    if (activeProductType === "mtx_high") return mtxHighData;
    if (activeProductType === "professional") return professionalMultimeterData;
    return leakageClampData;
  };

  // Navigation tabs data
  const navTabs = [
    { id: "overview", label: "Overview", icon: <Gauge className="h-5 w-5" /> },
    { id: "details", label: "Product Details", icon: <Zap className="h-5 w-5" /> },
    { id: "applications", label: "Applications", icon: <Shield className="h-5 w-5" /> }
  ];

  return (
    <PageLayout
      title="Digital Multimeters"
      subtitle="Advanced metering solutions for monitoring and analyzing electrical parameters with precision"
      category="measure"
    >
      {/* Modern Background */}
      <ModernBackground />

      {/* Premium Modern Navigation Tabs - Responsive Design */}
      <div className="sticky top-0 z-30 bg-white shadow-lg border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4">
          {/* Desktop Navigation */}
          <div className="hidden md:flex justify-center py-2">
            <div className="bg-gray-50 p-1.5 rounded-full flex shadow-sm">
              {navTabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => {
                    setActiveTab(tab.id);
                    if (tab.id === "details") {
                      navigate(`?tab=${tab.id}&product=${activeProductType}&detailTab=${activeDetailTab}`, { replace: true });
                    } else {
                      navigate(`?tab=${tab.id}`, { replace: true });
                    }
                    setIsMobileMenuOpen(false);
                  }}
                  className={cn(
                    "relative px-6 py-2.5 font-medium rounded-full transition-all duration-300 flex items-center mx-1 overflow-hidden",
                    activeTab === tab.id
                      ? "bg-gradient-to-r from-yellow-500 to-yellow-400 text-white shadow-md transform -translate-y-0.5"
                      : "text-gray-700 hover:bg-yellow-50 hover:text-yellow-500"
                  )}
                >
                  <div className="flex items-center relative z-10">
                    <span className="mr-2">{tab.icon}</span>
                    <span>{tab.label}</span>
                  </div>
                  {activeTab === tab.id && (
                    <motion.div
                      layoutId="navIndicator"
                      className="absolute inset-0 bg-gradient-to-r from-yellow-500 to-yellow-400 -z-0"
                      initial={false}
                      transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                    />
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Mobile Navigation */}
          <div className="md:hidden py-2 flex justify-between items-center">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="text-gray-700 hover:text-yellow-500 focus:outline-none"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            <span className="font-semibold text-gray-900 text-lg">
              {navTabs.find(tab => tab.id === activeTab)?.label}
            </span>

            <div className="w-6"></div> {/* Spacer for balanced layout */}
          </div>

          {/* Mobile Menu Dropdown */}
          <div className={`md:hidden overflow-hidden transition-all duration-300 ease-in-out ${isMobileMenuOpen ? 'max-h-60' : 'max-h-0'}`}>
            <div className="bg-white rounded-lg shadow-lg p-2 mb-2">
              {navTabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => {
                    setActiveTab(tab.id);
                    if (tab.id === "details") {
                      navigate(`?tab=${tab.id}&product=${activeProductType}&detailTab=${activeDetailTab}`, { replace: true });
                    } else {
                      navigate(`?tab=${tab.id}`, { replace: true });
                    }
                    setIsMobileMenuOpen(false);
                  }}
                  className={`w-full text-left px-4 py-3 rounded-lg my-1 flex items-center ${
                    activeTab === tab.id
                      ? "bg-yellow-50 text-yellow-600 font-medium"
                      : "text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  <span className="mr-3">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {activeTab === "overview" && (
        <>
          {/* Hero Section */}
          <HeroSection
            onRequestDemo={handleRequestDemo}
            onViewBrochure={handleViewBrochure}
          />

          {/* Key Features Overview */}
          <div className="py-8 md:py-12 bg-gradient-to-br from-yellow-50 to-white">
            <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-8 md:mb-12">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                  viewport={{ once: true }}
                >
                  <h2 className="text-2xl md:text-3xl font-bold text-yellow-600 mb-4 font-['Open_Sans']">Why Choose Our Digital Multimeters?</h2>
                  <p className="mt-4 text-base md:text-lg text-gray-800 max-w-4xl mx-auto font-medium text-center font-['Open_Sans']">
                    Precision-engineered instruments that combine accuracy, durability, and advanced features
                  </p>
                </motion.div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
                <FeatureHighlight
                  icon={<Gauge className="h-6 w-6 text-white" />}
                  title="Precision Accuracy"
                  description="Our digital multimeters provide highly accurate measurements with exceptional resolution, ensuring reliable readings for professional applications."
                />

                <FeatureHighlight
                  icon={<Zap className="h-6 w-6 text-white" />}
                  title="Versatile Measurement"
                  description="Measure voltage, current, resistance, capacitance, frequency and temperature with a single device, simplifying your testing and troubleshooting toolkit."
                />

                <FeatureHighlight
                  icon={<Shield className="h-6 w-6 text-white" />}
                  title="Data Analysis & Storage"
                  description="Advanced models offer data logging capabilities, memory storage, and PC interfaces to help you track, analyze, and document your measurements over time."
                />
              </div>
            </div>
          </div>

          {/* Products Section */}
          <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-8 md:mb-12"
            >
              <span className="inline-block bg-yellow-100 text-yellow-800 px-4 py-1 rounded-full text-sm font-semibold mb-4 font-['Open_Sans']">
                PROFESSIONAL SERIES
              </span>
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-6 text-gray-900 font-['Open_Sans']">
                Our Digital Multimeter Series
              </h2>
              <p className="max-w-4xl mx-auto text-gray-800 text-base md:text-lg font-medium text-center font-['Open_Sans']">
                Choose the perfect multimeter for your electrical measurement needs.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
              <ProductCard
                {...basicMultimeterData}
                onViewDetailsClick={() => handleViewDetails("basic")}
              />
              <ProductCard
                {...standardMultimeterData}
                onViewDetailsClick={() => handleViewDetails("standard")}
              />
              <ProductCard
                {...advancedMultimeterData}
                onViewDetailsClick={() => handleViewDetails("advanced")}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 mt-4 md:mt-6">
              <ProductCard
                {...caAdvancedData}
                onViewDetailsClick={() => handleViewDetails("ca_advanced")}
              />
              <ProductCard
                {...mtxHighData}
                onViewDetailsClick={() => handleViewDetails("mtx_high")}
              />
              <ProductCard
                {...professionalMultimeterData}
                onViewDetailsClick={() => handleViewDetails("professional")}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 mt-4 md:mt-6 justify-center">
              <div className="md:col-start-2">
                <ProductCard
                  {...leakageClampData}
                  onViewDetailsClick={() => handleViewDetails("leakage")}
                />
              </div>
            </div>
          </div>

          {/* Contact Information Section */}
          <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <ContactSection onContactClick={handleRequestDemo} />
          </div>
        </>
      )}

      {activeTab === "details" && (
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6 md:py-8">
          {/* Enhanced Product Type Selector - Compact Version */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-gradient-to-r from-yellow-50 to-white rounded-xl shadow-md p-4 md:p-6 mb-6 relative overflow-hidden font-['Open_Sans']"
          >
            <div className="absolute top-0 right-0 w-64 h-64 bg-yellow-200 rounded-full opacity-20 transform translate-x-1/2 -translate-y-1/2 blur-3xl"></div>

            <h2 className="text-lg md:text-xl font-bold text-black mb-4 relative z-10 text-center font-['Open_Sans']">
              Select <span className="text-yellow-500">Model Series</span>
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-7 gap-2 md:gap-3 relative z-10">
              {productOptions.map((option) => (
                <motion.button
                  key={option.id}
                  whileHover={{ y: -3, boxShadow: "0 8px 20px -5px rgba(0, 0, 0, 0.1)" }}
                  whileTap={{ y: 0 }}
                  onClick={() => {
                    setActiveProductType(option.id);
                    navigate(`?tab=details&product=${option.id}&detailTab=${activeDetailTab}`, { replace: true });
                  }}
                  className={`relative rounded-lg transition-all duration-300 overflow-hidden ${
                    activeProductType === option.id
                      ? "ring-1 ring-yellow-400"
                      : "hover:ring-1 hover:ring-yellow-300"
                  }`}
                >
                  <div className={`h-full py-3 md:py-4 px-2 md:px-3 flex flex-col items-center text-center ${
                    activeProductType === option.id
                      ? "bg-yellow-400 text-white"
                      : "bg-white hover:bg-yellow-50"
                  }`}>
                    <div className="mb-2">
                      {option.icon && (
                        <div className={`${activeProductType === option.id ? "text-white" : "text-yellow-400"}`}>
                          {option.icon}
                        </div>
                      )}
                    </div>

                    <h3 className={`text-xs md:text-sm font-bold mb-1 font-['Open_Sans'] ${activeProductType === option.id ? "text-white" : "text-black"}`}>
                      {option.label}
                    </h3>

                    <div className={`text-xs font-['Open_Sans'] ${activeProductType === option.id ? "text-white opacity-80" : "text-gray-500"}`}>
                      {option.models}
                    </div>

                    {activeProductType === option.id && (
                      <div className="mt-2 bg-white bg-opacity-20 rounded-full px-2 md:px-3 py-0.5 text-xs font-semibold font-['Open_Sans']">
                        Selected
                      </div>
                    )}
                  </div>
                </motion.button>
              ))}
            </div>
          </motion.div>

          {/* Enhanced Product Detail Section */}
          <div className="grid grid-cols-1 md:grid-cols-12 gap-4 md:gap-6 product-details-section">
            <div className="md:col-span-5">
              <div className="sticky top-24">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                  className="bg-gradient-to-br from-white to-yellow-50 rounded-2xl shadow-lg p-4 md:p-6 mb-6 relative overflow-hidden font-['Open_Sans']"
                >
                  {/* Background decoration */}
                  <div className="absolute top-0 right-0 w-1/2 h-1/2 bg-yellow-200 rounded-full opacity-10 blur-3xl"></div>
                  <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-yellow-100 rounded-full opacity-10 blur-3xl"></div>

                  {/* Product badge */}
                  <div className="absolute top-4 left-4 bg-yellow-400 text-white px-3 py-1 rounded-full text-xs font-semibold z-10 font-['Open_Sans']">
                    {getActiveProductData().modelNumber}
                  </div>

                  {/* Image container with glow effect */}
                  <div className="relative mb-6 mt-4">
                    <div className="absolute inset-0 bg-gradient-to-br from-yellow-200 to-yellow-50 rounded-full opacity-30 blur-xl transform scale-90"></div>
                    <motion.div
                      animate={{ y: [0, -10, 0] }}
                      transition={{ repeat: Infinity, duration: 3, ease: "easeInOut" }}
                      className="relative z-10 flex justify-center items-center py-6"
                    >
                      <img
                        src={getActiveProductData().image}
                        alt={getActiveProductData().title}
                        className="max-h-48 md:max-h-56 w-auto object-contain drop-shadow-2xl transform transition-transform duration-500 hover:scale-110"
                      />
                    </motion.div>
                  </div>

                  {/* Product details */}
                  <div className="text-center mb-6">
                    <h3 className="text-xl md:text-2xl font-bold text-black mb-3 font-['Open_Sans']">{getActiveProductData().title}</h3>
                    <div className="flex justify-center space-x-3 mb-4">
                      <span className="inline-block bg-yellow-100 text-yellow-700 px-3 py-1 rounded-full text-sm font-medium font-['Open_Sans']">
                        {getActiveProductData().features[0]}
                      </span>
                    </div>
                  </div>

                  {/* Specs cards */}
                  <div className="grid grid-cols-2 gap-3 mb-6">
                    <div className="bg-white p-3 rounded-xl shadow-sm border border-yellow-100 transform transition-transform duration-300 hover:-translate-y-1 hover:shadow-md">
                      <div className="flex items-center mb-2">
                        <Gauge className="h-4 w-4 md:h-5 md:w-5 text-yellow-400 mr-2" />
                        <span className="text-xs md:text-sm font-medium text-gray-500 font-['Open_Sans']">Display</span>
                      </div>
                      <span className="font-semibold text-black text-sm md:text-base font-['Open_Sans']">{getActiveProductData().displayInfo}</span>
                    </div>
                    <div className="bg-white p-3 rounded-xl shadow-sm border border-yellow-100 transform transition-transform duration-300 hover:-translate-y-1 hover:shadow-md">
                      <div className="flex items-center mb-2">
                        <Zap className="h-4 w-4 md:h-5 md:w-5 text-yellow-400 mr-2" />
                        <span className="text-xs md:text-sm font-medium text-gray-500 font-['Open_Sans']">Protection</span>
                      </div>
                      <span className="font-semibold text-black text-sm md:text-base font-['Open_Sans']">
                        {activeProductType === "basic" || activeProductType === "ca_advanced" || activeProductType === "leakage" ? "IP 54" : "IP 67"}
                      </span>
                    </div>
                  </div>

                  {/* View Brochure button */}
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      className="w-full bg-gradient-to-r from-yellow-400 to-yellow-400 hover:from-yellow-500 hover:to-yellow-500 text-black font-semibold rounded-lg shadow-md transition-all duration-300 flex items-center justify-center py-2 md:py-3 text-sm md:text-base font-['Open_Sans']"
                      onClick={handleViewBrochure}
                    >
                      <span>View Product Brochure</span>
                      <FileText className="ml-2 h-4 w-4 md:h-5 md:w-5" />
                    </Button>
                  </motion.div>
                </motion.div>
              </div>
            </div>

            <div className="md:col-span-7">
              {/* Enhanced Detail Tabs Navigation */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="bg-white rounded-xl shadow-lg mb-6 overflow-hidden font-['Open_Sans']"
              >
                <div className="flex border-b overflow-x-auto scrollbar-hide">
                  {[
                    { id: "features", label: "Features", icon: <Shield className="h-4 sm:h-5 w-4 sm:w-5" /> },
                    { id: "measurements", label: "Measurements", icon: <Gauge className="h-4 sm:h-5 w-4 sm:w-5" /> },
                    { id: "comparison", label: "Comparison", icon: <BarChart className="h-4 sm:h-5 w-4 sm:w-5" /> }
                  ].map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => {
                        setActiveDetailTab(tab.id);
                        navigate(`?tab=details&product=${activeProductType}&detailTab=${tab.id}`, { replace: true });
                      }}
                      className={`px-4 md:px-6 py-3 md:py-4 font-medium whitespace-nowrap flex items-center transition-all duration-300 text-sm md:text-base font-['Open_Sans'] ${
                        activeDetailTab === tab.id
                          ? "bg-yellow-50 border-b-2 border-yellow-400 text-yellow-700"
                          : "text-gray-700 hover:text-yellow-600 hover:bg-yellow-50/50"
                      }`}
                    >
                      <span className="mr-2">{tab.icon}</span>
                      {tab.label}
                    </button>
                  ))}
                </div>
              </motion.div>

              {/* Tab Content with enhanced styling */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="transform origin-top"
              >
                {activeDetailTab === "comparison" ? (
                  <ComparisonTable productType={activeProductType} />
                ) : (
                  <ProductTabContent activeProductType={activeProductType} activeTab={activeDetailTab} />
                )}
              </motion.div>
            </div>
          </div>

          {/* Enhanced Contact Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
            className="mt-12"
          >
            <ContactSection onContactClick={handleRequestDemo} />
          </motion.div>
        </div>
      )}

      {activeTab === "applications" && (
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6 md:py-8">
          <div className="text-center mb-8 md:mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4 font-['Open_Sans']">Applications</h1>
              <p className="text-base md:text-lg text-gray-800 max-w-4xl mx-auto text-center font-['Open_Sans']">
                KRYKARD digital multimeters are versatile instruments designed for a wide range of professional applications
              </p>
            </motion.div>
          </div>

          <ApplicationsSection />

          <div className="mt-8 md:mt-12 bg-white rounded-2xl shadow-lg p-4 md:p-6">
            <h2 className="text-xl md:text-2xl font-bold text-gray-900 mb-4 md:mb-6 text-center font-['Open_Sans']">Industry-Specific Solutions</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Building Maintenance</h3>
                  <p className="text-gray-800 font-medium">
                    Monitor electrical systems, troubleshoot issues, and ensure safety compliance in commercial buildings and facilities management.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Manufacturing</h3>
                  <p className="text-gray-800 font-medium">
                    Quality control, equipment testing, and preventive maintenance for production lines and automation systems.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Educational Institutions</h3>
                  <p className="text-gray-800 font-medium">
                    Laboratory testing, student training, and practical demonstrations in technical and engineering education.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V4a1 1 0 011-1h2a1 1 0 011 1v14a1 1 0 01-1 1h-2a1 1 0 01-1-1z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Automotive Industry</h3>
                  <p className="text-gray-800 font-medium">
                    Vehicle electrical systems diagnostics, battery testing, and electronic component troubleshooting.
                  </p>
                </div>
              </motion.div>
            </div>

            <div className="flex justify-center mt-6 md:mt-8">
              <Button
                className="px-4 md:px-6 py-2 md:py-3 bg-yellow-400 hover:bg-yellow-500 text-black font-semibold rounded-lg shadow-md transition duration-300 flex items-center space-x-2 text-sm md:text-base font-['Open_Sans']"
                onClick={handleViewBrochure}
              >
                <span>View Brochure</span>
                <FileText className="ml-2 h-4 w-4 md:h-5 md:w-5" />
              </Button>
            </div>
          </div>

          <div className="mt-8 md:mt-12">
            <ContactSection onContactClick={handleRequestDemo} />
          </div>
        </div>
      )}

      {/* No PDF Viewer Modal - Using direct link instead */}
    </PageLayout>
  );
};

export default DigitalMultimeters;